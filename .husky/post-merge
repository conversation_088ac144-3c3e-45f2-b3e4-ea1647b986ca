#!/bin/sh

# 自动化 post-merge 钩子
# 1. 检测依赖变化并自动安装 (pnpm-lock.yaml)
# 2. 智能检测代码变化，为相关应用拉取 env，统一拉取 config
# 3. 从任何目录运行都能无缝工作

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取当前工作目录和仓库根目录
current_dir=$(pwd)
repo_root=$(git rev-parse --show-toplevel 2>/dev/null || echo "$current_dir")

# === 常量定义 ===
readonly ENV_SCRIPT_PATTERN='"env"[[:space:]]*:'

# === 1. 依赖管理和工作区检查 (仓库根目录) ===
dependency_changed=false
work_done=false

# 检查 pnpm-lock.yaml 是否发生变化 (with robust error handling)
if git diff --name-only HEAD@{1} HEAD 2>/dev/null | grep -q "pnpm-lock.yaml"; then
  dependency_changed=true
  echo "${BLUE}📦 检测到依赖变化 (pnpm-lock.yaml)${NC}"

  # 切换到仓库根目录执行安装
  cd "$repo_root" || exit 1

  echo "${YELLOW}⚡ 正在安装依赖...${NC}"
  if pnpm install --frozen-lockfile --prefer-offline 2>/dev/null; then
    echo "${GREEN}✅ 依赖安装完成${NC}"
    work_done=true
  else
    echo "${RED}❌ 依赖安装失败 - 请手动运行 'pnpm install'${NC}"
  fi

  # 返回原目录
  cd "$current_dir" || exit 1
fi

# === 2. 环境变量和配置管理 (env 按应用拉取，config 统一拉取) ===

# 检测哪些应用/包发生了变化
detect_changed_apps_and_packages() {
  local changed_files
  local changed_apps=()
  local changed_packages=()
  local affects_all=false

  # 获取变化文件，处理 git 边界情况
  if ! changed_files=$(git diff --name-only HEAD@{1} HEAD 2>/dev/null); then
    # Fallback: 可能是首次提交或其他边界情况
    changed_files=$(git show --name-only --format="" HEAD 2>/dev/null || echo "")
  fi

  # 如果没有变化，直接返回
  if [ -z "$changed_files" ]; then
    echo "false||"
    return
  fi

  # 一次性检查全局影响文件
  if echo "$changed_files" | grep -qE '^(package\.json|pnpm-workspace\.yaml|turbo\.json)'; then
    affects_all=true
  fi

  # 检测变化的应用 (只有在 apps 目录存在时)
  if [ -d "$repo_root/apps" ]; then
    for app_dir in "$repo_root/apps"/*; do
      if [ -d "$app_dir" ]; then
        local app_name=$(basename "$app_dir")
        if echo "$changed_files" | grep -q "^apps/$app_name/"; then
          changed_apps+=("$app_name")
        fi
      fi
    done
  fi

  # 检测变化的包 (只有在 packages 目录存在时)
  if [ -d "$repo_root/packages" ]; then
    for pkg_dir in "$repo_root/packages"/*; do
      if [ -d "$pkg_dir" ]; then
        local pkg_name=$(basename "$pkg_dir")
        if echo "$changed_files" | grep -q "^packages/$pkg_name/"; then
          changed_packages+=("$pkg_name")
        fi
      fi
    done
  fi

  # 安全的返回格式
  echo "$affects_all|${changed_apps[*]}|${changed_packages[*]}"
}

# 自动发现有 env 脚本的应用（在变化的应用中）
discover_apps_with_scripts() {
  local target_apps=("$@")
  local apps_with_env=()

  # 早期返回：如果没有目标应用
  if [ ${#target_apps[@]} -eq 0 ]; then
    echo ""
    return
  fi

  for app_name in "${target_apps[@]}"; do
    local package_json="$repo_root/apps/$app_name/package.json"

    if [ -f "$package_json" ]; then
      # 只检查 env 脚本是否存在
      if grep -q "$ENV_SCRIPT_PATTERN" "$package_json" 2>/dev/null; then
        apps_with_env+=("$app_name")
      fi
    fi
  done

  # 返回 env 应用列表
  echo "${apps_with_env[*]}"
}

# 为单个应用运行 env/config 脚本
run_app_scripts() {
  local app_name="$1"
  local is_current_app="$2"

  # 基本输入验证
  if [ -z "$app_name" ]; then
    echo "${RED}❌ 错误: 应用名称不能为空${NC}"
    return 1
  fi

  local app_dir="$repo_root/apps/$app_name"

  # 检查应用目录是否存在
  if [ ! -d "$app_dir" ]; then
    echo "${RED}❌ 错误: 应用目录不存在: $app_dir${NC}"
    return 1
  fi

  # 检查是否有相关脚本，如果没有则跳过
  local package_json="$app_dir/package.json"
  if [ ! -f "$package_json" ]; then
    echo "${YELLOW}⚠️  $app_name: package.json 不存在，跳过${NC}"
    return 0
  fi

  local has_env_script=false

  if grep -q "$ENV_SCRIPT_PATTERN" "$package_json" 2>/dev/null; then
    has_env_script=true
  fi

  # 如果没有 env 脚本，跳过处理
  if [ "$has_env_script" = false ]; then
    return 0
  fi

  # 显示处理信息
  if [ "$is_current_app" = true ]; then
    echo "${BLUE}🎯 处理当前应用: $app_name (代码有变化)${NC}"
  else
    echo "${BLUE}🔧 处理应用: $app_name (代码有变化)${NC}"
  fi

  # 切换到应用目录并执行脚本
  (
    cd "$app_dir" || exit 1

    # 运行 env 脚本
    if pnpm run env 2>/dev/null; then
      :  # 成功时不显示消息，env 脚本自己会处理输出
    else
      echo "${RED}⚠️  警告: $app_name 环境变量拉取失败${NC}"
      echo "${YELLOW}💡 可手动运行: cd apps/$app_name && pnpm run env${NC}"
    fi
  )
}

# 检测变化并决定需要处理的应用
change_info=$(detect_changed_apps_and_packages)
IFS='|' read -ra change_arrays <<< "$change_info"
affects_all="${change_arrays[0]}"
changed_apps=(${change_arrays[1]})
changed_packages=(${change_arrays[2]})

# 确定当前应用（如果在 apps 子目录中）
current_app=""
if [[ "$current_dir" == */apps/* ]]; then
  current_app=$(basename "$current_dir")
fi

# 决定需要处理的应用
apps_to_process=()

if [ "$affects_all" = "true" ]; then
  # 全局变化：处理所有有 env/config 的应用
  echo "${YELLOW}🌍 检测到全局配置变化，处理所有应用${NC}"
  for app_dir in "$repo_root/apps"/*; do
    if [ -d "$app_dir" ]; then
      apps_to_process+=($(basename "$app_dir"))
    fi
  done
elif [ ${#changed_apps[@]} -gt 0 ] || [ ${#changed_packages[@]} -gt 0 ]; then
  # 特定变化：只处理变化的应用
  echo "${BLUE}🎯 检测到代码变化${NC}"

  if [ ${#changed_apps[@]} -gt 0 ]; then
    echo "${BLUE}  • 变化的应用: ${changed_apps[*]}${NC}"
    apps_to_process+=("${changed_apps[@]}")
  fi

  if [ ${#changed_packages[@]} -gt 0 ]; then
    echo "${BLUE}  • 变化的包: ${changed_packages[*]}${NC}"
    # 包变化可能影响所有应用，但保守起见只处理当前应用（如果有）
    if [ -n "$current_app" ]; then
      echo "${YELLOW}  • 由于包变化，也处理当前应用: $current_app${NC}"
      if [[ ! " ${apps_to_process[@]} " =~ " $current_app " ]]; then
        apps_to_process+=("$current_app")
      fi
    fi
  fi
else
  echo "${GREEN}✨ 无应用/包代码变化，跳过环境变量/配置更新${NC}"
fi

# 获取需要处理 env 的应用
if [ ${#apps_to_process[@]} -gt 0 ]; then
  env_apps_string=$(discover_apps_with_scripts "${apps_to_process[@]}")
  read -ra apps_with_env <<< "$env_apps_string"

  # 处理应用环境变量
  if [ ${#apps_with_env[@]} -gt 0 ]; then
    echo "${BLUE}⚡ 处理 ${#apps_with_env[@]} 个应用的环境变量${NC}"
    work_done=true

    # 优先处理当前应用（如果存在且在列表中）
    if [ -n "$current_app" ] && [[ " ${apps_with_env[@]} " =~ " $current_app " ]]; then
      run_app_scripts "$current_app" true
      echo ""
    fi

    # 处理其他应用
    for app in "${apps_with_env[@]}"; do
      if [ "$app" != "$current_app" ]; then
        run_app_scripts "$app" false
        echo ""
      fi
    done
  fi

  # === 统一拉取配置（一次性操作）===

  # 检查并构建 packages/config 包（如果需要）
  config_dist_path="$repo_root/packages/config/dist"
  if [ ! -d "$config_dist_path" ]; then
    echo "${BLUE}🔧 首次设置：构建 @repo/config 包...${NC}"
    if (cd "$repo_root/packages/config" && pnpm run build 2>/dev/null); then
      echo "${GREEN}   ✓ @repo/config 构建完成${NC}"
    else
      echo "${RED}❌ @repo/config 构建失败${NC}"
      echo "${YELLOW}💡 请手动运行: cd packages/config && pnpm run build${NC}"
    fi
  fi

  echo "${BLUE}🔄 拉取配置并生成类型...${NC}"
  if (cd "$repo_root" && node devops/scripts/doppler-pull-config.js 2>/dev/null); then
    echo "${GREEN}✅ 配置更新完成${NC}"
    work_done=true
  else
    echo "${YELLOW}⚠️  配置更新失败，请手动运行: node devops/scripts/doppler-pull-config.js${NC}"
  fi
fi

# === 3. 总结和后续建议 ===
if [ "$work_done" = true ]; then
  echo ""
  echo "${GREEN}╭─────────────────────────────────────────╮${NC}"
  echo "${GREEN}│  🎉 更新完成！ Happy coding! 🚀         │${NC}"
  echo "${GREEN}╰─────────────────────────────────────────╯${NC}"
fi
