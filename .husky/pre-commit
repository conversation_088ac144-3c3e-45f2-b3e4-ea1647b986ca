#!/bin/sh

# 在提交前运行代码检查和格式化 (仅检查暂存文件)
# Run code checks and formatting before commit (staged files only)

echo "🔍 Running pre-commit checks on staged files..."

# 使用 Biome 内置的 --staged 支持
# Use Biome's built-in --staged support
echo "🔧 Formatting and fixing staged files with Biome..."

# 获取当前暂存的文件列表
# Get list of currently staged files
STAGED_FILES=$(git diff --staged --name-only --diff-filter=ACMR)

# 首先尝试自动修复所有问题（包括警告），如果失败则继续
# First try to auto-fix all issues (including warnings), continue if it fails
echo "🔧 Attempting to auto-fix issues..."
pnpm biome check --write --staged --files-ignore-unknown=true --no-errors-on-unmatched > /dev/null 2>&1 || echo "⚠️  Auto-fix had some issues, continuing with lint check..."

# 重新添加修复后的文件到暂存区
# Re-stage the fixed files
if [ -n "$STAGED_FILES" ]; then
  echo "$STAGED_FILES" | xargs git add
fi

# 然后只检查错误级别问题，显示详细错误信息
# Then check only error-level issues with detailed error information
echo "🔍 Checking for error-level issues..."
if ! pnpm biome check --staged --files-ignore-unknown=true --no-errors-on-unmatched --diagnostic-level=error; then
  echo ""
  echo "❌ Found error-level issues that must be fixed before committing."
  echo "Please review the detailed error information above and fix the issues."
  exit 1
fi

echo "✅ Pre-commit checks passed!"
