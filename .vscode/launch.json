{"version": "0.2.0", "configurations": [{"name": "Debug youapi", "type": "node", "request": "launch", "program": "${workspaceFolder}/devops/scripts/with-doppler", "args": ["pnpm", "exec", "nest", "start", "you<PERSON>i", "--debug", "--watch"], "cwd": "${workspaceFolder}/apps/youapi", "console": "integratedTerminal", "env": {"NODE_ENV": "development", "YOUMIND_ENV": "preview", "NODE_OPTIONS": "--experimental-require-module"}, "restart": true, "skipFiles": ["<node_internals>/**"]}]}