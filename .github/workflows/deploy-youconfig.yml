name: Deploy YouConfig Worker

# YouConfig Cloudflare Worker 自动部署
# 处理 Doppler 配置同步到 Cloudflare KV，支持多环境部署

on:
  push:
    branches: [main, preview]
    paths:
      - 'devops/workers/youconfig/**'
      - '.github/workflows/deploy-youconfig.yml'

jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy YouConfig Worker

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Install dependencies
        run: |
          # Install youconfig and its workspace dependencies (including @repo/config)
          # Include devDependencies for build tools like TypeScript and type definitions
          # Skip prepare scripts to avoid husky and other dev-only setup scripts
          pnpm install --filter youconfig... --frozen-lockfile --ignore-scripts

      - name: Determine deployment environment
        id: env
        run: |
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "env=production" >> $GITHUB_OUTPUT
            echo "🚀 Deploying to production environment"
          else
            echo "env=preview" >> $GITHUB_OUTPUT
            echo "🔍 Deploying to preview environment"
          fi

      - name: Install Wrangler CLI
        run: pnpm add -g wrangler@latest

      - name: Build YouConfig and dependencies
        run: |
          # Build youconfig and all its dependencies (including @repo/config)
          pnpm --filter youconfig... build

      - name: Deploy with Wrangler CLI
        working-directory: devops/workers/youconfig
        run: |
          # Set secrets
          echo "${{ secrets.DOPPLER_TOKEN }}" | wrangler secret put DOPPLER_TOKEN --env ${{ steps.env.outputs.env }}
          echo "${{ secrets.DOPPLER_WEBHOOK_SECRET }}" | wrangler secret put DOPPLER_WEBHOOK_SECRET --env ${{ steps.env.outputs.env }}

          # Deploy the worker
          wrangler deploy --env ${{ steps.env.outputs.env }}
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
