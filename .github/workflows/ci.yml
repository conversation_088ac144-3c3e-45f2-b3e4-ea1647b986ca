name: <PERSON><PERSON> - <PERSON><PERSON> and <PERSON> Check

on:
  pull_request:
    branches: [main]
  push:
    branches: [main]

concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

jobs:
  code-quality:
    name: <PERSON><PERSON> and <PERSON> Check
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Create .npmrc from Doppler secret
        run: echo "${{ secrets.NPM_RC_CONTENT }}" > .npmrc

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build packages
        run: pnpm run build:packages:local

      - name: Lint check
        run: pnpm run lint

      - name: Format check
        run: pnpm run format:check

      # - name: Type check
      #   run: pnpm run typecheck
