name: Deploy YouAPI to AWS ECS

on:
  push:
    branches: [main, preview]
    paths:
      - "apps/youapi/**"
      - ".github/workflows/deploy-youapi.yml"
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'preview'
        type: choice
        options:
          - preview
          - prod

# 并发控制：取消正在进行的部署
concurrency:
  group: deploy-youapi-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  id-token: write # OIDC for AWS

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    # 设置超时防止挂起
    timeout-minutes: 20
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      DO_NOT_TRACK: ${{ secrets.DO_NOT_TRACK }}
      # 优化 Node.js 内存使用
      NODE_OPTIONS: "--max-old-space-size=4096"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # 浅克隆减少下载时间
          fetch-depth: 1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"

      - name: Fetch App secrets from <PERSON><PERSON><PERSON>
        uses: dopplerhq/secrets-fetch-action@v1.3.0
        id: doppler
        with:
          doppler-token: ${{ secrets.DOPPLER_TOKEN }}
          doppler-project: youniverse-youapi
          doppler-config: ${{ github.event_name == 'workflow_dispatch' && (github.event.inputs.environment == 'prod' && 'prod' || 'pre') || (github.ref_name == 'main' && 'prod' || 'pre') }}
          inject-env-vars: true

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_DEPLOY_ROLE_ARN }}
          role-session-name: GitHubActionsDeploy
          aws-region: us-west-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Create .npmrc from Doppler secret
        run: echo "${{ secrets.NPM_RC_CONTENT }}" > .npmrc

      - name: Build, tag, and push image to ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_REPO_NAME: youapi
          IMAGE_TAG: ${{ github.sha }}
          YOUMIND_ENV: ${{ github.event_name == 'workflow_dispatch' && (github.event.inputs.environment == 'prod' && 'production' || 'preview') || (github.ref_name == 'main' && 'production' || 'preview') }}
        run: |
          docker build -f apps/youapi/Dockerfile \
            --build-arg YOUMIND_ENV=$YOUMIND_ENV \
            -t $ECR_REGISTRY/$IMAGE_REPO_NAME:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$IMAGE_REPO_NAME:$IMAGE_TAG

      - name: Prepare task definition with secrets
        id: task-def
        run: |
          ENV_SUFFIX=${{ github.event_name == 'workflow_dispatch' && (github.event.inputs.environment == 'prod' && 'prod' || 'preview') || (github.ref_name == 'main' && 'prod' || 'preview') }}

          # Start with base task definition
          cp apps/youapi/ecs/task-definition.json task-definition.json

          # Update log group based on environment
          jq --arg lg "/ecs/youapi-${ENV_SUFFIX}" \
             '.containerDefinitions[0].logConfiguration.options."awslogs-group" = $lg' \
             task-definition.json > tmp.json && mv tmp.json task-definition.json

          # Add environment variables from Doppler
          echo '${{ toJson(steps.doppler.outputs) }}' | \
            jq 'to_entries |
                map(select(.key != "DOPPLER_PROJECT" and .key != "DOPPLER_ENVIRONMENT" and .key != "DOPPLER_CONFIG")) |
                map({name: .key, value: .value})' > env-vars.json

          jq --slurpfile envs env-vars.json \
             '.containerDefinitions[0].environment += $envs[0]' \
             task-definition.json > tmp.json && mv tmp.json task-definition.json

          # Add YOUMIND_ENV based on branch or manual input
          jq --arg env "${{ github.event_name == 'workflow_dispatch' && (github.event.inputs.environment == 'prod' && 'production' || 'preview') || (github.ref_name == 'main' && 'production' || 'preview') }}" \
             '.containerDefinitions[0].environment += [{name: "YOUMIND_ENV", value: $env}]' \
             task-definition.json > tmp.json && mv tmp.json task-definition.json

          echo "definition-file=task-definition.json" >> $GITHUB_OUTPUT

      - name: Render task definition
        id: render_td
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.definition-file }}
          container-name: youapi
          image: ${{ steps.login-ecr.outputs.registry }}/youapi:${{ github.sha }}

      - name: Deploy Amazon ECS service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        timeout-minutes: 15
        with:
          task-definition: ${{ steps.render_td.outputs.task-definition }}
          service: ${{ github.event_name == 'workflow_dispatch' && (github.event.inputs.environment == 'prod' && 'youapi-prod' || 'youapi-preview') || (github.ref_name == 'main' && 'youapi-prod' || 'youapi-preview') }}
          cluster: youapi
          wait-for-service-stability: true
