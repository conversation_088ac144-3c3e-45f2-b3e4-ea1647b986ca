name: Deploy YouW<PERSON> to Cloudflare Pages

on:
  push:
    branches: [main, preview]
    paths:
      - 'apps/youweb/**'
      - 'packages/**'
      - '.github/workflows/deploy-youweb.yml'

jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Create .npmrc from Doppler secret
        run: echo "${{ secrets.NPM_RC_CONTENT }}" > .npmrc

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: 📦 Install wrangler via pnpm
        run: pnpm add -g wrangler

      - name: Install Turbo globally
        run: pnpm add -g turbo

      - name: Determine deployment environment
        id: env
        run: |
          if [ "${{ github.event_name }}" = "push" ] && [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "env=production" >> $GITHUB_OUTPUT
            echo "branch=main" >> $GITHUB_OUTPUT
            echo "🚀 Deploying to production environment"
          elif [ "${{ github.event_name }}" = "push" ] && [ "${{ github.ref }}" = "refs/heads/preview" ]; then
            echo "env=preview" >> $GITHUB_OUTPUT
            echo "branch=preview" >> $GITHUB_OUTPUT
            echo "🔍 Deploying preview branch to preview environment"
          else
            echo "env=preview" >> $GITHUB_OUTPUT
            echo "branch=pr-${{ github.event.number }}" >> $GITHUB_OUTPUT
            echo "🔍 Deploying PR to preview environment"
          fi

      - name: Build YouWeb and dependencies for Cloudflare
        run: turbo build:cf --filter=youweb
        env:
          YOUMIND_ENV: ${{ steps.env.outputs.env }}
          NODE_ENV: production

      - name: Deploy to Cloudflare Pages
        run: |
          cd apps/youweb
          wrangler pages deploy dist --project-name=youweb --branch=${{ steps.env.outputs.branch }}
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          # 确保 Pages Functions 能访问正确的环境配置
          CLOUDFLARE_ENVIRONMENT: ${{ steps.env.outputs.env }}

      - name: Deployment Summary
        run: |
          echo "🚀 YouWeb deployed successfully!"
          echo "Environment: ${{ steps.env.outputs.env }}"
          echo "Branch: ${{ steps.env.outputs.branch }}"
          echo ""
          if [ "${{ steps.env.outputs.env }}" = "production" ]; then
            echo "📍 Access via: em2025.youmind.com/boards"
            echo "📍 Direct URL: Check Pages deployment for .pages.dev domain"
          else
            echo "📍 Access via: em2025-preview.youmind.com/boards"
            echo "📍 Direct URL: Check Pages deployment for preview .pages.dev domain"
          fi
          echo ""
          echo "🎯 Routes: /boards, /snips, /thoughts"
