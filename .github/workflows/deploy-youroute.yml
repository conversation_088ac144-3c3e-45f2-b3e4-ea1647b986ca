name: Deploy YouRoute Worker

# YouRoute Cloudflare Worker 自动部署
# 处理多应用路由分发，支持环境变量管理和多环境部署

on:
  push:
    branches: [main, preview]
    paths:
      - 'devops/workers/youroute/**'
      - '.github/workflows/deploy-youroute.yml'

jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy YouRoute Worker

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Install dependencies
        run: |
          # Install youroute and its workspace dependencies (including @repo/server-common)
          # Include devDependencies for build tools like TypeScript and type definitions
          # Skip prepare scripts to avoid husky and other dev-only setup scripts
          pnpm install --filter youroute... --frozen-lockfile --ignore-scripts

      - name: Determine deployment environment
        id: env
        run: |
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "env=production" >> $GITHUB_OUTPUT
            echo "🚀 Deploying to production environment"
          else
            echo "env=preview" >> $GITHUB_OUTPUT
            echo "🔍 Deploying to preview environment"
          fi

      - name: Set environment-specific secrets
        id: secrets
        run: |
          if [ "${{ steps.env.outputs.env }}" = "production" ]; then
            echo "supabase_url=${{ secrets.PROD_NEXT_PUBLIC_SUPABASE_URL }}" >> $GITHUB_OUTPUT
            echo "supabase_anon_key=${{ secrets.PROD_NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> $GITHUB_OUTPUT
            echo "supabase_jwt_secret=${{ secrets.PROD_SUPABASE_JWT_SECRET }}" >> $GITHUB_OUTPUT
            echo "📋 Using production Supabase configuration"
          else
            echo "supabase_url=${{ secrets.PRE_NEXT_PUBLIC_SUPABASE_URL }}" >> $GITHUB_OUTPUT
            echo "supabase_anon_key=${{ secrets.PRE_NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> $GITHUB_OUTPUT
            echo "supabase_jwt_secret=${{ secrets.PRE_SUPABASE_JWT_SECRET }}" >> $GITHUB_OUTPUT
            echo "📋 Using preview Supabase configuration"
          fi

      - name: Install Wrangler CLI
        run: pnpm add -g wrangler@latest

      - name: Build YouRoute and dependencies
        run: |
          # Build youroute and all its dependencies (including @repo/server-common)
          pnpm --filter youroute... build

      - name: Deploy with Wrangler CLI
        working-directory: devops/workers/youroute
        run: |
          # Set secrets, 鉴权使用
          echo "${{ steps.secrets.outputs.supabase_url }}" | wrangler secret put NEXT_PUBLIC_SUPABASE_URL --env ${{ steps.env.outputs.env }}
          echo "${{ steps.secrets.outputs.supabase_anon_key }}" | wrangler secret put NEXT_PUBLIC_SUPABASE_ANON_KEY --env ${{ steps.env.outputs.env }}
          echo "${{ steps.secrets.outputs.supabase_jwt_secret }}" | wrangler secret put SUPABASE_JWT_SECRET --env ${{ steps.env.outputs.env }}

          # Deploy the worker
          wrangler deploy --env ${{ steps.env.outputs.env }}
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
