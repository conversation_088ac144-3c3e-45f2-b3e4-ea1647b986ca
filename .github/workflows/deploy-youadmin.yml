name: Deploy <PERSON>Admin to <PERSON>flare Workers

on:
  push:
    branches: [main, preview]
    paths:
      - 'apps/youadmin/**'
      - '.github/workflows/deploy-youadmin.yml'

# 并发控制：取消正在进行的部署
concurrency:
  group: deploy-youadmin-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy
    # 设置超时防止挂起
    timeout-minutes: 15
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
      DO_NOT_TRACK: ${{ secrets.DO_NOT_TRACK }}
      # 优化 Node.js 内存使用
      NODE_OPTIONS: "--max-old-space-size=4096"

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # 浅克隆减少下载时间
          fetch-depth: 1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Enable Corepack
        run: corepack enable

      - name: Create .npmrc from Doppler secret
        run: echo "${{ secrets.NPM_RC_CONTENT }}" > .npmrc

      - name: Fetch App secrets from <PERSON><PERSON><PERSON>
        uses: dopplerhq/secrets-fetch-action@v1.3.0
        id: doppler
        with:
          doppler-token: ${{ secrets.DOPPLER_TOKEN }}
          doppler-project: youniverse-youadmin
          doppler-config: ${{ github.ref_name == 'main' && 'prod' || 'pre' }}
          inject-env-vars: true

      - name: Determine deployment environment
        id: env
        run: |
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "env=production" >> $GITHUB_OUTPUT
            echo "🚀 Deploying to production environment"
          else
            echo "env=preview" >> $GITHUB_OUTPUT
            echo "🔍 Deploying to preview environment"
          fi

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_OUTPUT

      # 优化的 pnpm 缓存策略
      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      # Turbo 缓存
      - name: Setup Turbo Cache
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      # 只安装 youadmin 及其依赖，大幅减少安装时间
      - name: Install dependencies
        run: pnpm install --filter=youadmin... --frozen-lockfile --prefer-offline

      # 直接使用 pnpm turbo，避免全局安装
      - name: Build YouAdmin and dependencies for Cloudflare
        run: pnpm turbo build:cf --filter=youadmin
        env:
          NODE_ENV: production
          YOUMIND_ENV: ${{ steps.env.outputs.env }}

      # 并行化密钥同步以节省时间
      - name: Sync secrets from Doppler to Cloudflare Workers
        run: |
          set -euo pipefail
          cd apps/youadmin

          # 确保在任何情况下都清理 secrets 文件
          trap 'rm -f secrets.json' EXIT

          # 处理 Doppler 密钥
          echo '${{ toJson(steps.doppler.outputs) }}' | jq 'to_entries | map(select(.key != "DOPPLER_PROJECT" and .key != "DOPPLER_ENVIRONMENT" and .key != "DOPPLER_CONFIG")) | from_entries' > secrets.json

          # 显示将要同步的 secrets 数量（不显示值）
          SECRET_COUNT=$(jq 'length' secrets.json)
          echo "⚡ Syncing $SECRET_COUNT secrets to Cloudflare Workers..."

          # 并行上传所有密钥以节省时间
          npx wrangler secret bulk secrets.json --env ${{ steps.env.outputs.env }} &
          BULK_PID=$!

          # 等待所有后台作业完成
          wait $BULK_PID
          wait

        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

      - name: Deploy to Cloudflare Workers
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          workingDirectory: apps/youadmin
          command: deploy --env ${{ steps.env.outputs.env }}
