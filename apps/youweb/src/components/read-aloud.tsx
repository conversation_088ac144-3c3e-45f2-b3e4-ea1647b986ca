import * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';
import type {
  SnipArticleVO,
  SnipSnippetVO,
  SnipVO,
  SnipVoiceVO,
} from '@repo/common/types/snip/app-types';
import { SnipTypeEnum } from '@repo/common/types/snip/types';
import { LoadingSpinner } from '@repo/ui/components/custom/loading-spinner';
import { AnimatePresence, motion } from 'framer-motion';
import { Check, PlayCircle } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { AudioPlayer } from '@/components/audio-player';
import CircularProgress from '@/components/circular-progress';
import { SpeechIcon } from '@/components/icon/speech';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { useTranslation } from '@/hooks/useTranslation';
import { callHTTP } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';

import { useTrackActions } from './posthog/useTrackActions';

const VOICE_OPTIONS = [
  {
    name: 'alloy',
    gender: 'female',
    avatar: 'https://cdn.gooo.ai/assets/alloy.jpeg',
  },
  {
    name: 'echo',
    gender: 'male',
    avatar: 'https://cdn.gooo.ai/assets/echo.png',
    background: 'rgba(217, 229, 204, 1)',
  },
  {
    name: 'fable',
    gender: 'female',
    avatar: 'https://cdn.gooo.ai/assets/fable.png',
    background: 'rgba(193, 211, 187, 1)',
  },
  {
    name: 'onyx',
    gender: 'male',
    avatar: 'https://cdn.gooo.ai/assets/onyx.jpeg',
  },
  {
    name: 'nova',
    gender: 'female',
    avatar: 'https://cdn.gooo.ai/assets/alloy.jpeg',
  },
  {
    name: 'shimmer',
    gender: 'female',
    avatar: 'https://cdn.gooo.ai/assets/alloy.jpeg',
  },
];

interface ReadAloudProps extends React.HTMLAttributes<HTMLDivElement> {
  snip: SnipVO;
}

enum ReadAloudState {
  IDLE,
  GENERATING,
  GENERATED,
  LOADING,
  LOADED,
  PLAYING,
  PAUSED,
}

export const ReadAloud: React.FC<ReadAloudProps> = ({ className, snip }) => {
  const { t } = useTranslation('AudioPlayer');

  const { id, play_url } = snip as SnipVoiceVO;

  const ref = useRef<HTMLDivElement>(null);
  const [playUrl, setPlayUrl] = useState(play_url || '');
  const [status, setStatus] = useState<ReadAloudState>(
    playUrl ? ReadAloudState.LOADING : ReadAloudState.IDLE,
  );
  const [open, setOpen] = useState(false);
  // play_url: "/files/aihubmix-tts-1-alloy-mp3"
  const [voice, setVoice] = useState(play_url?.split('-')[3] || 'alloy');
  const [sound, setSound] = useState<Howl | null>(null);
  const [duration, setDuration] = useState(0);
  const [progress, setProgress] = useState(0);

  const { trackButtonClick } = useTrackActions();

  useEffect(() => {
    const playUrl = (snip as SnipVoiceVO)?.play_url || '';
    setPlayUrl(playUrl);
    setStatus(playUrl ? ReadAloudState.GENERATED : ReadAloudState.IDLE);
  }, [snip]);

  const updateSnipPlayUrl = async (play_url: string) => {
    const { error } = await callHTTP('/api/v1/snip/updateSnipPlayUrl', {
      method: 'POST',
      body: { id, play_url },
    });
  };

  const generateAudio = async (voice: string) => {
    setStatus(ReadAloudState.GENERATING);

    try {
      const content = getReadAloudContent(snip);
      const { data, error } = await callHTTP('/api/v1/tts', {
        method: 'POST',
        body: {
          input: content,
          voice,
        },
      });

      if (error) {
        throw error;
      }

      const playUrl = data.audio_url;
      if (playUrl) {
        setPlayUrl(playUrl);
        setStatus(ReadAloudState.LOADING);

        await updateSnipPlayUrl(playUrl);
      }
    } catch (_e) {
      setStatus(ReadAloudState.IDLE);
    } finally {
      setStatus(ReadAloudState.GENERATED);
    }
  };

  const handlePlay = () => {
    setOpen(true);
    sound?.play();
  };

  const handlePause = () => {
    setOpen(true);
    sound?.pause();
  };

  const handlePlayerReady = (sound: Howl) => {
    setSound(sound);
    setDuration(sound.duration());
    setStatus(ReadAloudState.LOADED);

    sound.on('pause', () => {
      setStatus(ReadAloudState.PAUSED);
    });
    sound.on('play', () => setStatus(ReadAloudState.PLAYING));
    sound.on('end', () => {
      setStatus(ReadAloudState.PAUSED);
      setProgress(0);
    });
    sound.on('stop', () => {});
  };

  const handleTimeUpdate = (time: number) => {
    setProgress(time);
  };

  useEffect(() => {
    return () => {
      sound?.stop();

      // TODO: save timestamp in extra info
    };
  }, [sound]);

  return status < ReadAloudState.GENERATED ? (
    status === ReadAloudState.GENERATING ? (
      <span className="footnote flex items-center gap-2 text-disabled">
        <LoadingSpinner size={20} />
        <span className="w-18">{t('generating')}</span>
      </span>
    ) : (
      <Button
        variant="ghost"
        className={cn('notranslate h-8 rounded-full px-2 text-muted-foreground', className)}
        onClick={() => {
          generateAudio(voice);
          // 上报埋点
          trackButtonClick('read_aloud_click', {
            snip_id: id,
            voice,
          });
        }}
      >
        <PlayCircle size={20} className="mr-2" />
        <span className="body">{t('listen')}</span>
      </Button>
    )
  ) : (
    <HoverCard open={open} onOpenChange={setOpen} openDelay={0}>
      <HoverCardTrigger asChild>
        <Button
          variant="ghost"
          disabled={status === ReadAloudState.LOADING}
          className={cn('h-8 rounded-full px-2 text-muted-foreground', className)}
          onClick={status === ReadAloudState.PLAYING ? handlePause : handlePlay}
        >
          {status === ReadAloudState.PLAYING ? (
            <CircularProgress value={(progress / duration) * 100} size={20} strokeWidth={2} />
          ) : (
            <PlayCircle size={20} />
          )}
          {status === ReadAloudState.LOADING ? (
            <span className="footnote w-18 ml-2 text-muted-foreground">{t('loading')}</span>
          ) : (
            <AnimatePresence>
              {duration && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="footnote w-18 ml-2 flex text-muted-foreground"
                >
                  <div className="w-8">{convertSecondsToMMSS(progress)}</div>/
                  <div className="w-8">{convertSecondsToMMSS(duration)}</div>
                </motion.div>
              )}
            </AnimatePresence>
          )}
        </Button>
      </HoverCardTrigger>
      <HoverCardContent
        ref={ref}
        className={cn('w-80 rounded-2xl border-none p-0 outline-none', open ? 'visible' : 'hidden')}
        forceMount
      >
        <AudioPlayer
          track={{
            src: playUrl,
          }}
          variant="mini"
          className="h-full"
          onPlayerReady={handlePlayerReady}
          onPlayerTimeUpdate={handleTimeUpdate}
          renderCustomControls={() => (
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 rounded-full text-muted-foreground"
                >
                  <SpeechIcon size={20} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[200px] border-none"
                // container={ref.current}
              >
                <DropdownMenuRadioGroup
                  value={voice}
                  onValueChange={(v) => {
                    if (v !== voice) {
                      setVoice(v);
                      setPlayUrl('');
                      generateAudio(v);
                    }
                  }}
                >
                  {VOICE_OPTIONS.map(({ name, gender, avatar, background }, i) => (
                    <DropdownMenuRadioItem key={i} value={name} className="px-3 py-4">
                      <Avatar className="mr-3 h-10 w-10 object-cover">
                        <AvatarImage
                          src={avatar}
                          alt="voice avatar"
                          className="object-cover"
                          style={{ background }}
                        />
                        <AvatarFallback></AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="body-strong">{name}</p>
                        <p className="muted text-muted-foreground">{gender}</p>
                      </div>
                    </DropdownMenuRadioItem>
                  ))}
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        />
      </HoverCardContent>
    </HoverCard>
  );
};

function getReadAloudContent(snip: SnipVO | null) {
  let content = '';
  switch (snip?.type) {
    case SnipTypeEnum.ARTICLE:
      // prepend title
      content = ((snip as SnipArticleVO).title +
        '\n' +
        (snip as SnipArticleVO).content.raw) as string;
      break;
    case SnipTypeEnum.SNIPPET:
      content = (snip as SnipSnippetVO).content.raw as string;
      break;
    default:
      break;
  }

  const $div = document.createElement('div');
  $div.innerHTML = content;

  return ($div.textContent || '').trim().replace(/\n+/g, ' ').replace(/\s+/g, ' ');
}

function convertSecondsToMMSS(seconds: number) {
  const roundedSeconds = Math.ceil(seconds);
  const minutes = Math.floor(roundedSeconds / 60);
  const remainingSeconds = Math.floor(roundedSeconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

const DropdownMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>
>(({ className, children, ...props }, ref) => (
  <DropdownMenuPrimitive.RadioItem
    ref={ref}
    className={cn(
      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className,
    )}
    {...props}
  >
    {children}
    <span className="absolute right-2 flex h-6 w-6 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
  </DropdownMenuPrimitive.RadioItem>
));
DropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;
