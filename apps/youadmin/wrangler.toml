main = ".open-next/worker.js"
compatibility_date = "2025-07-01"
compatibility_flags = ["nodejs_compat", "global_fetch_strictly_public"]

# Production environment configuration
[env.production]
name = "youadmin-production"

[env.production.assets]
directory = ".open-next/assets"
binding = "ASSETS"

[[env.production.services]]
binding = "WORKER_SELF_REFERENCE"
service = "youadmin-production"

[[env.production.kv_namespaces]]
binding = "CONFIG_KV"
id = "2c7ddc953a334acdad5a7287088e2ef5"

# Preview environment configuration
[env.preview]
name = "youadmin-preview"

[env.preview.assets]
directory = ".open-next/assets"
binding = "ASSETS"

[[env.preview.services]]
binding = "WORKER_SELF_REFERENCE"
service = "youadmin-preview"

[[env.preview.kv_namespaces]]
binding = "CONFIG_KV"
id = "16e5d5a58d484c239d031175be66fc4b"

[observability.logs]
enabled = true
