# YouAdmin

Analytics and administration dashboard for YouMind platform, built with Next.js and designed for Cloudflare deployment.

## Features

- **Analytics Dashboard**: Real-time metrics from Google Analytics and ClickHouse
- **Subscription Analysis**: Detailed breakdown of subscription statuses
- **Content Analytics**: Analysis of snips, thoughts, chats, and boards
- **Platform Agnostic**: Built to run on any platform, not tied to Vercel

## Tech Stack

- **Framework**: Next.js 15 (App Router)
- **UI**: shadcn/ui components with Radix UI primitives
- **Database**: ClickHouse for analytics data
- **Analytics**: Google Analytics Data API
- **Charts**: Recharts for data visualization
- **Styling**: Tailwind CSS

## Getting Started

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Configure your environment variables in `.env.local`:
   - ClickHouse connection details
   - Google Analytics credentials

3. Run the development server:
   ```bash
   pnpm dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## shadcn/ui Components

This project uses shadcn/ui components which are installed via CLI:

```bash
pnpm dlx shadcn@latest add button card table tabs
```

Components are automatically generated in the `components/ui/` directory.

## Environment Variables

Required environment variables:

- `CLICKHOUSE_URL`: ClickHouse database URL
- `CLICKHOUSE_USERNAME`: ClickHouse username
- `CLICKHOUSE_PASSWORD`: ClickHouse password
- `GOOGLE_ANALYTICS_PRIVATE_KEY`: Google Analytics private key
- `GOOGLE_CLIENT_EMAIL`: Google Analytics client email
- `GOOGLE_CLOUD_PROJECT`: Google Cloud project ID
- `GOOGLE_ANALYTICS_TAG_ID`: Google Analytics tracking ID

## Deployment

This application is designed to be platform-agnostic and can be deployed to:

- Cloudflare Pages
- Netlify
- Railway
- Any Node.js hosting platform

For Cloudflare deployment, use the `npm run build` command which outputs a standalone build.

## Architecture

The application follows a clean architecture pattern:

- `/app`: Next.js App Router pages and layouts
- `/components`: Reusable UI components
- `/lib`: Business logic and service layers
- `/lib/clickhouse.ts`: ClickHouse service for analytics queries
- `/lib/google-analytics.ts`: Google Analytics service for web metrics

## Data Sources

1. **ClickHouse**: Contains replicated data from the main PostgreSQL database
   - User spaces and subscription data
   - Content entities (snips, thoughts, chats, boards)
   - Usage records and cost data

2. **Google Analytics**: Web analytics data
   - User engagement metrics
   - Page views and sessions
   - Realtime user data
