// Google Analytics implementation for Cloudflare Workers
// Using HTTP-based Reporting API v4 instead of gRPC-based @google-analytics/data

import { BetaAnalyticsDataClient } from '@google-analytics/data';

const propertyId = '450646367';

// Keep the original client for development environments
let analyticsDataClient: BetaAnalyticsDataClient | null = null;

try {
  analyticsDataClient = new BetaAnalyticsDataClient({
    projectId: 'youmind-ga-*************',
    keyFilename: undefined,
    credentials: {
      client_email:
        '<EMAIL>',
      private_key: process.env.GOOGLE_ANALYTICS_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    },
  });
} catch (error) {
  console.warn(
    'Google Analytics gRPC client failed (Workers environment), falling back to HTTP API',
  );
  analyticsDataClient = null;
}

// Helper function to get Google OAuth2 access token using JWT
async function getGoogleAccessToken(): Promise<string> {
  const serviceAccountEmail =
    '<EMAIL>';
  const privateKey = process.env.GOOGLE_ANALYTICS_PRIVATE_KEY?.replace(/\\n/g, '\n');

  if (!privateKey) {
    throw new Error('GOOGLE_ANALYTICS_PRIVATE_KEY environment variable is required');
  }

  // Create JWT manually for Workers environment
  const header = {
    alg: 'RS256',
    typ: 'JWT',
  };

  const now = Math.floor(Date.now() / 1000);
  const payload = {
    iss: serviceAccountEmail,
    scope: 'https://www.googleapis.com/auth/analytics.readonly',
    aud: 'https://oauth2.googleapis.com/token',
    exp: now + 3600,
    iat: now,
  };

  // Encode header and payload
  const encodedHeader = btoa(JSON.stringify(header))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  const encodedPayload = btoa(JSON.stringify(payload))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  const message = `${encodedHeader}.${encodedPayload}`;

  // Import private key and sign
  const keyData = privateKey
    .replace(/-----BEGIN PRIVATE KEY-----/g, '')
    .replace(/-----END PRIVATE KEY-----/g, '')
    .replace(/\s/g, '');

  const binaryKey = Uint8Array.from(atob(keyData), (c) => c.charCodeAt(0));

  const cryptoKey = await crypto.subtle.importKey(
    'pkcs8',
    binaryKey,
    {
      name: 'RSASSA-PKCS1-v1_5',
      hash: 'SHA-256',
    },
    false,
    ['sign'],
  );

  const signature = await crypto.subtle.sign(
    'RSASSA-PKCS1-v1_5',
    cryptoKey,
    new TextEncoder().encode(message),
  );

  const encodedSignature = btoa(String.fromCharCode(...Array.from(new Uint8Array(signature))))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  const jwt = `${message}.${encodedSignature}`;

  // Exchange JWT for access token
  const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
      assertion: jwt,
    }),
  });

  if (!tokenResponse.ok) {
    const error = await tokenResponse.text();
    throw new Error(`Token request failed: ${tokenResponse.statusText} - ${error}`);
  }

  const tokenData = await tokenResponse.json();
  return tokenData.access_token;
}

// Use Google Analytics Reporting API v4 with HTTP requests
async function getAnalyticsDataViaHTTP() {
  try {
    const accessToken = await getGoogleAccessToken();

    const response = await fetch(
      `https://analyticsdata.googleapis.com/v1beta/properties/${propertyId}:runReport`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dateRanges: [
            {
              startDate: '30daysAgo',
              endDate: 'today',
            },
          ],
          dimensions: [
            { name: 'date' },
            { name: 'signedInWithUserId' },
            { name: 'customUser:subscription_status' },
          ],
          metrics: [{ name: 'totalUsers' }, { name: 'screenPageViews' }],
          orderBys: [
            {
              dimension: {
                dimensionName: 'date',
              },
              desc: false,
            },
          ],
        }),
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Analytics API request failed: ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();

    // Transform to match expected format
    return {
      rows:
        data.rows?.map((row: any) => ({
          dimensionValues: row.dimensionValues || [],
          metricValues: row.metricValues || [],
        })) || [],
    };
  } catch (error) {
    console.error('HTTP Analytics API error:', error);
    throw error; // Don't hide the error - let calling code handle it
  }
}

export async function getUVAndPVFromGA() {
  // Try HTTP API first (works in Workers)
  if (!analyticsDataClient) {
    console.log('Using Google Analytics HTTP API (Workers environment)');
    return await getAnalyticsDataViaHTTP();
  }

  // Fallback to gRPC client (development)
  try {
    console.log('Using Google Analytics gRPC client (development)');
    const [response] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate: '30daysAgo',
          endDate: 'today',
        },
      ],
      dimensions: [
        { name: 'date' },
        { name: 'signedInWithUserId' },
        { name: 'customUser:subscription_status' },
      ],
      metrics: [{ name: 'totalUsers' }, { name: 'screenPageViews' }],
      orderBys: [
        {
          dimension: {
            dimensionName: 'date',
          },
          desc: false,
        },
      ],
    });

    return response;
  } catch (error) {
    console.error('gRPC Analytics API failed, trying HTTP API:', error);
    return await getAnalyticsDataViaHTTP();
  }
}

export class GoogleAnalyticsService {
  private client: BetaAnalyticsDataClient | null;
  private propertyId: string;

  constructor() {
    this.client = analyticsDataClient;
    this.propertyId = `properties/${propertyId}`;
  }

  async getRealtimeData() {
    // For realtime data, we'll need to use a different approach or skip it
    // Realtime API might not be available via HTTP in the same way
    if (!this.client) {
      throw new Error(
        'Realtime data not available in Workers environment. Consider using Cloudflare Analytics instead.',
      );
    }

    try {
      const [response] = await this.client.runRealtimeReport({
        property: this.propertyId,
        metrics: [{ name: 'activeUsers' }, { name: 'screenPageViews' }],
        dimensions: [{ name: 'country' }, { name: 'deviceCategory' }],
      });

      return response;
    } catch (error) {
      console.error('Google Analytics realtime data error:', error);
      throw error;
    }
  }

  async getBasicMetrics(startDate: string = '30daysAgo', endDate: string = 'today') {
    return await getUVAndPVFromGA();
  }

  async getTopPages(startDate: string = '7daysAgo', endDate: string = 'today'): Promise<any> {
    // Implement HTTP-based version for Workers
    if (!this.client) {
      try {
        const accessToken = await getGoogleAccessToken();

        const response = await fetch(
          `https://analyticsdata.googleapis.com/v1beta/properties/${propertyId}:runReport`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dateRanges: [{ startDate, endDate }],
              dimensions: [{ name: 'pagePath' }, { name: 'pageTitle' }],
              metrics: [{ name: 'screenPageViews' }, { name: 'activeUsers' }],
              orderBys: [
                {
                  metric: { metricName: 'screenPageViews' },
                  desc: true,
                },
              ],
              limit: 10,
            }),
          },
        );

        if (!response.ok) {
          throw new Error(`Top pages API request failed: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('HTTP top pages API error:', error);
        throw error;
      }
    }

    try {
      const [response] = await this.client.runReport({
        property: this.propertyId,
        dateRanges: [{ startDate, endDate }],
        metrics: [{ name: 'screenPageViews' }, { name: 'activeUsers' }],
        dimensions: [{ name: 'pagePath' }, { name: 'pageTitle' }],
        orderBys: [
          {
            metric: { metricName: 'screenPageViews' },
            desc: true,
          },
        ],
        limit: 10,
      });

      return response;
    } catch (error) {
      console.error('gRPC top pages error, trying HTTP:', error);
      return await this.getTopPages(startDate, endDate); // Recursive call will use HTTP path
    }
  }

  async getDeviceAndLocationData(
    startDate: string = '7daysAgo',
    endDate: string = 'today',
  ): Promise<any> {
    if (!this.client) {
      try {
        const accessToken = await getGoogleAccessToken();

        const response = await fetch(
          `https://analyticsdata.googleapis.com/v1beta/properties/${propertyId}:runReport`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              dateRanges: [{ startDate, endDate }],
              dimensions: [{ name: 'country' }, { name: 'deviceCategory' }],
              metrics: [{ name: 'activeUsers' }, { name: 'sessions' }],
              orderBys: [
                {
                  metric: { metricName: 'activeUsers' },
                  desc: true,
                },
              ],
            }),
          },
        );

        if (!response.ok) {
          throw new Error(`Device/location API request failed: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('HTTP device/location API error:', error);
        throw error;
      }
    }

    try {
      const [response] = await this.client.runReport({
        property: this.propertyId,
        dateRanges: [{ startDate, endDate }],
        metrics: [{ name: 'activeUsers' }, { name: 'sessions' }],
        dimensions: [{ name: 'country' }, { name: 'deviceCategory' }],
        orderBys: [
          {
            metric: { metricName: 'activeUsers' },
            desc: true,
          },
        ],
      });

      return response;
    } catch (error) {
      console.error('gRPC device/location error, trying HTTP:', error);
      return await this.getDeviceAndLocationData(startDate, endDate);
    }
  }
}

export const googleAnalyticsService = new GoogleAnalyticsService();
