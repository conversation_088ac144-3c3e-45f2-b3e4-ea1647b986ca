import { loadServerConfig } from '@repo/config';
import { tryGetCurrentUserId } from '@repo/server-common';
import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';

interface AuthWrapperProps {
  children: React.ReactNode;
}

/**
 * 认证包装器 - 处理整个应用的认证逻辑
 */
async function AuthWrapper({ children }: AuthWrapperProps) {
  const headerStore = await headers();
  const cookieStore = await cookies();

  // 获取当前用户ID
  const userId = await tryGetCurrentUserId({
    getHeader: (name: string) => headerStore.get(name),
    getCookie: (name: string) => cookieStore.get(name)?.value,
    setCookie: () => {
      // 在服务端只读模式下忽略cookie设置
    },
    removeCookie: () => {
      // 在服务端只读模式下忽略cookie删除
    },
  });

  if (!userId) {
    const authHost = process.env.YOUMIND_HOST;
    const currentUrl = headerStore.get('referer') ?? '/admin/dashboard';
    const signInUrl = `${authHost}/sign-in?next=${encodeURIComponent(currentUrl)}`;
    redirect(signInUrl);
  }

  // 检查管理员权限
  try {
    const config = await loadServerConfig();
    const SYSTEM_ADMINS = config.get('SYSTEM_ADMINS', []);

    if (!SYSTEM_ADMINS.includes(userId)) {
      const authHost = process.env.YOUMIND_HOST;
      redirect(`${authHost}/sign-in?error=insufficient_permissions`);
    }
  } catch (configError) {
    console.error(`[YouAdmin] 配置加载失败: ${configError}`);
  }

  // 认证通过，渲染子组件
  return <>{children}</>;
}

export default AuthWrapper;
