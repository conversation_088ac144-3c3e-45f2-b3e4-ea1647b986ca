'use client';

import { eachDayOfInterval, format, subDays } from 'date-fns';
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { UserTrendsChart } from '@/components/analytics/user-trends-chart';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { apiCall } from '@/lib/utils';

interface MetricItem {
  label: string;
  value: number;
  link?: string;
  change?: string;
  subValue?: number;
  subValueLabel?: string;
  subItems?: Array<{
    value: number;
    label: string;
    link?: string;
  }>;
}

interface MetricGroup {
  sum: {
    label: string;
    value: number;
    link?: string;
  };
  items: MetricItem[];
}

function fillMissingDays(
  spaceData: {
    day: string;
    activate_count: number;
    create_count: number;
  }[],
) {
  const today = new Date();
  const last30Days = eachDayOfInterval({
    start: subDays(today, 30),
    end: today,
  });

  return last30Days.map((date) => {
    const formattedDate = format(date, 'yyyy-MM-dd');
    const existingData = spaceData.find((item) => item.day === formattedDate);

    return (
      existingData || {
        day: formattedDate,
        activate_count: 0,
        create_count: 0,
      }
    );
  });
}

// Using the apiCall utility function from @/lib/utils for proper basePath handling

export default function DashboardPage() {
  const [allCount, setAllCount] = useState(0);
  const [activeMonthlyCount, setActiveMonthlyCount] = useState(0);
  const [trialCount, setTrialCount] = useState(0);
  const [activeCount, setActiveCount] = useState(0);
  const [trialEndedCount, setTrialEndedCount] = useState(0);
  const [subscribeErrorCount, setSubscribeErrorCount] = useState(0);
  const [uninitializedCount, setUninitializedCount] = useState(0);
  const [initializedCount, setInitializedCount] = useState(0);
  const [activatedCount, setActivatedCount] = useState(0);
  const [unknownCount, setUnknownCount] = useState(0);
  const [subscribedAndTrialingCount, setSubscribedAndTrialingCount] = useState(0);
  const [subscribedNotRenewingCount, setSubscribedNotRenewingCount] = useState(0);
  const [activeRenewingCount, setActiveRenewingCount] = useState(0);
  const [activeNotRenewingCount, setActiveNotRenewingCount] = useState(0);
  const [subscribedTrialingRenewingCount, setSubscribedTrialingRenewingCount] = useState(0);
  const [subscribedTrialingNotRenewingCount, setSubscribedTrialingNotRenewingCount] = useState(0);

  const [spaceCount, setSpaceCount] = useState<
    {
      day: string;
      activate_count: number;
      create_count: number;
    }[]
  >([]);
  const [view, setView] = useState('uv');
  const [rawGAData, setRawGAData] = useState<
    {
      date: string;
      isSignedIn?: string;
      subscriptionStatus?: string;
      uv?: number;
      pv?: number;
    }[]
  >([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch user count data from our ClickHouse API
        const userData = await apiCall('/api/dashboard/users', {
          method: 'POST',
        });
        if (userData) {
          const data = userData;
          setAllCount(data.allCount || 0);
          setTrialCount(data.trialCount || 0);
          setActiveCount(data.activeCount || 0);
          setActiveMonthlyCount(data.activeMonthlyCount || 0);
          setTrialEndedCount(data.trialEndedCount || 0);
          setSubscribeErrorCount(data.subscribeErrorCount || 0);
          setUnknownCount(data.unknownCount || 0);
          setSubscribedAndTrialingCount(data.subscribedAndTrialingCount || 0);
          setUninitializedCount(data.uninitializedCount || 0);
          setInitializedCount(data.initializedCount || 0);
          setActivatedCount(data.activatedCount || 0);
          setSubscribedNotRenewingCount(data.subscribedNotRenewingCount || 0);
          setActiveRenewingCount(data.activeRenewingCount || 0);
          setActiveNotRenewingCount(data.activeNotRenewingCount || 0);
          setSubscribedTrialingRenewingCount(data.subscribedTrialingRenewingCount || 0);
          setSubscribedTrialingNotRenewingCount(data.subscribedTrialingNotRenewingCount || 0);
        }

        // Fetch space count data from our ClickHouse API
        const spaceData = await apiCall('/api/dashboard/spaces', {
          method: 'POST',
        });
        if (spaceData) {
          setSpaceCount(fillMissingDays(spaceData));
        }

        // Fetch UV and PV data from our Google Analytics API
        const analyticsData = await apiCall('/api/dashboard/analytics', {
          method: 'POST',
        });
        if (analyticsData) {
          setRawGAData(analyticsData);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set default values for development
        setActivatedCount(100);
        setAllCount(150);
      }
    };
    fetchData();
  }, []);

  const uvAndPV = useMemo(() => {
    // 首先生成30天的基础数据结构
    const today = new Date();
    const last30Days = eachDayOfInterval({
      start: subDays(today, 30),
      end: today,
    });

    // 初始化30天的数据，所有值为0
    const data: {
      day: string;
      notSignedIn: number;
      trialing: number;
      subscribed: number;
      activated: number;
    }[] = last30Days.map((date) => ({
      day: format(date, 'yyyy-MM-dd'),
      notSignedIn: 0,
      trialing: 0,
      subscribed: 0,
      activated: 0,
    }));

    // 用实际的GA数据填充
    rawGAData.forEach((row) => {
      const date = row.date.replace(/^(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
      const isSignedIn = row.isSignedIn === 'yes';
      const uv = row.uv || 0;
      const pv = row.pv || 0;
      const value = view === 'upv' ? (uv > 0 ? pv / uv : 0) : view === 'uv' ? uv : pv;
      const isSubscribed = row.subscriptionStatus === 'active';

      const existingDay = data.find((item) => item.day === date);

      if (existingDay) {
        if (!isSignedIn) {
          existingDay.notSignedIn += value;
        } else if (isSubscribed) {
          existingDay.subscribed += value;
        } else {
          existingDay.trialing += value;
        }
      }
    });

    // 处理激活用户数据（仅在UV模式下）
    if (view === 'uv') {
      data.forEach((item) => {
        const spaceDataForDay = spaceCount.find((spaceItem) => spaceItem.day === item.day);
        const activateCount = spaceDataForDay?.activate_count
          ? parseInt(spaceDataForDay.activate_count.toString(), 10)
          : 0;
        item.trialing = Math.max(0, item.trialing - activateCount);
        item.activated = activateCount;
      });
    }

    return data;
  }, [rawGAData, view, spaceCount]);

  const metricGroups: MetricGroup[] = [
    {
      sum: { label: '总用户', value: allCount, link: '/admin/users' },
      items: [
        {
          label: '注册未完成',
          value: unknownCount,
          link: '/admin/users?status=unknown',
        },
        {
          label: '未初始化',
          value: uninitializedCount,
          link: '/admin/users?status=uninitialized',
        },
        {
          label: '未激活试用',
          value: initializedCount,
          link: '/admin/users?status=initialized',
        },
        {
          label: '已激活',
          value: activatedCount,
        },
      ],
    },
    {
      sum: {
        label: '已激活',
        value: activatedCount,
      },
      items: [
        {
          label: '试用中',
          value: trialCount,
          link: '/admin/users?status=trialing',
        },
        {
          label: '已订阅未扣费',
          value: subscribedAndTrialingCount,
          link: '/admin/users?status=subscribed&subscription_status=trialing',
          subItems: [
            {
              value: subscribedTrialingRenewingCount,
              label: '将扣费',
              link: '/admin/users?status=subscribed&subscription_status=trialing&cancel_at_period_end=false',
            },
            {
              value: subscribedTrialingNotRenewingCount,
              label: '取消扣费',
              link: '/admin/users?status=subscribed&subscription_status=trialing&cancel_at_period_end=true',
            },
          ],
        },
        {
          label: '已订阅并扣费',
          value: activeCount,
          link: '/admin/users?status=subscribed&subscription_status=active',
          change:
            activatedCount > 0 ? `${((activeCount / activatedCount) * 100).toFixed(2)}%` : '0%',
          subItems: [
            {
              value: activeRenewingCount,
              label: '续订',
              link: '/admin/users?status=subscribed&subscription_status=active&cancel_at_period_end=false',
            },
            {
              value: activeNotRenewingCount,
              label: '不续订',
              link: '/admin/users?status=subscribed&subscription_status=active&cancel_at_period_end=true',
            },
          ],
          subValue: activeMonthlyCount,
          subValueLabel: '其中 $20 月付',
        },
        {
          label: '已订阅不续订',
          value: subscribedNotRenewingCount,
          link: '/admin/users?status=subscribed&cancel_at_period_end=true',
        },
        {
          label: '试用到期',
          value: trialEndedCount,
          link: '/admin/users?status=inactive',
        },
        {
          label: '订阅异常',
          value: subscribeErrorCount,
          link: '/admin/users?subscription_status=incomplete_expired,incomplete,canceled,past_due,unpaid',
        },
        {
          label: '其他',
          value:
            activatedCount -
            trialCount -
            subscribedAndTrialingCount -
            activeCount -
            subscribedNotRenewingCount -
            trialEndedCount -
            subscribeErrorCount,
        },
      ],
    },
  ];

  const router = useRouter();

  return (
    <div className="w-full overflow-x-hidden p-3 2xl:p-6">
      {metricGroups.map((group, groupIndex) => (
        <div key={groupIndex} className="mb-4">
          <div className="flex flex-col gap-3 2xl:gap-4">
            <div className="flex items-center gap-1 2xl:gap-2">
              <div
                className={`flex-1 rounded-lg border border-gray-100 bg-white p-4 shadow-sm 2xl:p-6 ${
                  group.sum.link ? 'cursor-pointer hover:border-gray-300' : ''
                }`}
                onClick={() => group.sum.link && router.push(group.sum.link)}
              >
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-500">{group.sum.label}</p>
                </div>
                <div className="mt-2 flex items-baseline">
                  <p className="text-xl font-semibold text-gray-800 2xl:text-2xl">
                    {group.sum.value}
                  </p>
                </div>
              </div>
              <div className="text-xl font-semibold text-gray-400">=</div>
              {group.items.map((metric, index) => (
                <React.Fragment key={index}>
                  <div
                    className={`rounded-lg border border-gray-100 bg-white p-4 shadow-sm 2xl:flex-1 2xl:p-6 ${
                      metric.link ? 'cursor-pointer hover:border-gray-300' : ''
                    } ${group.items.length > 5 ? 'flex-auto' : 'flex-1'}`}
                    onClick={() => metric.link && router.push(metric.link)}
                  >
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-500">{metric.label}</p>
                      {metric.change && (
                        <span className="text-xs text-gray-500 2xl:text-sm">{metric.change}</span>
                      )}
                    </div>
                    <div className="mt-2 flex items-baseline">
                      <p className="text-xl font-semibold text-gray-800 2xl:text-2xl">
                        {metric.value}
                      </p>
                      {metric.subValue !== undefined && (
                        <span className="ml-2 text-xs text-gray-500 2xl:text-sm">
                          ({metric.subValueLabel}: {metric.subValue})
                        </span>
                      )}
                    </div>
                    {metric.subItems && (
                      <div className="mt-2 flex flex-col gap-1">
                        {metric.subItems.map((subItem, subIndex) => (
                          <div
                            key={subIndex}
                            className={`flex items-center justify-between text-xs ${
                              subItem.link ? 'cursor-pointer hover:text-blue-600' : ''
                            }`}
                            onClick={(e) => {
                              e.stopPropagation();
                              subItem.link && router.push(subItem.link);
                            }}
                          >
                            <span className="text-gray-600">{subItem.label}</span>
                            <span className="font-medium text-gray-800">{subItem.value}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  {index < group.items.length - 1 && (
                    <div className="text-xl font-semibold text-gray-400">+</div>
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>
      ))}

      <div className="mt-4">
        <div className="rounded-lg border border-gray-100 bg-white p-4 shadow-sm 2xl:p-6">
          <div className="flex flex-col justify-between gap-2 2xl:flex-row 2xl:items-center 2xl:gap-0">
            <h3 className="flex flex-wrap items-center text-base font-semibold 2xl:text-lg">
              近 30 日浏览访问趋势
              <span className="ml-2 text-xs font-normal text-gray-500 2xl:text-sm">
                (数据来源：Google Analytics)
              </span>
              <RadioGroup
                value={view}
                onValueChange={(value) => setView(value)}
                orientation="horizontal"
                className="ml-4 inline-flex"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="uv" id="r1" />
                  <Label htmlFor="r1">UV</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="pv" id="r2" />
                  <Label htmlFor="r2">PV</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="upv" id="r3" />
                  <Label htmlFor="r3">UPV</Label>
                </div>
              </RadioGroup>
            </h3>
            <a
              href="https://analytics.google.com/analytics/web/#/p450646367/reports/intelligenthome"
              target="_blank"
              className="text-xs text-blue-500 2xl:text-sm"
              rel="noopener"
            >
              Google Analytics
            </a>
          </div>
          <UserTrendsChart data={uvAndPV} view={view as 'uv' | 'pv' | 'upv'} />
        </div>
      </div>
    </div>
  );
}
