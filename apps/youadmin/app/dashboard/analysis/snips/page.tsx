import { EntityAnalyticsChart } from '@/components/analytics/entity-analytics-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { clickhouseService } from '@/lib/clickhouse';

async function getSnipsData() {
  const [analysis, domainAnalysis, recentActivity] = await Promise.all([
    clickhouseService.getSnipsAnalysis(30),
    clickhouseService.getSnipsDomainAnalysis(),
    clickhouseService.query(`
      SELECT
        toDate(created_at) as date,
        count() as snips_created,
        countDistinct(space_id) as unique_spaces,
        countDistinct(user_id) as unique_users
      FROM snips
      WHERE created_at >= now() - INTERVAL 7 DAY
      GROUP BY date
      ORDER BY date DESC
    `),
  ]);

  return { analysis, domainAnalysis, recentActivity };
}

export default async function SnipsAnalysisPage() {
  const { analysis, domainAnalysis, recentActivity } = await getSnipsData();

  const totalSnips = analysis.reduce((sum, day) => sum + day.snips_created, 0);
  const avgDaily = totalSnips / 30;
  const uniqueSpaces = Math.max(...analysis.map((day) => day.unique_spaces));

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Snips Analysis</h1>
        <p className="text-muted-foreground">
          Analytics for snips creation and domain distribution
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Snips (30d)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSnips.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Last 30 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daily Average</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgDaily.toFixed(0)}</div>
            <p className="text-xs text-muted-foreground">Snips per day</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Spaces</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{uniqueSpaces.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Creating snips</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Domains</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{domainAnalysis.length}</div>
            <p className="text-xs text-muted-foreground">Unique domains</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Daily Snips Creation</CardTitle>
            <CardDescription>Snips created over the last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <EntityAnalyticsChart
              data={analysis.reverse()}
              dataKey="snips_created"
              title="Snips Created"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity (7 days)</CardTitle>
            <CardDescription>Daily breakdown of recent snips activity</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Snips</TableHead>
                  <TableHead>Spaces</TableHead>
                  <TableHead>Users</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentActivity.map((day: any, index: number) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      {new Date(day.date).toLocaleDateString()}
                    </TableCell>
                    <TableCell>{day.snips_created.toLocaleString()}</TableCell>
                    <TableCell>{day.unique_spaces.toLocaleString()}</TableCell>
                    <TableCell>{day.unique_users.toLocaleString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Top Domains</CardTitle>
          <CardDescription>Most popular domains for snip creation</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Domain</TableHead>
                <TableHead>Count</TableHead>
                <TableHead>Percentage</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {domainAnalysis.slice(0, 10).map((domain: any, index: number) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{domain.domain}</TableCell>
                  <TableCell>{domain.count.toLocaleString()}</TableCell>
                  <TableCell>{((domain.count / totalSnips) * 100).toFixed(1)}%</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
