import { EntityAnalyticsChart } from '@/components/analytics/entity-analytics-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { clickhouseService } from '@/lib/clickhouse';

async function getBoardsData() {
  const [analysis, recentActivity] = await Promise.all([
    clickhouseService.getBoardsAnalysis(30),
    clickhouseService.query(`
      SELECT
        toDate(created_at) as date,
        count() as boards_created,
        countDistinct(space_id) as unique_spaces,
        countDistinct(user_id) as unique_users
      FROM boards
      WHERE created_at >= now() - INTERVAL 7 DAY
      GROUP BY date
      ORDER BY date DESC
    `),
  ]);

  return { analysis, recentActivity };
}

export default async function BoardsAnalysisPage() {
  const { analysis, recentActivity } = await getBoardsData();

  const totalBoards = analysis.reduce((sum, day) => sum + day.boards_created, 0);
  const avgDaily = totalBoards / 30;
  const uniqueSpaces = Math.max(...analysis.map((day) => day.unique_spaces));

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Boards Analysis</h1>
        <p className="text-muted-foreground">Analytics for board creation and user engagement</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Boards (30d)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalBoards.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Last 30 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daily Average</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgDaily.toFixed(0)}</div>
            <p className="text-xs text-muted-foreground">Boards per day</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Spaces</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{uniqueSpaces.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Creating boards</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Weekly Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {recentActivity.reduce((sum: number, day: any) => sum + day.boards_created, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Last 7 days</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Daily Boards Creation</CardTitle>
            <CardDescription>Boards created over the last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <EntityAnalyticsChart
              data={analysis.reverse()}
              dataKey="boards_created"
              title="Boards Created"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity (7 days)</CardTitle>
            <CardDescription>Daily breakdown of recent boards activity</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Boards</TableHead>
                  <TableHead>Spaces</TableHead>
                  <TableHead>Users</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentActivity.map((day: any, index: number) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      {new Date(day.date).toLocaleDateString()}
                    </TableCell>
                    <TableCell>{day.boards_created.toLocaleString()}</TableCell>
                    <TableCell>{day.unique_spaces.toLocaleString()}</TableCell>
                    <TableCell>{day.unique_users.toLocaleString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
