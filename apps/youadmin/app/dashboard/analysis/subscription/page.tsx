import { SubscriptionStatsChart } from '@/components/analytics/subscription-stats-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { clickhouseService } from '@/lib/clickhouse';

async function getSubscriptionData() {
  const [stats, detailedStats] = await Promise.all([
    clickhouseService.getSubscriptionStats(),
    clickhouseService.query(`
      SELECT
        status,
        count() as count,
        countDistinct(user_id) as unique_users,
        avg(created_at) as avg_creation_date
      FROM spaces
      WHERE status IS NOT NULL
      GROUP BY status
      ORDER BY count DESC
    `),
  ]);

  return { stats, detailedStats };
}

export default async function SubscriptionAnalysisPage() {
  const { stats, detailedStats } = await getSubscriptionData();

  const totalSpaces = stats.reduce((sum, stat) => sum + stat.count, 0);
  const totalUsers = detailedStats.reduce((sum: number, stat: any) => sum + stat.unique_users, 0);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Subscription Analysis</h1>
        <p className="text-muted-foreground">
          Detailed breakdown of subscription statuses and user distribution
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spaces</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSpaces.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Across all subscription types</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unique Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Users with spaces</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(
                ((stats.find((s) => s.status === 'active')?.count || 0) / totalSpaces) *
                100
              ).toFixed(1)}
              %
            </div>
            <p className="text-xs text-muted-foreground">Spaces with active subscriptions</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Subscription Distribution</CardTitle>
            <CardDescription>Visual breakdown of subscription statuses</CardDescription>
          </CardHeader>
          <CardContent>
            <SubscriptionStatsChart data={stats} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Detailed Statistics</CardTitle>
            <CardDescription>Comprehensive subscription status breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Status</TableHead>
                  <TableHead>Spaces</TableHead>
                  <TableHead>Users</TableHead>
                  <TableHead>Percentage</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {detailedStats.map((stat: any, index: number) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium capitalize">
                      {stat.status || 'Unknown'}
                    </TableCell>
                    <TableCell>{stat.count.toLocaleString()}</TableCell>
                    <TableCell>{stat.unique_users.toLocaleString()}</TableCell>
                    <TableCell>{((stat.count / totalSpaces) * 100).toFixed(1)}%</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
