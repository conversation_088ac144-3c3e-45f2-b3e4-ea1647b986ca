import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import AuthWrapper from './auth-wrapper';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'YouMind Admin',
  description: 'YouMind Analytics and Administration Dashboard',
};

// 强制动态渲染，禁用静态生成
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <AuthWrapper>
          <div className="min-h-screen bg-background">{children}</div>
        </AuthWrapper>
      </body>
    </html>
  );
}
