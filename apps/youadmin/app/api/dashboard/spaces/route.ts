import { NextRequest, NextResponse } from 'next/server';
import { clickhouseService } from '@/lib/clickhouse';

export async function POST(_request: NextRequest) {
  try {
    // Replicate the original space metrics logic using ClickHouse
    // This mimics the original PostgreSQL query with generate_series and LEFT JOINs
    const query = `
      WITH date_series AS (
        SELECT toDate(now() - toIntervalDay(number)) as day
        FROM numbers(31)
        WHERE day >= today() - toIntervalDay(30)
      ),
      created_spaces AS (
        SELECT
          toDate(created_at) as day,
          count() as create_count
        FROM spaces
        WHERE deleted_at IS NULL
          AND created_at >= now() - toIntervalDay(30)
        GROUP BY toDate(created_at)
      ),
      activated_spaces AS (
        SELECT
          toDate(activated_at) as day,
          count() as activate_count
        FROM spaces
        WHERE deleted_at IS NULL
          AND activated_at IS NOT NULL
          AND activated_at >= now() - toIntervalDay(30)
        GROUP BY toDate(activated_at)
      )
      SELECT
        toString(date_series.day) as day,
        COALESCE(created_spaces.create_count, 0) as create_count,
        COALESCE(activated_spaces.activate_count, 0) as activate_count
      FROM date_series
      LEFT JOIN created_spaces ON date_series.day = created_spaces.day
      LEFT JOIN activated_spaces ON date_series.day = activated_spaces.day
      ORDER BY date_series.day
    `;

    const spaceData = await clickhouseService.query<{
      day: string;
      create_count: string;
      activate_count: string;
    }>(query);

    // Convert ClickHouse string results to proper numeric types
    const typedSpaceData = spaceData.map((row) => ({
      day: row.day,
      create_count: parseInt(row.create_count, 10),
      activate_count: parseInt(row.activate_count, 10),
    }));

    return NextResponse.json(typedSpaceData);
  } catch (error) {
    console.error('Error fetching space dashboard data:', error);
    return NextResponse.json({ error: 'Failed to fetch space data' }, { status: 500 });
  }
}
