import { type NextRequest, NextResponse } from 'next/server';
import { getUVAndPVFromGA } from '@/lib/google-analytics';

export async function POST(_request: NextRequest) {
  try {
    // Use the exact same function as youapp
    const data = await getUVAndPVFromGA();
    const result =
      data?.rows?.map((row: any) => ({
        date: row.dimensionValues?.[0].value || '',
        isSignedIn: row.dimensionValues?.[1].value || '',
        subscriptionStatus: row.dimensionValues?.[2].value || '',
        uv: parseInt(row.metricValues?.[0].value || '0', 10),
        pv: parseInt(row.metricValues?.[1].value || '0', 10),
      })) || [];

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching analytics dashboard data:', error);
    // Return empty data instead of error to allow dashboard to work without GA
    return NextResponse.json([]);
  }
}
