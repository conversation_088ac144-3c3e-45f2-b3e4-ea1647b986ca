import { type NextRequest, NextResponse } from 'next/server';
import { clickhouseService } from '@/lib/clickhouse';

export async function POST(_request: NextRequest) {
  try {
    // Replicate the original user count logic using ClickHouse
    // This query mimics the original LEFT JOIN between users and spaces
    const query = `
      SELECT
        COUNT(*) as count,
        subscription_status,
        status,
        subscription_price,
        subscription_cancel_at_period_end
      FROM spaces
      WHERE deleted_at IS NULL
      GROUP BY
        subscription_status,
        status,
        subscription_price,
        subscription_cancel_at_period_end
    `;

    const rawData = await clickhouseService.query<{
      count: string;
      subscription_status: string | null;
      status: string | null;
      subscription_price: string | null;
      subscription_cancel_at_period_end: string | null;
    }>(query);

    // Convert ClickHouse string results to proper types
    const typedData = rawData.map((row) => ({
      count: parseInt(row.count, 10),
      subscription_status: row.subscription_status,
      status: row.status,
      subscription_price: row.subscription_price,
      subscription_cancel_at_period_end: row.subscription_cancel_at_period_end === 'true',
    }));

    // Calculate metrics using the exact original logic with properly typed data
    const allCount = typedData.reduce((sum, row) => sum + row.count, 0);

    // Users with no space (registration incomplete)
    const unknownCount = typedData
      .filter((row) => row.status === null)
      .reduce((sum, row) => sum + row.count, 0);

    // Users with uninitialized status
    const uninitializedCount = typedData
      .filter((row) => row.status === 'uninitialized')
      .reduce((sum, row) => sum + row.count, 0);

    // Users with initialized status (trial not activated)
    const initializedCount = typedData
      .filter((row) => row.status === 'initialized')
      .reduce((sum, row) => sum + row.count, 0);

    // Users in trial status
    const trialCount = typedData
      .filter((row) => row.status === 'trialing' || row.status === 'trial_expiring')
      .reduce((sum, row) => sum + row.count, 0);

    // Users with subscribed status and trialing subscription_status
    const subscribedAndTrialingCount = typedData
      .filter((row) => row.status === 'subscribed' && row.subscription_status === 'trialing')
      .reduce((sum, row) => sum + row.count, 0);

    // Subscribed trialing users who will renew
    const subscribedTrialingRenewingCount = typedData
      .filter(
        (row) =>
          row.status === 'subscribed' &&
          row.subscription_status === 'trialing' &&
          row.subscription_cancel_at_period_end !== true,
      )
      .reduce((sum, row) => sum + row.count, 0);

    // Subscribed trialing users who will cancel
    const subscribedTrialingNotRenewingCount = typedData
      .filter(
        (row) =>
          row.status === 'subscribed' &&
          row.subscription_status === 'trialing' &&
          row.subscription_cancel_at_period_end === true,
      )
      .reduce((sum, row) => sum + row.count, 0);

    // Users with subscribed status and active subscription_status
    const activeCount = typedData
      .filter((row) => row.status === 'subscribed' && row.subscription_status === 'active')
      .reduce((sum, row) => sum + row.count, 0);

    // Active users who will renew
    const activeRenewingCount = typedData
      .filter(
        (row) =>
          row.status === 'subscribed' &&
          row.subscription_status === 'active' &&
          row.subscription_cancel_at_period_end !== true,
      )
      .reduce((sum, row) => sum + row.count, 0);

    // Active users who will not renew
    const activeNotRenewingCount = typedData
      .filter(
        (row) =>
          row.status === 'subscribed' &&
          row.subscription_status === 'active' &&
          row.subscription_cancel_at_period_end === true,
      )
      .reduce((sum, row) => sum + row.count, 0);

    // Active users with monthly subscription
    const activeMonthlyCount = typedData
      .filter(
        (row) =>
          row.status === 'subscribed' &&
          row.subscription_status === 'active' &&
          row.subscription_price === 'youmind-pro-monthly',
      )
      .reduce((sum, row) => sum + row.count, 0);

    // Users with inactive status (trial ended)
    const trialEndedCount = typedData
      .filter((row) => row.status === 'inactive')
      .reduce((sum, row) => sum + row.count, 0);

    // Users with subscribed status but subscription error
    const subscribeErrorCount = typedData
      .filter(
        (row) =>
          row.status === 'subscribed' &&
          row.subscription_status !== 'active' &&
          row.subscription_status !== 'trialing',
      )
      .reduce((sum, row) => sum + row.count, 0);

    // Users with subscribed status and cancel_at_period_end = true
    const subscribedNotRenewingCount = typedData
      .filter(
        (row) => row.status === 'subscribed' && row.subscription_cancel_at_period_end === true,
      )
      .reduce((sum, row) => sum + row.count, 0);

    // Activated users = all users minus unknown, uninitialized, and initialized
    const activatedCount = allCount - unknownCount - uninitializedCount - initializedCount;

    const result = {
      allCount,
      unknownCount,
      uninitializedCount,
      initializedCount,
      activatedCount,
      trialCount,
      subscribedAndTrialingCount,
      subscribedTrialingRenewingCount,
      subscribedTrialingNotRenewingCount,
      activeCount,
      activeRenewingCount,
      activeNotRenewingCount,
      activeMonthlyCount,
      trialEndedCount,
      subscribeErrorCount,
      subscribedNotRenewingCount,
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching user dashboard data:', error);
    return NextResponse.json({ error: 'Failed to fetch user data' }, { status: 500 });
  }
}
