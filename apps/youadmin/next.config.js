/** @type {import('next').NextConfig} */
const nextConfig = {
  basePath: '/admin',
  // 强制所有页面动态渲染，禁用静态生成
  output: 'standalone',
  experimental: {
    serverActions: {
      bodySizeLimit: '10mb',
    },
  },
  eslint: {
    dirs: ['app', 'components', 'lib'],
  },
  poweredByHeader: false,
  // 禁用图片优化（减少构建时间，适合 admin 面板）
  images: {
    unoptimized: true,
  },
};

module.exports = nextConfig;

// Initialize OpenNext for local development
if (process.env.NODE_ENV === 'development') {
  try {
    const { initOpenNextCloudflareForDev } = require('@opennextjs/cloudflare');
    initOpenNextCloudflareForDev();
  } catch (error) {
    console.warn('OpenNext Cloudflare dev initialization failed:', error.message);
  }
}
