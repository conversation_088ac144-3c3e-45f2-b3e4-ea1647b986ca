'use client';

import { format, parseISO } from 'date-fns';
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

interface UsageData {
  date: string;
  total_usage: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_cost: number;
}

interface UsageChartProps {
  data: UsageData[];
}

export function UsageChart({ data }: UsageChartProps) {
  const chartData = data
    .map((item) => ({
      ...item,
      formattedDate: format(parseISO(item.date), 'MMM dd'),
    }))
    .reverse(); // Reverse to show chronological order

  return (
    <ResponsiveContainer width="100%" height={350}>
      <ComposedChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="formattedDate" fontSize={12} tickLine={false} axisLine={false} />
        <YAxis yAxisId="left" fontSize={12} tickLine={false} axisLine={false} />
        <YAxis
          yAxisId="right"
          orientation="right"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        <Tooltip
          content={({ active, payload, label }) => {
            if (active && payload && payload.length) {
              return (
                <div className="rounded-lg border bg-background p-2 shadow-sm">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex flex-col">
                      <span className="text-[0.70rem] uppercase text-muted-foreground">Date</span>
                      <span className="font-bold text-muted-foreground">{label}</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-[0.70rem] uppercase text-muted-foreground">Usage</span>
                      <span className="font-bold">
                        {payload.find((p) => p.dataKey === 'total_usage')?.value?.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-[0.70rem] uppercase text-muted-foreground">Cost</span>
                      <span className="font-bold">
                        $
                        {parseFloat(
                          (payload.find((p) => p.dataKey === 'total_cost')?.value as string) || '0',
                        ).toFixed(2)}
                      </span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-[0.70rem] uppercase text-muted-foreground">Tokens</span>
                      <span className="font-bold">
                        {(
                          parseInt(
                            (payload.find((p) => p.dataKey === 'total_input_tokens')
                              ?.value as string) || '0',
                          ) +
                          parseInt(
                            (payload.find((p) => p.dataKey === 'total_output_tokens')
                              ?.value as string) || '0',
                          )
                        ).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              );
            }
            return null;
          }}
        />
        <Bar yAxisId="left" dataKey="total_usage" fill="#8884d8" name="Usage" />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="total_cost"
          stroke="#ff7300"
          strokeWidth={2}
          dot={false}
          name="Cost ($)"
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
}
