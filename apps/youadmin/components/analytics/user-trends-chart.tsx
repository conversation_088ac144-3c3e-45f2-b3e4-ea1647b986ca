'use client';

import { format } from 'date-fns';
import { useState } from 'react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  LabelList,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

interface ChartData {
  day: string;
  notSignedIn: number;
  trialing: number;
  subscribed: number;
  activated: number;
}

interface UserTrendsChartProps {
  data: ChartData[];
  view: 'uv' | 'pv' | 'upv';
}

export function UserTrendsChart({ data, view }: UserTrendsChartProps) {
  // 数据系列可见性状态
  const [visibleSeries, setVisibleSeries] = useState({
    notSignedIn: true,
    trialing: true,
    subscribed: true,
    activated: view === 'uv', // 仅在UV模式下默认显示
  });

  // 处理数据以适配 Recharts 格式
  const chartData = data.map((item) => ({
    ...item,
    formattedDate: format(new Date(item.day), 'MM-dd'),
    total:
      view === 'upv'
        ? Number(
            ((item.notSignedIn + item.trialing + item.subscribed + item.activated) / 4).toFixed(1),
          )
        : item.notSignedIn + item.trialing + item.subscribed + item.activated,
  }));

  // 切换数据系列可见性
  const toggleSeries = (seriesKey: keyof typeof visibleSeries) => {
    setVisibleSeries((prev) => ({
      ...prev,
      [seriesKey]: !prev[seriesKey],
    }));
  };

  // 使用 index 访问原始数据的标签函数
  const createLabelRenderer = (dataKey: string) => {
    const LabelRenderer = (props: any) => {
      const { x, y, width, height, index } = props;

      if (index === undefined || !chartData[index]) return null;

      const actualValue = chartData[index][dataKey as keyof ChartData] as number;

      // 只有当值大于5且高度大于8px时才显示标签
      if (!actualValue || actualValue < 5 || height < 8) return null;

      return (
        <text
          x={x + width / 2}
          y={y + height / 2}
          fill="#fff"
          textAnchor="middle"
          dy={3}
          fontSize={8}
          fontWeight="bold"
        >
          {view === 'upv' ? Number(actualValue).toFixed(1) : Math.round(actualValue)}
        </text>
      );
    };

    LabelRenderer.displayName = `LabelRenderer_${dataKey}`;
    return LabelRenderer;
  };

  // 自定义 Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="pointer-events-none rounded-md bg-gray-800 p-2 text-[10px] text-white shadow-lg 2xl:text-xs">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500" />
              <span>订阅用户: {view === 'upv' ? data.subscribed.toFixed(1) : data.subscribed}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-sky-400" />
              <span>试用用户: {view === 'upv' ? data.trialing.toFixed(1) : data.trialing}</span>
            </div>
            {view === 'uv' && (
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-amber-400" />
                <span>注册用户: {data.activated}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-slate-400" />
              <span>游客: {view === 'upv' ? data.notSignedIn.toFixed(1) : data.notSignedIn}</span>
            </div>
            <div className="mt-1 border-t border-gray-600 pt-1">
              <span>
                {view === 'upv' ? '平均: ' : '总计: '}
                {data.total}
              </span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="h-[400px] w-full">
      {/* 图表容器 - 为图例预留空间 */}
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 16,
            right: 20,
            left: 20,
            bottom: 8,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="formattedDate"
            fontSize={10}
            tickLine={false}
            axisLine={false}
            className="text-gray-500"
          />
          <YAxis fontSize={10} tickLine={false} axisLine={false} className="text-gray-500" />
          <Tooltip content={<CustomTooltip />} />

          {/* 按照视觉顺序渲染条形图 - 从下到上 */}
          {visibleSeries.notSignedIn && (
            <Bar dataKey="notSignedIn" stackId="users" fill="#94a3b8" stroke="none">
              <LabelList content={createLabelRenderer('notSignedIn')} />
            </Bar>
          )}
          {view === 'uv' && visibleSeries.activated && (
            <Bar dataKey="activated" stackId="users" fill="#fbbf24" stroke="none">
              <LabelList content={createLabelRenderer('activated')} />
            </Bar>
          )}
          {visibleSeries.trialing && (
            <Bar dataKey="trialing" stackId="users" fill="#38bdf8" stroke="none">
              <LabelList content={createLabelRenderer('trialing')} />
            </Bar>
          )}
          {visibleSeries.subscribed && (
            <Bar dataKey="subscribed" stackId="users" fill="#10b981" stroke="none">
              <LabelList content={createLabelRenderer('subscribed')} />
            </Bar>
          )}
        </BarChart>
      </ResponsiveContainer>

      {/* 可点击图例 - 固定在底部 */}
      <div className="flex items-center justify-center space-x-4 text-xs 2xl:text-sm">
        <div
          className={`flex cursor-pointer items-center transition-opacity hover:opacity-80 ${
            !visibleSeries.notSignedIn ? 'opacity-50' : ''
          }`}
          onClick={() => toggleSeries('notSignedIn')}
        >
          <div
            className={`mr-2 h-3 w-3 ${visibleSeries.notSignedIn ? 'bg-slate-400' : 'bg-gray-300 border border-slate-400'}`}
          />
          <span>游客</span>
        </div>
        {view === 'uv' && (
          <div
            className={`flex cursor-pointer items-center transition-opacity hover:opacity-80 ${
              !visibleSeries.activated ? 'opacity-50' : ''
            }`}
            onClick={() => toggleSeries('activated')}
          >
            <div
              className={`mr-2 h-3 w-3 ${visibleSeries.activated ? 'bg-amber-400' : 'bg-gray-300 border border-amber-400'}`}
            />
            <span>注册用户</span>
          </div>
        )}
        <div
          className={`flex cursor-pointer items-center transition-opacity hover:opacity-80 ${
            !visibleSeries.trialing ? 'opacity-50' : ''
          }`}
          onClick={() => toggleSeries('trialing')}
        >
          <div
            className={`mr-2 h-3 w-3 ${visibleSeries.trialing ? 'bg-sky-400' : 'bg-gray-300 border border-sky-400'}`}
          />
          <span>试用用户</span>
        </div>
        <div
          className={`flex cursor-pointer items-center transition-opacity hover:opacity-80 ${
            !visibleSeries.subscribed ? 'opacity-50' : ''
          }`}
          onClick={() => toggleSeries('subscribed')}
        >
          <div
            className={`mr-2 h-3 w-3 ${visibleSeries.subscribed ? 'bg-green-500' : 'bg-gray-300 border border-green-500'}`}
          />
          <span>订阅用户</span>
        </div>
      </div>
    </div>
  );
}
