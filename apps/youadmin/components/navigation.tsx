'use client';

import { BarChart3, Brain, Database, FileText, Home, Layers, MessageSquare } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

const navigationItems = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: Home,
  },
  {
    title: 'Analysis',
    href: '/dashboard/analysis',
    icon: BarChart3,
    children: [
      {
        title: 'Subscription',
        href: '/dashboard/analysis/subscription',
      },
      {
        title: 'Snips',
        href: '/dashboard/analysis/snips',
      },
      {
        title: 'Thoughts',
        href: '/dashboard/analysis/thoughts',
      },
      {
        title: 'Chats',
        href: '/dashboard/analysis/chats',
      },
      {
        title: 'Boards',
        href: '/dashboard/analysis/boards',
      },
    ],
  },
];

export function Navigation() {
  const pathname = usePathname();

  return (
    <nav className="space-y-2">
      {navigationItems.map((item) => (
        <div key={item.href}>
          <Link
            href={item.href}
            className={cn(
              'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
              pathname === item.href
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted',
            )}
          >
            <item.icon className="h-4 w-4" />
            {item.title}
          </Link>
          {item.children && (
            <div className="ml-6 space-y-1 mt-2">
              {item.children.map((child) => (
                <Link
                  key={child.href}
                  href={child.href}
                  className={cn(
                    'block rounded-md px-3 py-2 text-sm transition-colors',
                    pathname === child.href
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-muted',
                  )}
                >
                  {child.title}
                </Link>
              ))}
            </div>
          )}
        </div>
      ))}
    </nav>
  );
}
