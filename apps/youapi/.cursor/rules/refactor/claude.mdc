---
description:
globs:
alwaysApply: true
---
# IAM 模块 API 迁移方案

基于 material-mng 模块的成功迁移经验，制定以下 IAM (Identity and Access Management) 模块的迁移方案。本方案参考已完成的 material-mng 模块迁移模式，适用于 IAM 模块内用户、权限、认证等聚合的迁移。

## 架构原则和设计风格

### 1. 分层架构设计
- **Controller 层**: 处理 HTTP 请求，使用 CQRS 模式
- **Service 层**: 应用服务，包含 Commands/Queries/Handlers
- **Domain 层**: 领域实体、值对象、领域事件
- **Repository 层**: 数据访问抽象，使用聚合根

### 2. 模块组织原则
- iam 模块代表身份认证和访问管理的限界上下文
- 按聚合组织，每个聚合有独立的 Service、Repository
- 使用 NestJS 的 Module 系统进行依赖注入

### 3. 路由一致性原则
- **聚合独立性**：保持聚合的业务边界清晰，user、user-preference、space、subscription 为独立聚合
- **路由一致性**：迁移前后保持相同的路由路径，确保客户端不需要修改
- **版本保持**：使用与 youapp 一致的 `/api/v1` 路由前缀，避免客户端改动
- **实现方式**：
    - 使用统一的 Controller 作为路由入口，保持 `/api/v1` 前缀
    - Controller 内部调用不同聚合的服务
    - 保持聚合的封装性，不暴露内部结构给客户端

### 4. 架构重构要求
- **重构现有代码**: 基于已迁移的 IAM 模块代码进行 DDD + CQRS 架构重构
- **舍弃**: 现有的传统 Service 层架构和对 youapp domain service 的直接依赖
- **重构**: 从数据库表颗粒度的 DAO 层重构到聚合颗粒度的 Repository 层
- **新增**: CQRS 模式的 Commands/Queries/Handlers 和 ApplicationService 层
- **解耦依赖**: 逐步解除对 youapp 的 xxxDomain、xxxDao 等对象的直接依赖，实现架构独立
- **保持兼容**: 确保重构后的 API 与原有 youapp API 完全兼容，路由路径保持不变
- **IMPORTANT - 数据库 Schema 迁移**: 表结构请迁移到 ~/Projects/youniverse/apps/youapi/src/shared/db/public.schema.ts，代码中字段统一改为 camelCase 风格，数据库中仍然保持 snake_case 风格。原有的 snake_case 风格 schema 文件位于 @src/dao/db/public.schema.ts 需要迁移到新的 camelCase 风格

### 5. API 设计风格
- **非 RESTful 风格**: 遵循 material-mng 模块的成功模式，所有 API 端点统一使用 POST 方法
- **命名约定**: 路由路径包含具体的操作名称，如 `POST /api/v1/signIn`, `POST /api/v1/user/createUser` 等
- **参数传递**: 所有参数通过请求体传递，不使用 URL 参数或查询字符串
- **响应格式**: 统一的 JSON 响应格式
- **中文 API 文档**: 使用中文的 summary 和 description
- **统一状态码**: 使用 `@HttpCode(200)` 统一返回 200 状态码

### 6. API 命名风格迁移
- **原有风格**: youapp 使用 snake_case 风格的 API 参数和响应
- **新风格**: youapi 统一使用 camelCase 风格
- **兼容性处理**: 使用 SerializeInterceptor 和 `x-use-snake-case` 请求头实现向后兼容
    - 客户端发送 `x-use-snake-case: true` 时，API 返回 snake_case 格式
    - 默认情况下，API 返回 camelCase 格式
    - 内部代码统一使用 camelCase 风格
    - 数据库字段保持 snake_case 风格
    - SerializeInterceptor 只处理响应数据格式，不处理请求参数

## 迁移流程

### 步骤 1: 需求分析和设计
1. **分析原有 API 接口**
    - 梳理 youapp 中相关的 API 端点
    - 分析业务逻辑和数据流
    - 确定聚合边界和依赖关系

2. **设计新的聚合结构**
    - 识别聚合根和实体
    - 设计值对象和领域事件
    - 定义聚合间的交互方式

### 步骤 2: 测试驱动开发
1. **编写集成测试**
    - 基于现有 youapp API 编写完整的测试用例
    - 覆盖所有 API 端点和业务场景
    - 包含边界条件和错误处理测试

2. **验证 API 兼容性**
    - 确保新 API 与原有 API 行为一致
    - 验证请求/响应格式的正确性（包括 snake_case/camelCase 兼容性）
    - 测试各种参数组合和边界情况
    - 验证 `x-use-snake-case` 请求头的兼容性处理

### 步骤 3: 实现迁移
1. **创建核心组件**
    - 领域实体 (AggregateRoot)
    - 领域事件
    - Repository 接口和实现
    - CQRS Commands/Queries/Handlers
    - DTO 和 Controller

### 步骤 4: 测试和验证
1. **运行集成测试**
    - 使用 `pnpm test:xxx-integration` 运行测试
    - 确保测试代码中解决了身份认证，参考 material-mng 模块的测试代码
    - 确保所有测试用例通过
    - 验证数据完整性和业务逻辑正确性

2. **对比测试**
    - youapp 已经运行在 3000 端口
    - 可以用后台运行命令 `nohup pnpm dev > youapi.log 2>&1 &` 将 youapi 运行在 4000 端口，当发现端口冲突，可以先杀进程再重启
    - 身份认证方式参考 src/modules/material-mng/tests/youapp-note-api.integration.spec.ts
    - 对比两个 API 的响应结果
    - 确保迁移后的行为一致

## 标准目录结构

### IAM 模块标准目录结构

```
src/modules/iam/
├── iam.module.ts                                    # 主模块，统一导出和依赖注入
├── controllers/                                     # 控制器层 - HTTP 请求处理
│   ├── auth.controller.ts                          # 认证相关 API 端点
│   ├── user.controller.ts                          # 用户管理 API 端点
│   ├── user-preference.controller.ts               # 用户偏好 API 端点
│   └── space.controller.ts                         # 工作空间 API 端点
├── domain/                                          # 领域层 - 业务规则和实体
│   ├── user/                                       # User 聚合
│   │   ├── models/
│   │   │   └── user.entity.ts                      # 用户聚合根
│   │   ├── events/
│   │   │   ├── user-created.event.ts               # 用户创建事件
│   │   │   ├── user-updated.event.ts               # 用户更新事件
│   │   │   └── ...                                 # 其他用户事件
│   │   └── types/
│   │       └── user.types.ts                       # 用户相关类型定义
│   ├── user-preference/                            # UserPreference 聚合
│   │   ├── models/
│   │   │   └── user-preference.entity.ts           # 用户偏好聚合根
│   │   ├── events/
│   │   │   ├── user-preference-created.event.ts    # 偏好创建事件
│   │   │   └── ...                                 # 其他偏好事件
│   │   └── types/
│   │       └── user-preference.types.ts            # 偏好相关类型定义
│   └── space/                                       # Space 聚合
│       ├── models/
│       │   └── space.entity.ts                     # 工作空间聚合根
│       ├── events/
│       │   ├── space-created.event.ts              # 空间创建事件
│       │   ├── space-trial-started.event.ts        # 试用开始事件
│       │   └── ...                                 # 其他空间事件
│       └── types/
│           └── space.types.ts                       # 空间相关类型定义
├── repositories/                                    # 仓储层 - 数据访问
│   ├── user.repository.ts                          # 用户数据访问
│   ├── user-preference.repository.ts               # 用户偏好数据访问
│   └── space.repository.ts                         # 工作空间数据访问
├── services/                                        # 应用服务层 - CQRS 实现
│   ├── commands/                                    # 命令定义
│   │   ├── user/
│   │   │   ├── init-current-user.command.ts        # 初始化当前用户命令
│   │   │   ├── update-user-name.command.ts         # 更新用户名命令
│   │   │   ├── ...                                 # 其他用户命令
│   │   │   └── index.ts                            # 命令导出
│   │   ├── user-preference/
│   │   │   ├── create-user-preference.command.ts   # 创建偏好命令
│   │   │   ├── ...                                 # 其他偏好命令
│   │   │   └── index.ts                            # 命令导出
│   │   └── space/
│   │       ├── create-space.command.ts             # 创建空间命令
│   │       ├── start-trial.command.ts              # 开始试用命令
│   │       ├── ...                                 # 其他空间命令
│   │       └── index.ts                            # 命令导出
│   ├── queries/                                     # 查询定义
│   │   ├── user/
│   │   │   ├── get-user-preference.query.ts        # 获取用户偏好查询
│   │   │   ├── ...                                 # 其他用户查询
│   │   │   └── index.ts                            # 查询导出
│   │   ├── user-preference/
│   │   │   ├── get-user-preference.query.ts        # 获取偏好查询
│   │   │   ├── ...                                 # 其他偏好查询
│   │   │   └── index.ts                            # 查询导出
│   │   ├── space/
│   │   │   ├── get-space-by-id.query.ts            # 按ID获取空间查询
│   │   │   ├── get-space-by-user.query.ts          # 按用户获取空间查询
│   │   │   ├── ...                                 # 其他空间查询
│   │   │   └── index.ts                            # 查询导出
│   │   └── auth/
│   │       ├── can-preview.query.ts                # 权限检查查询
│   │       ├── ...                                 # 其他认证查询
│   │       └── index.ts                            # 查询导出
│   └── handlers/                                    # 命令和查询处理器
│       ├── user/
│       │   ├── init-current-user.handler.ts        # 初始化用户处理器
│       │   ├── update-user-name.handler.ts         # 更新用户名处理器
│       │   ├── get-user-preference.handler.ts      # 获取偏好处理器
│       │   ├── ...                                 # 其他用户处理器
│       │   └── index.ts                            # 处理器导出
│       ├── user-preference/
│       │   ├── create-user-preference.handler.ts   # 创建偏好处理器
│       │   ├── update-user-preference.handler.ts   # 更新偏好处理器
│       │   ├── ...                                 # 其他偏好处理器
│       │   └── index.ts                            # 处理器导出
│       ├── space/
│       │   ├── create-space.handler.ts             # 创建空间处理器
│       │   ├── start-trial.handler.ts              # 开始试用处理器
│       │   ├── get-space-by-id.handler.ts          # 按ID获取空间处理器
│       │   ├── ...                                 # 其他空间处理器
│       │   └── index.ts                            # 处理器导出
│       └── auth/
│           ├── can-preview.handler.ts              # 权限检查处理器
│           ├── ...                                 # 其他认证处理器
│           └── index.ts                            # 处理器导出
├── dto/                                             # 数据传输对象
│   ├── user.dto.ts                                 # 用户相关 DTO
│   ├── user-preference.dto.ts                      # 用户偏好 DTO
│   └── space.dto.ts                                # 工作空间 DTO
└── tests/                                           # 测试文件
    ├── README.md                                    # 测试说明文档
    ├── jest.config.js                              # Jest 配置
    ├── test-setup.ts                               # 测试环境设置
    ├── validate-refactoring.ts                     # 重构验证工具
    └── youapp-user-api.integration.spec.ts         # 用户 API 集成测试
```

### 其他模块参考结构

```
src/modules/material-mng/                            # 素材管理模块（已完成重构）
├── material-mng.module.ts                          # 主模块
├── controllers/
│   ├── note.controller.ts                          # 笔记控制器
│   ├── thought.controller.ts                       # 思考控制器
│   └── board.controller.ts                         # 看板控制器
├── domain/
│   ├── note/                                       # Note 聚合
│   ├── thought/                                     # Thought 聚合
│   ├── board/                                       # Board 聚合
│   └── thought-version/                             # ThoughtVersion 聚合
├── repositories/                                    # 数据访问层
├── services/                                        # CQRS 服务层
└── tests/                                           # 测试文件

src/shared/                                          # 共享模块
├── db/
│   ├── database.service.ts                         # 数据库服务
│   └── public.schema.ts                            # 数据库 Schema (camelCase)
├── interceptors/
│   └── serialize.interceptor.ts                    # 序列化拦截器 (snake_case 兼容)
└── base/
    └── base.controller.ts                           # 基础控制器 (用户认证)
```

### 目录结构设计原则

#### 1. **按聚合组织** (Aggregate-Driven Structure)
- 每个聚合有独立的目录 (`user/`, `space/`, `user-preference/`)
- 聚合内部按功能分层 (`models/`, `events/`, `types/`)
- 避免技术驱动的目录结构 (如按 `entities/`, `services/` 分类)

#### 2. **CQRS 清晰分离** (Command-Query Separation)
- `commands/` - 所有修改操作的命令定义
- `queries/` - 所有查询操作的查询定义
- `handlers/` - 命令和查询的处理器实现
- 按聚合进一步分组，保持业务边界清晰

#### 3. **分层架构映射** (Layered Architecture Mapping)
- `controllers/` - 表现层，处理 HTTP 请求
- `services/` - 应用层，协调业务逻辑
- `domain/` - 领域层，核心业务规则
- `repositories/` - 基础设施层，数据访问

#### 4. **依赖方向控制** (Dependency Direction Control)
```
Controllers → Services (Commands/Queries/Handlers) → Domain ← Repositories
```
- Controllers 依赖 Services
- Services 依赖 Domain
- Repositories 依赖 Domain (实现 Domain 定义的接口)
- Domain 不依赖任何外层

#### 5. **模块化和可测试性** (Modularity & Testability)
- 每个目录都有 `index.ts` 统一导出
- 测试文件独立目录，便于管理
- 支持聚合级别的独立测试和部署

#### 6. **命名约定一致性** (Consistent Naming Conventions)
- 聚合目录: `kebab-case` (如 `user-preference/`)
- 文件名: `kebab-case.type.ts` (如 `user-preference.entity.ts`)
- 类名: `PascalCase` (如 `UserPreference`)
- 方法和变量: `camelCase` (如 `updateUserName`)

这个目录结构既体现了 DDD 的聚合边界，又清晰地展示了 CQRS 的命令查询分离，为开发者提供了清晰的代码组织指导。

## 标准实现模式

### 1. 领域实体模式

#### 领域实体属性访问控制规范

基于 DDD 最佳实践，采用以下属性访问控制模式：

1. **身份标识和不可变元数据**：使用 `public readonly`
   - id, createdAt, creatorId, spaceId 等

2. **可变业务状态**：使用 `private` + getter + 业务方法
   - 通过 private 保护不变量，通过 getter 提供读访问
   - 通过具有业务含义的方法（如 updateContent, publish）修改状态

3. **内部状态**：使用 `private`，不暴露
   - isNew, position 等仅内部使用的状态

4. **TypeScript 构造函数简化写法**：
   - 不可变属性可在构造函数中直接声明为 `public readonly`
   - 可变属性在构造函数中声明为 `private`，提供 getter 访问

5. **不使用下划线前缀**：
   - 遵循现代 TypeScript 和 NestJS 最佳实践
   - 使用 TypeScript 的 private 关键字提供真正的访问控制

#### 标准领域实体模式
```typescript
export class {Aggregate} extends AggregateRoot {
  private isNew: boolean = false;

  constructor(
    public readonly id: string,
    public readonly createdAt: Date,
    private updatedAt: Date,
    public readonly creatorId: string,
    public readonly spaceId: string,
    private status?: {Status},
    private content?: {Content},
  ) {
    super();
  }

  // 为可变属性提供 getter
  get updatedAt(): Date { return this.updatedAt; }
  get status(): {Status} | undefined { return this.status; }
  get content(): {Content} | undefined { return this.content; }

  // 通过业务方法修改状态
  updateContent(content: {Content}): void {
    this.content = content;
    this.updatedAt = new Date();
    this.apply(new {Aggregate}UpdatedEvent(this.id, 'content'));
  }

  changeStatus(newStatus: {Status}): void {
    this.status = newStatus;
    this.updatedAt = new Date();
    this.apply(new {Aggregate}StatusChangedEvent(this.id, newStatus));
  }

  static create(/* 创建参数 */): {Aggregate} {
    const aggregate = new {Aggregate}(/* 参数 */);
    aggregate.isNew = true;
    aggregate.apply(new {Aggregate}CreatedEvent(/* 事件参数 */));
    return aggregate;
  }

  delete(): void {
    this.apply(new {Aggregate}DeletedEvent(this.id));
  }

  get isNew(): boolean { return this.isNew; }
  markAsExisting(): void { this.isNew = false; }
}
```

#### 关键设计原则
- **封装性优先**: 使用 private 属性保护业务不变量，防止外部直接修改状态
- **业务方法**: 通过具有业务含义的方法修改状态，确保业务规则得到执行
- **只读访问**: 通过 getter 提供对私有状态的只读访问
- **不可变标识**: id、createdAt、creatorId 等使用 `public readonly` 确保不可变性
- **字符串字面量类型**: 使用 `as const` 替代枚举，提供更好的类型安全
- **值对象模式**: 使用 interface 定义复杂的值对象结构
- **领域事件**: 在业务方法中触发事件，传递关键信息
- **实体生命周期**: 使用 `isNew` 标记实体状态，供 Repository 层使用

### 2. Repository 模式

#### 基本模式
```typescript
@Injectable()
export class {Aggregate}Repository {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  async save(aggregate: {Aggregate}): Promise<void> {
    const do = this.entityToDO(aggregate);
    if (aggregate.isNew) {
      await this.db.insert(table).values(do);
      aggregate.markAsExisting();
    } else {
      const { id, ...shouldUpdate } = do;
      await this.db.update(table).set(shouldUpdate).where(eq(table.id, id));
    }
  }

  async findById(id: string): Promise<{Aggregate} | undefined> {
    const result = await this.db
      .select()
      .from(table)
      .where(and(isNull(table.deletedAt), eq(table.id, id)));

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  async getById(id: string): Promise<{Aggregate}> {
    const result = await this.findById(id);
    if (!result) {
      throw new NotFoundException(`{Aggregate} with id ${id} not found`);
    }
    return result;
  }

  private entityToDO(aggregate: {Aggregate}): {Aggregate}DO {
    // 实体到 DO 的转换
  }

  private doToEntity(do: {Aggregate}DO): {Aggregate} {
    // DO 到实体的转换
  }
}
```

#### 关键设计原则
- **过滤条件接口**: 使用 TypeScript 接口定义查询条件
- **软删除**: 使用 `deletedAt` 字段实现软删除，查询时过滤已删除记录
- **JSON 字段处理**: 复杂对象存储为 JSON 字符串，转换时进行解析
- **类型安全**: 使用 TypeScript 类型断言和类型守卫
- **参数分离**: 更新时分离 id 和更新字段，避免意外修改 id

### 3. CQRS Handler 模式

#### Command 定义
```typescript
export class Create{Aggregate}Command implements ICommand {
  constructor(
    public readonly userId: string,
    public readonly spaceId: string,
    /* 其他参数 */
  ) {}
}
```

#### Command Handler 实现
```typescript
@CommandHandler(Create{Aggregate}Command)
export class Create{Aggregate}Handler implements ICommandHandler<Create{Aggregate}Command> {
  constructor(
    private readonly {aggregate}Repository: {Aggregate}Repository,
    private readonly {aggregate}DtoService: {Aggregate}DtoService,
  ) {}

  async execute(command: Create{Aggregate}Command): Promise<{Aggregate}Dto> {
    const { userId, spaceId, /* 其他参数 */ } = command;

    // 1. 业务验证
    await this.validateBusinessRules(/* 参数 */);

    // 2. 创建聚合
    const aggregate = {Aggregate}.create(/* 参数 */);

    // 3. 保存聚合
    await this.{aggregate}Repository.save(aggregate);

    // 4. 提交领域事件
    aggregate.commit();

    // 5. 返回 DTO
    return this.{aggregate}DtoService.to{Aggregate}Dto(aggregate);
  }

  private async validateBusinessRules(/* 参数 */): Promise<void> {
    // 业务验证逻辑
  }
}
```

#### Query Handler 实现
```typescript
export class List{Aggregate}Query implements IQuery {
  constructor(
    public readonly spaceId: string,
    /* 其他参数 */
  ) {}
}

@QueryHandler(List{Aggregate}Query)
export class List{Aggregate}Handler implements IQueryHandler<List{Aggregate}Query> {
  constructor(
    private readonly {aggregate}Repository: {Aggregate}Repository,
    private readonly {aggregate}DtoService: {Aggregate}DtoService,
  ) {}

  async execute(query: List{Aggregate}Query): Promise<{Aggregate}Dto[]> {
    // 1. 权限验证
    await this.validatePermissions(query.spaceId);

    // 2. 构建查询条件
    const filter = this.buildFilter(query);

    // 3. 查询数据
    const aggregates = await this.{aggregate}Repository.findByFilter(filter);

    // 4. 转换为 DTO
    return this.{aggregate}DtoService.to{Aggregate}DtoList(aggregates);
  }

  private async validatePermissions(spaceId: string): Promise<void> {
    // 权限验证逻辑
  }

  private buildFilter(query: List{Aggregate}Query): {Aggregate}Filter {
    // 构建查询条件
  }
}
```

#### 关键设计原则
- **命令查询分离**: 命令处理业务逻辑，查询处理数据获取
- **业务验证**: 在 Handler 中进行业务规则验证
- **数据转换**: 在 Handler 中处理参数转换和默认值
- **事件提交**: 使用 `commit()` 方法提交领域事件
- **DTO 转换**: 使用专门的 DtoService 进行实体到 DTO 的转换
- **错误处理**: 使用 NestJS 内置异常类型进行统一错误处理

### 4. Controller 模式

#### 基本模式
```typescript
@ApiTags('{Aggregate} Management API')
@Controller('api/v1/{aggregate}')
export class {Aggregate}Controller extends BaseController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {
    super();
  }

  @Post('{action}')
  @HttpCode(200)
  @ApiOperation({
    summary: '中文摘要',
    description: '中文详细描述',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: {Result}Dto,
  })
  async {action}(@Body() dto: {Action}Dto): Promise<{Result}Dto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new {Action}Command(/* 参数 */);
    return this.commandBus.execute(command);
  }
}
```

#### 关键设计原则
- **非 RESTful 风格**: 所有 API 端点使用 `@Post()` 方法
- **中文 API 文档**: 使用中文的 summary 和 description
- **统一状态码**: 使用 `@HttpCode(200)` 统一返回 200 状态码
- **基础控制器**: 继承 `BaseController` 获取用户信息
- **CQRS 模式**: 使用 CommandBus 和 QueryBus 分离命令和查询
- **DTO 验证**: 使用 `@Body()` 装饰器和 DTO 类进行参数验证
- **类型安全**: 使用 TypeScript 类型定义返回值类型

#### DtoService 模式

```typescript
@Injectable()
export class {Aggregate}DtoService {
  constructor(
    /* 依赖注入 */
  ) {}

  // 单个实体转换
  async to{Aggregate}Dto(aggregate: {Aggregate}): Promise<{Aggregate}Dto> {
    const dto: {Aggregate}Dto = {
      id: aggregate.id,
      createdAt: aggregate.createdAt,
      updatedAt: aggregate.updatedAt,
      /* 其他基本字段 */
    };

    // 处理关联实体信息
    if (aggregate.relatedEntity) {
      dto.relatedEntity = await this.processRelatedEntity(aggregate.relatedEntity);
    }

    // 保持 API 一致性，即使为空也要添加字段
    dto.optionalField = aggregate.optionalField || /* 默认值 */;

    return dto;
  }

  // 批量转换（性能优化版本）
  async to{Aggregate}DtoList(aggregates: {Aggregate}[]): Promise<{Aggregate}Dto[]> {
    if (aggregates.length === 0) {
      return [];
    }

    // 分组获取关联实体信息以优化性能
    const relatedEntityIds = aggregates
      .filter(aggregate => aggregate.relatedEntity)
      .map(aggregate => aggregate.relatedEntity.id);

    // 批量获取实体信息
    const relatedEntityMap = await this.batchGetRelatedEntities(relatedEntityIds);

    return aggregates.map(aggregate => this.build{Aggregate}Dto(aggregate, relatedEntityMap));
  }

  private build{Aggregate}Dto(aggregate: {Aggregate}, relatedEntityMap: Map<string, any>): {Aggregate}Dto {
    // 构建 DTO 逻辑
  }

  private async batchGetRelatedEntities(ids: string[]): Promise<Map<string, any>> {
    // 批量获取关联实体逻辑
  }
}
```

**关键设计原则**:
- **性能优化**: 批量转换时使用 Map 缓存关联实体信息
- **兼容性处理**: 保持向后兼容的字段
- **API 一致性**: 即使为空也要添加字段以保持响应结构一致
- **错误容错**: 关联实体获取失败时使用默认值

## 测试和质量保证

### 1. 测试策略
- **完整覆盖**: 测试所有 API 端点和业务场景
- **边界测试**: 验证参数格式、必填项、边界值
- **组合测试**: 测试可选参数的各种组合
- **错误处理**: 测试异常情况和错误响应
- **数据验证**: 创建/更新后通过查询验证数据正确性

### 2. 测试配置和环境

#### Jest 配置规范
- **继承 package.json 配置**: 使用或继承 package.json 中的 Jest 配置，避免重复定义
- **ESM 模式**: 使用 `preset: "ts-jest/presets/default-esm"` 和 `NODE_OPTIONS='--experimental-vm-modules'`
- **模块映射**: 继承 `moduleNameMapper` 配置，支持 `@/` 路径别名
- **测试超时**: 设置合适的 `testTimeout` 值，建议 120000ms (2分钟)

#### 环境变量设置（参考 material-mng/tests/test-setup.ts）
- **自动加载**: 在 `test-setup.ts` 中自动加载 `.env.preview.local` 和 `.env.preview` 文件
- **环境变量检查**: 确保必要的环境变量存在，如 `SUPABASE_JWT_SECRET`
- **服务检查**: 在 `beforeAll` 中检查依赖服务（如 youapp）是否可用
- **环境变量清单**：
  - `TEST_USER_ID`: 测试用户 ID（默认: 'test-user-123'）
  - `SUPABASE_JWT_SECRET`: Supabase JWT 签名密钥（必需）
  - `NEXT_PUBLIC_SUPABASE_URL`: Supabase 项目 URL

### 3. 认证实现（参考 material-mng/note 测试）

**JWT Token 生成**：
```typescript
const secret = new TextEncoder().encode(SUPABASE_JWT_SECRET);
const jwt = await new jose.SignJWT({
  aud: 'authenticated',
  exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour
  sub: TEST_USER_ID,
  email: '<EMAIL>',
  role: 'authenticated',
  iss: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/auth/v1`,
})
.setProtectedHeader({ alg: 'HS256' })
.setIssuedAt()
.sign(secret);
```

**HTTP 客户端配置**：
```typescript
const apiClient = axios.create({
  baseURL: testConfig.baseURL,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${jwt}`,
    'x-use-snake-case': 'true',
  },
});
```

### 4. 测试独立性规范
- **完全独立**：每个测试用例必须完全独立，不能依赖其他测试的执行结果或顺序
- **严禁数据污染**：禁止在测试中清除所有数据，只能清理本测试创建的数据
- **独立数据管理**：每个测试组使用 `beforeAll/afterAll` 或 `beforeEach/afterEach` 创建和清理自己的测试数据
- **禁止全局变量**：不得使用 `global.*` 等全局变量在测试间共享状态
- **任意顺序执行**：测试用例应该能够以任意顺序运行或单独运行，不能假设特定的执行顺序

### 5. 代码规范
- 使用 TypeScript 严格模式
- 遵循 NestJS 最佳实践
- 内部代码统一使用 camelCase，通过 SerializeInterceptor 来兼容原来 snake_case 的输入输出
- 对于需要复制的"错误"行为，使用 `// TODO: 修正此处的不合理行为` 注释标记
- **架构独立性**：逐步解除对 youapp 的 xxxDomain、xxxDao 等对象的直接依赖，实现架构独立
- **重构逐步进行**：重构过程中可以保持对现有代码的依赖，但最终目标是实现完全的架构独立
- **DTO 和数据验证**：Controller 中所有方法必须使用 DTO 作为参数，使用 class-validator 进行数据验证，避免使用内联对象类型
- **参考成功案例**：参考 material-mng 模块的实现方式，特别是 note、thought、board 等聚合的架构模式

### 6. 性能和安全要求
- 避免 N+1 查询问题
- 合理使用数据库索引
- 优化序列化/反序列化
- 考虑并发访问场景
- 验证用户权限
- 防止 SQL 注入
- 输入数据验证
- 敏感信息脱敏


## 迁移检查清单

### 第一阶段：测试驱动的需求分析
- [ ] **编写集成测试用例**
    - [ ] 基于 youapp 原有 API 编写完整测试用例
    - [ ] 覆盖所有 API 端点和业务场景
    - [ ] 包含各种边界条件和参数组合
    - [ ] 包含错误处理和异常场景
    - [ ] 边写边测，确保测试用例本身正确
    - [ ] 所有测试用例在 youapp 上通过

### 第二阶段：DDD 架构重写
- [ ] **分析 youapp 原有实现**
    - [ ] 梳理原有 API 的业务逻辑
    - [ ] 分析依赖的底层代码和数据结构
    - [ ] 识别现有实现中的"错误"行为
- [ ] **按 DDD 规范重写**
    - [ ] 设计聚合结构和领域模型
    - [ ] 实现领域实体和值对象（重新编写，不直接使用 youapp 的类）
    - [ ] 实现 Repository 层（重新编写，不调用 youapp 的 xxxDao）
    - [ ] 实现 CQRS 组件 (Commands/Queries/Handlers)
    - [ ] 实现 Controller 和 DTO
    - [ ] **架构重构**：在重构过程中逐步解除对 youapp 的依赖，最终实现架构独立
    - [ ] **分阶段重构**：允许重构过程中保持对现有代码的依赖，但需要清楚标识待重构部分
    - [ ] 对于"错误"的逻辑，先完全复制并添加 `// TODO: 修正此处的不合理行为` 注释

### 第三阶段：测试驱动验证
- [ ] **切换测试目标**
    - [ ] 修改测试配置，使用 youapi 路由
    - [ ] 使用 `x-use-snake-case: true` 请求头确保兼容性
    - [ ] **严格遵循**：测试用例不可修改，只能调整 youapi 实现
    - [ ] **测试修复原则**：除了状态码兼容性处理外，其他测试逻辑尽量不要修改，应该优先考虑 youapi 的实现有问题
- [ ] **测试验证**
    - [ ] 所有核心功能测试通过
    - [ ] 所有边界条件测试通过
    - [ ] 所有错误处理测试通过
    - [ ] 数据一致性验证通过
    - [ ] 性能基准验证通过

### 第四阶段：质量保证
- [ ] **代码质量**
    - [ ] 代码审查通过
    - [ ] 类型检查通过
    - [ ] 单元测试覆盖率达标
- [ ] **部署准备**
    - [ ] 环境配置完整
    - [ ] 监控和日志配置
    - [ ] 文档更新


## 经验总结和实施规划

### Material-Mng 模块成功经验

#### 1. **模块化设计成功经验**
- **主模块**: `MaterialMngModule` 作为核心模块，统一管理所有聚合
- **独立子模块**: 如 `ThoughtVersionModule` 独立实现，被主模块导入
- **清晰边界**: 每个聚合保持独立的业务边界，通过 Repository 模式封装数据访问
- **统一路由**: 通过主 Controller 统一管理路由，保持与 youapp 的兼容性

#### 2. **测试驱动开发经验**
- **完整测试覆盖**: 每个聚合都有完整的集成测试
- **双目标测试**: 支持 youapp/youapi 切换，便于对比验证
- **兼容性保证**: 使用 `x-use-snake-case` 请求头确保 API 向后兼容
- **测试独立性**: 每个测试用例完全独立，避免数据污染

#### 3. **成功的架构模式**
- **CQRS 模式**: 清晰分离命令和查询，提高代码可维护性
- **聚合根模式**: 通过聚合根管理实体生命周期和业务规则
- **Repository 模式**: 封装数据访问逻辑，与领域层解耦
- **事件驱动**: 使用领域事件处理异步操作和聚合间通信

### IAM 模块重构规划

#### 现状分析
IAM 模块已经从 youapp 平迁了原有的代码实现，具备完整的 `/api/v1` 路由功能和业务逻辑，但使用的是传统的 Service 层架构，需要重构为现代的 DDD + CQRS 架构。

#### 重构目标聚合设计：
1. **User 聚合** - 用户基本信息管理
2. **User-Preference 聚合** - 用户偏好设置管理
3. **Space 聚合** - 工作空间管理
4. **Subscription 聚合** - 订阅和计费管理
5. **Authentication 功能** - 认证相关命令和查询

#### 实施策略
**阶段一：保持 API 兼容性的内部重构**
- 保持现有 Controller 层路由不变
- 逐步引入 CQRS 模式，添加 Commands/Queries/Handlers
- 重构 Service 层，从传统服务转为 CommandBus/QueryBus 调用
- 引入完整的 Repository 模式替代直接 Domain 服务调用

**阶段二：完整的 DDD 架构重构**
- 实现完整的聚合根和领域实体
- 引入领域事件和事件处理器
- 完善值对象和领域服务
- 添加完整的集成测试

## 编码规范和约定

### 1. 命名约定
- **文件和目录**: kebab-case (`material-mng/`, `user-preference.controller.ts`)
- **类名和接口**: PascalCase (`UserService`, `UserRepository`, `CreateUserDto`)
- **变量和方法**: camelCase (`userId`, `isValid`, `getUserById()`)
- **常量**: SCREAMING_SNAKE_CASE (`MAX_USERS`, `DEFAULT_PAGE_SIZE`)

### 2. 文件后缀约定
- **聚合根**: `*.entity.ts`
- **值对象**: `*.vo.ts`
- **数据传输对象**: `*.dto.ts`
- **领域服务**: `*-domain.service.ts`
- **领域事件**: `*.event.ts`

### 3. 枚举类型
推荐使用字符串字面量类型，避免 TypeScript enum：

```typescript
// 推荐方式
export const UserStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
} as const;

export type UserStatus = typeof UserStatus[keyof typeof UserStatus];

// 使用
const user = { status: UserStatus.ACTIVE };
```

### 4. 方法命名规范

#### 查询方法命名
- **get**: 期望资源存在，不存在时抛异常 (`getUserById`)
- **find**: 资源可能不存在，不存在时返回 null (`findById`)
- **list**: 列表查询，返回数组 (`listUsers`, `listUsersByRole`)
- **search**: 全文索引、语义搜索 (`searchUsersByKeyword`)

#### 期望成功与可能失败
- **期望成功**: `deleteUser` → 失败时抛异常
- **可能失败**: `tryDeleteUser` → 返回 `boolean` 表示成功状态

### 5. 异常处理策略
- **统一使用 NestJS 内置异常**: BadRequestException、UnauthorizedException、ForbiddenException、NotFoundException、ConflictException、InternalServerErrorException
- **让异常冒泡**: 不在每层进行 try-catch，让异常向上传播
- **框架统一处理**: 由 NestJS 内置 Exception Filter 自动捕获异常并转换为标准 HTTP 响应格式

### 6. 参数传递规范
推荐使用对象参数而非位置参数，避免参数顺序错误：

```typescript
// ❌ 错误：位置参数容易出错
class UpdateUserCommand {
  constructor(
    public readonly userId: string,
    public readonly name?: string,
    public readonly email?: string,
    public readonly avatar?: string,
  ) {}
}

// ✅ 正确：对象参数明确且安全
interface UpdateUserParams {
  userId: string;
  name?: string;
  email?: string;
  avatar?: string;
}

class UpdateUserCommand {
  constructor(private params: UpdateUserParams) {
    this.userId = params.userId;
    this.name = params.name;
    this.email = params.email;
    this.avatar = params.avatar;
  }
}

// 使用时明确且不易出错
const command = new UpdateUserCommand({
  userId: dto.id,
  name: dto.name,
  email: dto.email,
  avatar: dto.avatar,
});
```

### 7. 其他编码约定
- 不要使用 barrel exports file 例如 index.ts
- 不要 undefined 和 null 混用，优先使用 ?，其次是 undefined
- async 函数使用 return 而不是 return await
- 路由的迁移情况见 @youapp-routes.md
