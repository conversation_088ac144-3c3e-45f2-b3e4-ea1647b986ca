---
description: 
globs: 
alwaysApply: true
---

当前应用基于 /Users/<USER>/Projects/github.com/YouMindInc/youapp 迁移，原应用是 Next.js 应用采取了函数式写法。迁移到当前应用后因适配至 NestJS 写法，迁移过程中的注意事项应参考以下规则。验证时仅验证当前修改文件，项目依然处于大量修改中，可能会有很多无意异常。

### Pattern 1: Singleton to Injectable Service

**Before**:
```typescript
const userDomain = new UserDomainService();
export default userDomain;
```

**After**:
```typescript
@Injectable()
export class UserDomainService {
  // Remove default export instance
}
```

### Pattern 2: Replace service call with DI

不要直接使用服务实例，youapp 的代码由于 Next.js 自身的限制没有 DI。迁移至当前项目后，均应修改为 DI 注入

**Before**:
```typescript
import userDAO from "@/dao/user";
await userDAO.selectById(id);
```

**After**:
```typescript
constructor(private readonly userDAO: UserDAO) {}
await this.userDAO.selectById(id);
```

### Pattern 3: Background Processing to Events

**Before**:
```typescript
runInBackground(someAsyncOperation);
```

**After**:
```typescript
this.eventBus.publish(new SomeEvent(data));
```

### Pattern 4: Migrate console to this.logger

**Before**:
```typescript
console.log("printing");
```

**After**:
```typescript
this.logger.info("printing");
```

### Pattern 5: Migrate method to get current user/space to use YouapiClsService

通过 NestJS CLS 进行上下文参数注入，改为 YouapiClsService 方法调用

**Before**:
```typescript
getCurrentUserId()
tryGetStoreUserId()
tryGetStoreSpace()
tryGetStoreSpaceId()
```

**After**:
```typescript
// replace getCurrentUserId() with
this.youbizClsService.getUserId();
// replace tryGetStoreUserId() with
this.youbizClsService.getUserId();
// replace tryGetStoreSpace() with
this.youbizClsService.getSpace();
// replace tryGetStoreSpaceId() with
this.youbizClsService.getSpaceId()
```

### Pattern 6: Remove Sentry

后续不需要了，删掉即可

**Before**:
```typescript
import * as Sentry from "@sentry/nextjs"

Sentry.captureException()
```

**After**:
```typescript
```

### Pattern 7: Update reference

通用能力会迁移到 common 模块，包括类型、异常、util。类似这样：

**Before**:
```typescript
import { LLMs } from "@/lib/common/chat";
```

**After**:
```typescript
import { LLMs } from "@/common/types";
```

DAO 也迁移到 "@/dao"，例如：

**Before**:
```typescript
import { userDao } from "@/lib/dao/user";
```

**After**:
```typescript
import { UserDAO } from "@/dao/user";
```

Domain 服务目前存在于对应的 domain 文件夹

**Before**:
```typescript
import { userService } from "@/lib/domain/user/index";
```

**After**:
```typescript
import { UserService } from "@/domain/user";
```

### Pattern 9: Comment on original file and unmigrated code

在文件顶部备注它的在 youapp 的原始文件，这样有助于在不确定迁移代码时回溯原始实现。

部分代码在迁移时被注释掉了，或找不到对应实现。对于这种代码请不要删除，仅注释原始代码并留下备注方便后续迁移时补上。

所有注释请使用中文编写。