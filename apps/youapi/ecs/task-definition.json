{"family": "you<PERSON>i", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096", "executionRoleArn": "arn:aws:iam::975050168163:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::975050168163:role/youapiTaskRole", "containerDefinitions": [{"name": "you<PERSON>i", "image": "WILL_BE_REPLACED", "portMappings": [{"containerPort": 4000, "protocol": "tcp"}], "essential": true, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:4000/api/healthz || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "environment": [{"name": "PORT", "value": "4000"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-region": "us-west-1", "awslogs-group": "/ecs/youapi-preview", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}]}