/**
 * Environment utility functions
 * Migrated from youapp/src/lib/utils
 */

/**
 * Get environment identifier for database naming
 * @param env - Optional environment override
 * @returns Environment identifier string
 */
export function getEnvIdentifier(env?: string): string {
  const currentEnv = env || process.env.NODE_ENV || 'development';

  switch (currentEnv) {
    case 'production':
      return 'prod';
    case 'preview':
    case 'staging':
      return 'preview';
    case 'development':
    case 'dev':
    default:
      return 'local';
  }
}

/**
 * Check if connecting to preview/staging database
 * @returns True if connecting to preview database
 */
export function isConnectingPreviewDB(): boolean {
  const dbUrl = process.env.DATABASE_URL || '';
  return dbUrl.includes('preview') || dbUrl.includes('staging') || getEnvIdentifier() === 'preview';
}

/**
 * Check if connecting to production database
 * @returns True if connecting to production database
 */
export function isConnectingProdDB(): boolean {
  const dbUrl = process.env.DATABASE_URL || '';
  return dbUrl.includes('prod') || getEnvIdentifier() === 'prod';
}
