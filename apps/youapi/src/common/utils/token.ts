/**
 * Character ranges for CJK (Chinese, Japanese, Korean) characters
 */
const CJK_RANGES: Array<[number, number]> = [
  [0x4e00, 0x9fff], // Chinese
  [0x3040, 0x30ff], // Japanese
  [0xac00, 0xd7af], // Korean
];

/**
 * Common punctuation characters
 */
const PUNCTUATION_CHARS = '.,!?;:()[]{}"\'' as const;

/**
 * Counts the number of CJK (Chinese, Japanese, Korean) characters in a string
 */
const countCJKChars = (text: string): number => {
  return text.split('').filter((char) => {
    const code = char.charCodeAt(0);
    return CJK_RANGES.some(([start, end]) => code >= start && code <= end);
  }).length;
};

/**
 * Estimates the number of tokens in a given text string
 *
 * @param text - Input string to estimate tokens for
 * @returns Estimated token count
 *
 * @example
 * ```typescript
 * // Basic usage
 * estimateTokens("Hello world!") // returns 3
 *
 * // With detailed breakdown
 * estimateTokens("Hello 你好!") // returns 4;
 * ```
 */
export const estimateTokens = (text: string): number => {
  if (!text) {
    return 0;
  }

  text = text.trim();

  // Component counts
  const totalChars: number = text.length;
  const cjkChars: number = countCJKChars(text);
  const spaces: number = text.split(' ').length - 1;
  const numbers: number = text.split('').filter((c) => /\d/.test(c)).length;
  const punctuation: number = text.split('').filter((c) => PUNCTUATION_CHARS.includes(c)).length;

  // Calculate English characters (excluding other counted characters)
  const englishChars: number = totalChars - cjkChars - spaces - numbers - punctuation;

  // Token estimations with more conservative values
  const cjkTokens: number = Math.ceil(cjkChars * 1.4); // Increase CJK token ratio
  const englishTokens: number = Math.ceil(englishChars / 3); // ~3 chars per token instead of 4
  const spaceTokens: number = spaces;
  const numberTokens: number = Math.ceil(numbers / 2); // ~2 digits per token instead of 2.5
  const punctuationTokens: number = punctuation;

  const totalTokens: number = Math.max(
    1,
    Math.ceil(
      // Use ceil instead of round for conservative estimation
      cjkTokens + englishTokens + spaceTokens + numberTokens + punctuationTokens + 2, // Increased safety padding
    ),
  );

  return totalTokens;
};

/**
 * Options for text truncation
 */
interface TruncateOptions {
  /** Maximum number of tokens allowed */
  maxTokens: number;
  /** Text to append when truncated (default: "...") */
  suffix?: string;
  /** Whether to preserve whole words (default: true) */
  preserveWords?: boolean;
  /** Reserve tokens for suffix (default: true) */
  reserveForSuffix?: boolean;
}

/**
 * Truncates text to fit within a specified token limit
 *
 * @param text - Input text to truncate
 * @param options - Truncation options
 * @returns Truncated text
 *
 * @example
 * ```typescript
 * // Basic usage
 * truncateByTokens("This is a long text", { maxTokens: 3 })
 * // returns "This is..."
 *
 * // Custom suffix
 * truncateByTokens("This is a long text", {
 *   maxTokens: 4,
 *   suffix: " [truncated]"
 * })
 * // returns "This is [truncated]"
 * ```
 */
export const truncateByTokens = (text: string, options: TruncateOptions): string => {
  const { maxTokens, suffix = '...', preserveWords = true, reserveForSuffix = true } = options;

  if (!text) return text;

  const currentTokens = estimateTokens(text);
  if (currentTokens <= maxTokens) return text;

  const suffixTokens = reserveForSuffix ? estimateTokens(suffix) : 0;
  const targetTokens = maxTokens - suffixTokens;

  if (targetTokens <= 0) {
    return suffix;
  }

  let truncated = text;
  let tokens = currentTokens;

  // Binary search for approximate position
  let left = 0;
  let right = text.length;

  while (left < right) {
    const mid = Math.floor((left + right + 1) / 2);
    const slice = text.slice(0, mid);
    tokens = estimateTokens(slice);

    if (tokens <= targetTokens) {
      left = mid;
    } else {
      right = mid - 1;
    }
  }

  truncated = text.slice(0, left);

  // If preserving words, backtrack to last space
  if (preserveWords && truncated.length > 1) {
    const lastSpace = truncated.lastIndexOf(' ');
    if (lastSpace > 0) {
      truncated = truncated.slice(0, lastSpace);
    }
  }

  return truncated + suffix;
};
