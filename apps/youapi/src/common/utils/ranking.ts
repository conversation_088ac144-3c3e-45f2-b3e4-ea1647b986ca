/**
 * Ranking Utilities - 排序工具
 * 使用 Figma 的 fractional-indexing 方案
 *
 * Migrated from:
 * - youapp/src/lib/common/utils.ts
 */

import { BASE_62_DIGITS, generateKeyBetween, generateNKeysBetween } from 'fractional-indexing';

/**
 * 使用 Figma 的 fractional-indexing 方案
 * https://www.figma.com/blog/realtime-editing-of-ordered-sequences/#fractional-indexing
 * 只用数字 + 大小写字母，共 62 个字符，可读性强一些
 *
 * @param rank_after 之后的排序值
 * @param rank_before 之前的排序值
 * @returns 两者之间的新排序值
 */
export function rankBetween(rankAfter: string | undefined, rankBefore: string | undefined): string {
  return generateKeyBetween(rankAfter, rankBefore, BASE_62_DIGITS);
}

/**
 * 生成 n 个排序值在两个排序值之间
 *
 * @param n 要生成的排序值数量
 * @param rank_after 之后的排序值
 * @param rank_before 之前的排序值
 * @returns n 个新的排序值数组
 */
export function rankNBetween(
  n: number,
  rankAfter: string | undefined,
  rankBefore: string | undefined,
): string[] {
  return generateNKeysBetween(rankAfter, rankBefore, n, BASE_62_DIGITS);
}

/**
 * 比较两个排序值
 *
 * @param a 第一个排序值
 * @param b 第二个排序值
 * @returns -1 if a < b, 1 if a > b, 0 if equal
 */
export function rankCompare(a: string, b: string): number {
  return a < b ? -1 : a > b ? 1 : 0;
}
