/** biome-ignore-all lint/complexity/noStaticOnlyClass: <工具类> */
import type { ModuleRef } from '@nestjs/core';
import { TestingModule } from '@nestjs/testing';

/**
 * 用于获取应用上下文中的服务实例
 */
export class ApplicationContext {
  private static moduleRef: ModuleRef | TestingModule;

  static register(ref: ModuleRef | TestingModule) {
    ApplicationContext.moduleRef = ref;
  }

  // biome-ignore lint/suspicious/noExplicitAny: <所有 provider 都可能被注入>
  static getProvider<T>(type: any): T {
    // Avoid circular JSON serialization by not logging the moduleRef object
    console.log(`ContextService getting provider: ${type?.name || type}`);
    return ApplicationContext.moduleRef.get(type, { strict: false });
  }
}
