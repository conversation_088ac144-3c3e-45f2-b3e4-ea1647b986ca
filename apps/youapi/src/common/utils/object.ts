/**
 * Object Utilities - 对象处理工具
 * 提供对象操作和安全解析功能
 *
 * Migrated from:
 * - youapp/src/lib/utils.ts
 */

/**
 * 安全解析 JSON 字符串
 * @param jsonString 要解析的 JSON 字符串
 * @param stripInvalidChars 是否移除无效字符
 * @param fallback 解析失败时的默认值
 * @returns 解析结果或默认值
 */
export function SafeParse<T>(
  jsonString: string,
  stripInvalidChars = false,
  fallback: T = {} as T,
): T {
  try {
    if (stripInvalidChars) {
      // remove all invalid control characters for json parsing
      jsonString = jsonString
        .replace(/\n/g, '')
        .replace(/\r/g, '')
        .replace(/\t/g, '')
        .replace(/\f/g, '')
        .replace(/\v/g, '');
    }

    return JSON.parse(jsonString) as T;
  } catch (e) {
    return fallback;
  }
}

/**
 * 定义类型：排除 undefined 和 null 的对象类型
 */
type Defined<T> = {
  [P in keyof T]: Exclude<T[P], undefined | null>;
};

/**
 * 净化对象，移除 undefined 和 null 值
 * @param obj 要净化的对象
 * @returns 净化后的对象
 */
export function purifyObject<T extends object>(obj: T): Defined<T> {
  return Object.fromEntries(
    Object.entries(obj).filter(([, value]) => value !== undefined && value !== null),
  ) as Defined<T>;
}

/**
 * Convert JSON object to XML format for LLM consumption
 * @param obj - Object to convert
 * @returns XML string representation
 */
export function jsonToXml(obj: any): string {
  if (typeof obj !== 'object' || obj === null) {
    return String(obj);
  }

  let xml = '';
  for (const [key, value] of Object.entries(obj)) {
    if (Array.isArray(value)) {
      for (const item of value) {
        xml += `<${key}>${jsonToXml(item)}</${key}>`;
      }
    } else if (typeof value === 'object' && value !== null) {
      xml += `<${key}>${jsonToXml(value)}</${key}>`;
    } else {
      xml += `<${key}>${String(value)}</${key}>`;
    }
  }
  return xml;
}
