import axios from 'axios';

import { LLMError } from '@/common/errors';
import { fetchWithTimeout, sleep } from './http';

// 不支持 image url, 需要换成 Uint8Array https://docs.anthropic.com/en/docs/build-with-claude/vision#can-claude-read-image-urls
export async function imageUrlToBytesAndFormat(
  url: string,
): Promise<{ bytes: Uint8Array; format: string }> {
  try {
    // Fetch the image using axios with responseType arraybuffer
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      headers: {
        Accept: 'image/*',
      },
      timeout: 10000,
    });

    // Response data is already ArrayBuffer, convert to Uint8Array
    const imageBytes = new Uint8Array(response.data);

    // Get the Content-Type from headers (axios normalizes headers to lowercase)
    const contentType = response.headers['content-type'].toLowerCase();
    const format = contentType.split('/')[1] || 'jpg';
    if (!format) {
      throw new LLMError('invalid_image_format', { format: contentType });
    }

    return { bytes: imageBytes, format: 'image/' + format };
  } catch (error) {
    console.warn('Failed to fetch image from url:', url, error);
    throw new LLMError('invalid_image_format', {
      message: (error as Error).message,
    });
  }
}

/**
 * 验证 CDN URL 是否有效的配置选项
 */
interface ValidateUrlOptions {
  /** 最大重试时间（毫秒），默认 10000ms */
  maxRetryTime?: number;
  /** 最小退避间隔（毫秒），默认 3000ms */
  minBackoffMs?: number;
  /** 最大退避间隔（毫秒），默认 30000ms */
  maxBackoffMs?: number;
  /** 退避倍数，默认 2 */
  backoffMultiplier?: number;
  /** 请求超时时间（毫秒），默认 5000ms */
  requestTimeout?: number;
}

/**
 * 验证结果
 */
interface ValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 最后一次响应的状态码 */
  lastStatusCode?: number;
  /** 重试次数 */
  retryCount: number;
  /** 总耗时（毫秒） */
  totalTimeMs: number;
  /** 错误信息 */
  error?: string;
}

/**
 * 使用 HTTP OPTIONS 请求验证 CDN URL 是否有效
 * 实现指数退避重试逻辑，防止过度请求
 *
 * @param url - 要验证的 CDN URL
 * @param options - 配置选项
 * @returns Promise<ValidationResult> - 验证结果
 */
export async function validateCdnUrl(
  url: string,
  options: ValidateUrlOptions = {},
): Promise<ValidationResult> {
  const {
    maxRetryTime = 10000, // 10 秒
    minBackoffMs = 3000, // 3 秒最小退避
    maxBackoffMs = 5000, // 5 秒最大退避
    backoffMultiplier = 2,
    requestTimeout = 5000, // 5 秒请求超时
  } = options;

  const startTime = Date.now();
  let retryCount = 0;
  let lastStatusCode: number | undefined;
  let lastError: string | undefined;
  let currentBackoff = minBackoffMs;

  while (Date.now() - startTime < maxRetryTime) {
    try {
      const response = await fetchWithTimeout(url, {
        method: 'HEAD',
        timeout: requestTimeout,
        headers: {
          Accept: '*/*',
        },
      });

      lastStatusCode = response.status;

      // 检查状态码是否有效（小于 400）
      if (response.status < 400) {
        return {
          isValid: true,
          lastStatusCode: response.status,
          retryCount,
          totalTimeMs: Date.now() - startTime,
        };
      }

      // 状态码为 400+，记录错误
      lastError = `HTTP ${response.status}: ${response.statusText}`;
    } catch (error) {
      // 网络错误或其他异常
      if (error instanceof Error) {
        lastError = error.message;
      } else {
        lastError = String(error);
      }
    }

    // 检查是否还有时间进行下一次重试
    const remainingTime = maxRetryTime - (Date.now() - startTime);
    if (remainingTime <= currentBackoff) {
      // 没有足够时间进行下一次重试
      break;
    }

    // 等待退避时间
    await sleep(currentBackoff);

    // 增加重试次数和退避时间
    retryCount++;
    currentBackoff = Math.min(currentBackoff * backoffMultiplier, maxBackoffMs);
  }

  // 所有重试都失败了
  return {
    isValid: false,
    lastStatusCode,
    retryCount,
    totalTimeMs: Date.now() - startTime,
    error: lastError,
  };
}

/**
 * 简化版本的 CDN URL 验证函数
 * 使用默认配置进行验证
 *
 * @param url - 要验证的 CDN URL
 * @returns Promise<boolean> - 是否有效
 */
export async function isCdnUrlValid(url: string): Promise<boolean> {
  if (!url.startsWith('https://cdn.gooo.ai/')) {
    return true;
  }
  const result = await validateCdnUrl(url);
  return result.isValid;
}
