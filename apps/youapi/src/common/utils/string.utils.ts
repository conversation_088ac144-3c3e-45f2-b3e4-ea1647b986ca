/**
 * String Utilities - 字符串工具函数
 * 从 youapp 迁移的字符串处理工具函数
 *
 * Migrated from:
 * - /youapp/src/lib/util/string.ts
 */

/**
 * 从 URL 中智能提取文件名
 * @param url 完整的 URL 字符串
 * @returns 提取的文件名
 */
export function extractSmartFilename(url: string): string {
  try {
    // 尝试解析 URL
    const urlObj = new URL(url);

    // 获取路径的最后一段
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);
    const lastSegment = pathSegments[pathSegments.length - 1];

    // 如果存在最后一段且看起来像个文件名
    if (lastSegment && /^[\w-]+([.]\w+)?$/.test(lastSegment)) {
      return decodeURIComponent(lastSegment);
    }

    // 如果是 PDF URL 但没有标准文件名，生成一个
    if (url.toLowerCase().includes('.pdf')) {
      const basename = pathSegments.join('-') || urlObj.hostname;
      return `${basename}.pdf`;
    }

    // 降级方案：使用 hostname + pathname 的组合
    return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
  } catch {
    // URL 解析失败时返回原始值
    return url;
  }
}

/**
 * 从文件名提取标题（移除扩展名）
 * @param fileName 文件名
 * @returns 提取的标题
 */
export function extractTitleFromFilename(fileName: string): string {
  if (fileName) {
    // 移除文件扩展名
    return fileName.replace(/\.[^/.]+$/, '');
  }
  return 'Document';
}
