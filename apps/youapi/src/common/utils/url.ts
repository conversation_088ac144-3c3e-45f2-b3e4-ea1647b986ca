import { Request } from 'express';

/**
 * URL utility functions
 * Migrated from youapp/src/lib/utils
 */

/**
 * Check if a URL is a video URL based on common video platforms
 * @param url - URL to check
 * @returns True if the URL is likely a video URL
 */
export function isVideoUrl(url: string): boolean {
  if (!url || typeof url !== 'string') return false;

  const videoPatterns = [
    /youtube\.com\/watch/i,
    /youtu\.be\//i,
    /vimeo\.com\//i,
    /twitch\.tv\//i,
    /dailymotion\.com\//i,
    /wistia\.com\//i,
    /brightcove\.com\//i,
    /\.mp4$/i,
    /\.mov$/i,
    /\.avi$/i,
    /\.mkv$/i,
    /\.webm$/i,
    /\.m4v$/i,
  ];

  return videoPatterns.some((pattern) => pattern.test(url));
}

/**
 * Extract image URLs from markdown content
 * @param markdown - Markdown content to parse
 * @returns Array of image URLs found in the markdown
 */
export function extractImageUrlsFromMarkdown(markdown: string): string[] {
  if (!markdown || typeof markdown !== 'string') return [];

  const imageRegex = /!\[.*?\]\((.*?)\)/g;
  const urls: string[] = [];
  let match: RegExpExecArray | null = imageRegex.exec(markdown);

  while (match !== null) {
    const url = match[1];
    if (url?.trim()) {
      urls.push(url.trim());
    }
    match = imageRegex.exec(markdown);
  }

  return urls;
}

/**
 * Get the origin URL for the application
 * @returns The application's origin URL
 */
export function getOrigin(): string {
  return process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:4000';
}

export function getBaseUrlFromRequest(request: Request): string {
  const protocol = request.get('x-forwarded-proto') || request.protocol;
  const host = request.get('x-forwarded-host') || request.host;
  return `${protocol}://${host}`;
}

/**
 * 检查 URL 是否是 YouMind 分享链接
 * 支持的格式：
 * - youmind-preview-site.vercel.app/[14位ID]
 * - youmind.ai/[14位ID]
 * @param url 要检查的 URL
 * @returns 如果是 YouMind 分享链接则返回匹配信息，否则返回 null
 */
export function checkYouMindShareUrl(url: string): { domain: string; id: string } | null {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    const pathname = urlObj.pathname;

    // 检查域名是否匹配
    const supportedDomains = ['youmind-preview-site.vercel.app', 'youmind.ai'];

    if (!supportedDomains.includes(hostname)) {
      return null;
    }

    // 检查路径格式：应该是 /[14位ID] 或者 /[14位ID]/
    const pathMatch = pathname.match(/^\/([a-zA-Z0-9]{14})\/?$/);
    if (!pathMatch) {
      return null;
    }

    const id = pathMatch[1];

    // 验证 ID 是否正好 14 位
    if (id.length !== 14) {
      return null;
    }

    return {
      domain: hostname,
      id: id,
    };
  } catch (error) {
    // URL 解析失败
    return null;
  }
}

export const safeDecodeURIComponent = (str: string): string => {
  try {
    return decodeURIComponent(str);
  } catch (e) {
    return str;
  }
};
