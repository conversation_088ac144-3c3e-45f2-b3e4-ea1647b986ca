/**
 * String Sanitization Utilities
 * 字符串清理工具函数
 *
 * Migrated from:
 * - /youapp/src/lib/util/server.ts
 */

/**
 * Remove control characters that can cause JSON parsing errors
 * 清理可能导致JSON解析错误的控制字符
 *
 * @param obj - The object to sanitize
 * @returns The sanitized object
 */
export function sanitizeControlCharacters(obj: unknown): unknown {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    // Replace control characters that can cause JSON parsing errors
    // We need to remove control characters (0-31) except for tabs, newlines, and carriage returns
    /* eslint-disable no-control-regex */
    return obj
      .replace(/[\x00-\x08]/g, '') // 0-8
      .replace(/\x0B/g, '') // 11
      .replace(/\x0C/g, '') // 12
      .replace(/[\x0E-\x1F]/g, '') // 14-31
      .replace(/[\x7F-\x9F]/g, ''); // 127-159
    /* eslint-enable no-control-regex */
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizeControlCharacters);
  }

  if (typeof obj === 'object') {
    const sanitized: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj as Record<string, unknown>)) {
      sanitized[key] = sanitizeControlCharacters(value);
    }
    return sanitized;
  }

  return obj;
}
