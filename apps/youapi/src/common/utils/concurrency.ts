/**
 * Concurrency Utilities - 并发控制工具
 * 提供并发执行和错误处理功能
 *
 * Migrated from:
 * - youapp/src/lib/utils.ts
 */

import pLimit from 'p-limit';

/**
 * 使用并发限制执行多个异步函数
 * @param fns 要执行的异步函数数组
 * @param concurrency 并发数限制，默认为3
 * @returns 所有函数执行结果的数组
 */
export async function runConcurrently<F extends () => Promise<any>>(
  fns: F[],
  concurrency = 3,
): Promise<Awaited<ReturnType<F>>[]> {
  const runner = pLimit(concurrency);
  return await Promise.all(fns.map((fn) => runner(fn)));
}

export async function runConcurrentlyWithBatch<F extends () => Promise<any>>(
  fns: F[],
  batchSize = 10,
): Promise<Awaited<ReturnType<F>>[]> {
  const runner = pLimit(batchSize);
  const batches = fns.reduce((acc, fn, index) => {
    const batchIndex = Math.floor(index / batchSize);
    acc[batchIndex] = acc[batchIndex] || [];
    acc[batchIndex].push(fn);
    return acc;
  }, [] as F[][]);

  const results = [];
  for (const batch of batches) {
    const batchResults = await Promise.all(batch.map((fn) => runner(fn)));
    results.push(...batchResults);
  }

  return results;
}

/**
 * 安全执行异步函数，出错时返回默认值
 * @param fn 要执行的异步函数
 * @param fallback 出错时的默认返回值
 * @returns 函数执行结果或默认值
 */
export async function runSafely<R, F extends () => Promise<R>>(fn: F, fallback: R): Promise<R> {
  try {
    return await fn();
  } catch (error) {
    console.warn('runSafely error', fn.name, error);
    return fallback;
  }
}

/**
 * 为异步操作添加超时控制
 * @param promise 要执行的 Promise
 * @param timeoutMs 超时时间（毫秒）
 * @returns 在超时时间内完成的 Promise 结果
 * @throws 超时时抛出 Error
 */
export async function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  const timeout = new Promise<never>((_, reject) =>
    setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs),
  );

  return Promise.race([promise, timeout]);
}
