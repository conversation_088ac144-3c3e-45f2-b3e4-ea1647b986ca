/**
 * Array Utilities - 数组处理工具
 * 提供数组操作相关的工具函数
 *
 * Migrated from:
 * - youapp/src/lib/utils.ts
 */

/**
 * 将数组分割成指定大小的块
 * @param array 要分割的数组
 * @param chunk_size 每个块的大小
 * @returns 分割后的数组块
 */
export function array_chunks<T>(array: Array<T>, chunk_size: number): Array<Array<T>> {
  return Array(Math.ceil(array.length / chunk_size))
    .fill(undefined)
    .map((_, index) => index * chunk_size)
    .map((begin) => array.slice(begin, begin + chunk_size));
}
