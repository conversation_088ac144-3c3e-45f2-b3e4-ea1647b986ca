import { marked } from 'marked';
import striptags from 'striptags';

import { estimateTokens, truncateByTokens } from './token';

const SINGLE_CHUNK_TOKEN_SIZE = 2000;
const SINGLE_CHUNK_TOKEN_SIZE_OVERLAP = 0.5;
const TOTAL_CHUNK_COUNT = 5;

export enum ChunkingStrategy {
  TOTAL_CHUNK_COUNT = 'total_chunk_count',
  SINGLE_CHUNK_TOKEN_SIZE = 'single_chunk_token_size',
  SINGLE_CHUNK_TOKEN_SIZE_OVERLAP = 'single_chunk_token_size_overlap',
  SEMANTIC = 'semantic',
}

// Overload signatures
export async function chunkText(options: {
  content: string;
  chunkingStrategy: ChunkingStrategy.TOTAL_CHUNK_COUNT;
  chunkCount?: number;
}): Promise<Array<{ text: string; chunkIndex: number }>>;

export async function chunkText(options: {
  content: string;
  chunkingStrategy: ChunkingStrategy.SINGLE_CHUNK_TOKEN_SIZE;
  maxTokenPerChunk?: number;
  sanitize?: boolean;
}): Promise<Array<{ text: string; chunkIndex: number }>>;

export async function chunkText(options: {
  content: string;
  chunkingStrategy: ChunkingStrategy.SINGLE_CHUNK_TOKEN_SIZE_OVERLAP;
  maxTokenPerChunk?: number;
  overlapRatio?: number;
}): Promise<Array<{ text: string; chunkIndex: number }>>;

export async function chunkText(options: {
  content: string;
  chunkingStrategy: ChunkingStrategy.SEMANTIC;
}): Promise<Array<{ text: string; chunkIndex: number }>>;

// Implementation
export async function chunkText({
  content,
  chunkCount = TOTAL_CHUNK_COUNT,
  chunkingStrategy = ChunkingStrategy.TOTAL_CHUNK_COUNT,
  maxTokenPerChunk = SINGLE_CHUNK_TOKEN_SIZE,
  overlapRatio = SINGLE_CHUNK_TOKEN_SIZE_OVERLAP,
  sanitize = true,
}: {
  content: string;
  chunkCount?: number;
  chunkingStrategy?: ChunkingStrategy;
  maxTokenPerChunk?: number;
  overlapRatio?: number;
  sanitize?: boolean;
}): Promise<Array<{ text: string; chunkIndex: number }>> {
  if (typeof content !== 'string' || !content) {
    return [];
  }

  if (content.trim() === '') {
    return [];
  }

  // Maximum number of chunks to prevent infinite loop
  const MAX_CHUNKS = 65535;

  let realContent = content;
  if (sanitize) {
    // @fixme remove `data-ym-citation` attributes for it blocks markdown parsing
    // and consequent sanitizing
    const html = await marked.parse(
      content.replace(/<[^>]*data-ym-citation[^>]*>(.*?)<\/[^>]*>/g, (_, content) => content),
    );

    realContent = striptags(html);
  }
  const paragraphs = realContent
    .split('\n')
    .filter((p) => p.trim() !== '')
    .map((p, i) => ({
      text: p,
      chunkIndex: i,
    }));

  if (chunkingStrategy === ChunkingStrategy.TOTAL_CHUNK_COUNT) {
    if (paragraphs.length <= chunkCount) {
      return paragraphs;
    }

    // divide paragraphs into groups
    const chunks = [];
    const groupSize = Math.ceil(paragraphs.length / chunkCount);

    for (let i = 0; i < paragraphs.length && chunks.length < MAX_CHUNKS; i += groupSize) {
      const endIndex = Math.min(i + groupSize, paragraphs.length);
      const group = paragraphs.slice(i, endIndex);

      chunks.push({
        chunkIndex: chunks.length,
        text: group.map((p) => p.text).join('\n'),
      });
    }

    return chunks;
  }

  if (chunkingStrategy === ChunkingStrategy.SINGLE_CHUNK_TOKEN_SIZE) {
    const chunks = [];
    let currentChunkIndex = 0;
    let currentChunk = '';
    let currentParagraphIndex = 0;

    while (currentParagraphIndex < paragraphs.length && chunks.length < MAX_CHUNKS) {
      const paragraph = paragraphs[currentParagraphIndex].text;
      const potentialChunk = currentChunk ? `${currentChunk}\n${paragraph}` : paragraph;

      // Check if adding this paragraph would exceed the token limit
      const potentialChunkTokenCount = estimateTokens(potentialChunk);

      if (potentialChunkTokenCount <= maxTokenPerChunk) {
        // This paragraph fits within the token limit, add it to current chunk
        currentChunk = potentialChunk;
        currentParagraphIndex++;
      } else {
        // Adding this paragraph would exceed the limit
        if (currentChunk) {
          // Save the current chunk if it exists and start a new one
          chunks.push({
            text: currentChunk,
            chunkIndex: currentChunkIndex++,
          });
          currentChunk = '';
        } else {
          // Single paragraph exceeds token limit, need to truncate it
          const truncatedParagraph = truncateByTokens(paragraph, {
            maxTokens: maxTokenPerChunk,
            suffix: '',
            preserveWords: true,
          });

          chunks.push({
            text: truncatedParagraph,
            chunkIndex: currentChunkIndex++,
          });

          // Handle remaining text from the truncated paragraph
          const remainingText = paragraph.slice(truncatedParagraph.length).trim();
          if (remainingText) {
            paragraphs.splice(currentParagraphIndex + 1, 0, {
              text: remainingText,
              chunkIndex: paragraphs[currentParagraphIndex].chunkIndex,
            });
          }
          currentParagraphIndex++;
        }
      }
    }

    // Don't forget to add the last chunk if it exists
    if (currentChunk) {
      chunks.push({
        text: currentChunk,
        chunkIndex: currentChunkIndex,
      });
    }

    return chunks;
  }

  if (chunkingStrategy === ChunkingStrategy.SINGLE_CHUNK_TOKEN_SIZE_OVERLAP) {
    throw new Error(
      'Not implemented strategy: ' + chunkingStrategy + ' overlapRatio: ' + overlapRatio,
    );
  }

  if (chunkingStrategy === ChunkingStrategy.SEMANTIC) {
    throw new Error('Not implemented strategy: ' + chunkingStrategy);
  }

  throw new Error(`Unsupported chunking strategy: ${chunkingStrategy}`);
}
