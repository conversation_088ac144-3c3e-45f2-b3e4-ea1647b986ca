/**
 * Webpage parsing utility functions
 * Migrated from youapp/src/lib/utils
 */

/**
 * Parse webpage metadata from HTML content
 * @param url - The URL of the webpage
 * @param html - The HTML content to parse
 * @returns Parsed webpage metadata
 */
export function parseWebpageMetaFromHtml(
  url: string,
  html: string,
): {
  url: string;
  normalized_url: string;
  title: string;
  description: string;
  site: {
    name: string;
    host: string;
    favicon_url: string;
  };
} {
  try {
    const urlObj = new URL(url);

    // Extract title
    const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i);
    let title = titleMatch ? titleMatch[1].trim() : '';

    // If no title found, try og:title
    if (!title) {
      const ogTitleMatch = html.match(
        /<meta[^>]*property\s*=\s*["']og:title["'][^>]*content\s*=\s*["']([^"']*)["']/i,
      );
      title = ogTitleMatch ? ogTitleMatch[1].trim() : '';
    }

    // Fallback to hostname
    if (!title) {
      title = urlObj.hostname;
    }

    // Extract description
    let description = '';
    const descMatch = html.match(
      /<meta[^>]*name\s*=\s*["']description["'][^>]*content\s*=\s*["']([^"']*)["']/i,
    );
    if (descMatch) {
      description = descMatch[1].trim();
    } else {
      // Try og:description
      const ogDescMatch = html.match(
        /<meta[^>]*property\s*=\s*["']og:description["'][^>]*content\s*=\s*["']([^"']*)["']/i,
      );
      description = ogDescMatch ? ogDescMatch[1].trim() : '';
    }

    // Extract site name
    let siteName = '';
    const siteNameMatch = html.match(
      /<meta[^>]*property\s*=\s*["']og:site_name["'][^>]*content\s*=\s*["']([^"']*)["']/i,
    );
    if (siteNameMatch) {
      siteName = siteNameMatch[1].trim();
    } else {
      siteName = urlObj.hostname;
    }

    // Extract favicon
    let faviconUrl = '';
    const faviconMatch = html.match(
      /<link[^>]*rel\s*=\s*["'](?:icon|shortcut icon)["'][^>]*href\s*=\s*["']([^"']*)["']/i,
    );
    if (faviconMatch) {
      const href = faviconMatch[1].trim();
      if (href.startsWith('http')) {
        faviconUrl = href;
      } else if (href.startsWith('//')) {
        faviconUrl = urlObj.protocol + href;
      } else if (href.startsWith('/')) {
        faviconUrl = urlObj.origin + href;
      } else {
        faviconUrl = urlObj.origin + '/' + href;
      }
    } else {
      // Default favicon location
      faviconUrl = urlObj.origin + '/favicon.ico';
    }

    return {
      url,
      normalized_url: url, // Will be normalized by the caller
      title: title || urlObj.hostname,
      description: description || '',
      site: {
        name: siteName || urlObj.hostname,
        host: urlObj.hostname,
        favicon_url: faviconUrl,
      },
    };
  } catch (error) {
    console.warn('Error parsing webpage metadata:', error);

    // Fallback metadata
    const urlObj = new URL(url);
    return {
      url,
      normalized_url: url,
      title: urlObj.hostname,
      description: '',
      site: {
        name: urlObj.hostname,
        host: urlObj.hostname,
        favicon_url: urlObj.origin + '/favicon.ico',
      },
    };
  }
}
