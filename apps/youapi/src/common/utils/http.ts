/**
 * HTTP Utilities - HTTP请求工具
 * 提供带超时和重试的HTTP请求功能
 *
 * Migrated from:
 * - youapp/src/lib/common/utils.ts
 */

const FAKE_USER_AGENT =
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36';

/**
 * 带超时的 fetch 请求
 *
 * @param input 请求的 URL 或 Request 对象
 * @param init 请求选项，包含可选的 timeout 参数
 * @returns Promise<Response>
 */
export async function fetchWithTimeout(
  input: string | URL | globalThis.Request,
  init?: RequestInit & { timeout?: number },
): Promise<Response> {
  const timeout = init?.timeout ?? 3000;
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(input, {
      ...init,
      headers: {
        ...init?.headers,
        'User-Agent': FAKE_USER_AGENT,
      },
      signal: controller.signal,
    });
    clearTimeout(id);
    return response;
  } catch (error) {
    clearTimeout(id);
    throw error;
  }
}

/**
 * 带重试机制的 fetch 请求
 *
 * @param input 请求的 URL 或 Request 对象
 * @param init 请求选项，包含可选的 timeout 和 retries 参数
 * @returns Promise<Response>
 */
export async function fetchWithRetry(
  input: string | URL | globalThis.Request,
  init?: RequestInit & {
    timeout?: number;
    retries?: number;
    retryDelay?: number;
  },
): Promise<Response> {
  const retries = init?.retries ?? 3;
  const retryDelay = init?.retryDelay ?? 1000;
  let lastError: Error;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await fetchWithTimeout(input, init);

      // If the response is successful or a client error (4xx), don't retry
      if (response.ok || (response.status >= 400 && response.status < 500)) {
        return response;
      }

      // For server errors (5xx), we should retry
      if (attempt === retries) {
        return response; // Return the last response if we've exhausted retries
      }

      lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      // If this is the last attempt, throw the error
      if (attempt === retries) {
        throw lastError;
      }
    }

    // Wait before retrying (exponential backoff)
    if (attempt < retries) {
      await delay(retryDelay * 2 ** attempt);
    }
  }

  throw lastError!;
}

/**
 * 延时函数
 *
 * @param ms 延时毫秒数
 * @returns Promise<void>
 */
export async function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Sleep 函数 (delay 的别名)
 *
 * @param ms 延时毫秒数
 * @returns Promise<void>
 */
export const sleep = delay;
