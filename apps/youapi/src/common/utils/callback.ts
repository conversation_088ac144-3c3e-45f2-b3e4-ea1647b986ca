/**
 * Callback utility functions
 * Migrated from youapp/src/lib/utils
 */

/**
 * Get the callback origin URL for webhooks
 * @returns The callback origin URL based on environment
 */
export function getCallbackOrigin(): string {
  // Check if we're in production
  if (process.env.NODE_ENV === 'production') {
    return process.env.PRODUCTION_CALLBACK_ORIGIN || 'https://api.youmind.com';
  }

  // Check if we're in preview/staging
  if (process.env.NODE_ENV === 'staging' || process.env.NODE_ENV === 'preview') {
    return process.env.PREVIEW_CALLBACK_ORIGIN || 'https://api-preview.youmind.com';
  }

  // Development/local environment
  return process.env.LOCAL_CALLBACK_ORIGIN || 'http://localhost:4000';
}
