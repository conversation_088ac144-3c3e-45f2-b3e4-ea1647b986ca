import {
  FilePart,
  ImagePart,
  TextPart,
  TextStreamPart,
  ToolCallPart,
  ToolResultPart,
  ToolSet,
} from 'ai';

export enum ProviderCapabilityEnum {
  TEXT = 'text',
  IMAGE = 'image',
  SPEECH = 'speech',
  EMBEDDING = 'embedding',
}

export enum GenerationTypeEnum {
  TEXT = 'text',
  IMAGE = 'image',
  SPEECH = 'speech',
  OBJECT = 'object',
  EMBEDDING = 'embedding',
}

export enum GenerationStatusEnum {
  PENDING = 'pending',
  GENERATING = 'generating',
  SUCCESS = 'success',
  FAILED = 'failed',
  WAITING_RETRY = 'waiting_retry',
}

export type AIMessageStream = TextStreamPart<ToolSet>;

/**
 * Array-only content types based on AI SDK but without string alternatives
 * These interfaces mirror the AI SDK types but enforce array content for consistency
 *
 * Benefits:
 * - Consistent array content across all message types
 * - Simplified processing without role-specific content handling
 * - Future-proof for adding non-text content types (images, files, tools)
 * - Maintains compatibility with AI SDK through conversion
 */

export type ArraySystemContent = Array<TextPart>;

export type ArrayUserContent = Array<TextPart | ImagePart | FilePart>;

export type ArrayAssistantContent = Array<TextPart | ToolCallPart | ToolResultPart>;

export type ArrayToolContent = Array<ToolResultPart>;

/**
 * Message interfaces with array-only content
 */
export interface ArraySystemMessage {
  role: 'system';
  content: ArraySystemContent;
}

export interface ArrayUserMessage {
  role: 'user';
  content: ArrayUserContent;
}

export interface ArrayAssistantMessage {
  role: 'assistant';
  content: ArrayAssistantContent;
}

export interface ArrayToolMessage {
  role: 'tool';
  content: ArrayToolContent;
}

/**
 * Union type for all array-only messages
 * Use this type for consistent internal message processing
 */
export type ArrayModelMessage =
  | ArraySystemMessage
  | ArrayUserMessage
  | ArrayAssistantMessage
  | ArrayToolMessage;
