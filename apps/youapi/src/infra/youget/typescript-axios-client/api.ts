/* tslint:disable */
/* eslint-disable */
/**
 * youget API
 * the scrapping system for YouMind
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { AxiosInstance, AxiosPromise, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, BaseAPI, operationServerMap } from './base';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  assertParamExists,
  createRequestFunction,
  DUMMY_BASE_URL,
  serializeDataIfNeeded,
  setSearchParams,
  toPathString,
} from './common';
import type { Configuration } from './configuration';

/**
 *
 * @export
 * @interface ApiV***********************************
 */
export interface ApiV*********************************** {
  /**
   * Azure transcription ID
   * @type {string}
   * @memberof ApiV***********************************
   */
  transcription_id?: string;
  /**
   * Azure transcription URL
   * @type {string}
   * @memberof ApiV***********************************
   */
  transcription_url?: string;
}
/**
 *
 * @export
 * @interface ApiV1DashboardTasksGet200Response
 */
export interface ApiV1DashboardTasksGet200Response {
  /**
   *
   * @type {Array<ApiV1TasksGet200ResponseInner>}
   * @memberof ApiV1DashboardTasksGet200Response
   */
  data: Array<ApiV1TasksGet200ResponseInner>;
  /**
   *
   * @type {ApiV1DashboardTasksGet200ResponsePagination}
   * @memberof ApiV1DashboardTasksGet200Response
   */
  pagination: ApiV1DashboardTasksGet200ResponsePagination;
}
/**
 *
 * @export
 * @interface ApiV1DashboardTasksGet200ResponsePagination
 */
export interface ApiV1DashboardTasksGet200ResponsePagination {
  /**
   *
   * @type {number}
   * @memberof ApiV1DashboardTasksGet200ResponsePagination
   */
  current_page: number;
  /**
   *
   * @type {number}
   * @memberof ApiV1DashboardTasksGet200ResponsePagination
   */
  per_page: number;
  /**
   *
   * @type {number}
   * @memberof ApiV1DashboardTasksGet200ResponsePagination
   */
  total: number;
  /**
   *
   * @type {number}
   * @memberof ApiV1DashboardTasksGet200ResponsePagination
   */
  total_pages: number;
}
/**
 *
 * @export
 * @interface ApiV1DashboardTasksStatsGet200Response
 */
export interface ApiV1DashboardTasksStatsGet200Response {
  /**
   *
   * @type {Array<ApiV1DashboardTasksStatsGet200ResponseStatusCountsInner>}
   * @memberof ApiV1DashboardTasksStatsGet200Response
   */
  status_counts: Array<ApiV1DashboardTasksStatsGet200ResponseStatusCountsInner>;
  /**
   *
   * @type {Array<ApiV1DashboardTasksStatsGet200ResponseTypeCountsInner>}
   * @memberof ApiV1DashboardTasksStatsGet200Response
   */
  type_counts: Array<ApiV1DashboardTasksStatsGet200ResponseTypeCountsInner>;
}
/**
 *
 * @export
 * @interface ApiV1DashboardTasksStatsGet200ResponseStatusCountsInner
 */
export interface ApiV1DashboardTasksStatsGet200ResponseStatusCountsInner {
  /**
   *
   * @type {string}
   * @memberof ApiV1DashboardTasksStatsGet200ResponseStatusCountsInner
   */
  status: string;
  /**
   *
   * @type {number}
   * @memberof ApiV1DashboardTasksStatsGet200ResponseStatusCountsInner
   */
  count: number;
}
/**
 *
 * @export
 * @interface ApiV1DashboardTasksStatsGet200ResponseTypeCountsInner
 */
export interface ApiV1DashboardTasksStatsGet200ResponseTypeCountsInner {
  /**
   *
   * @type {string}
   * @memberof ApiV1DashboardTasksStatsGet200ResponseTypeCountsInner
   */
  type: string;
  /**
   *
   * @type {number}
   * @memberof ApiV1DashboardTasksStatsGet200ResponseTypeCountsInner
   */
  count: number;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponse
 */
export interface ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponse
   */
  id?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponse
   */
  task_url?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponse
   */
  message: string;
  /**
   *
   * @type {boolean}
   * @memberof ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponse
   */
  error?: boolean;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult}
   * @memberof ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponse
   */
  result?: ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult
 */
export interface ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult {
  /**
   * 提取出的文本
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult
   */
  text: string;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunSaveImageTaskPostDefaultResponse
 */
export interface ApiV1ShortcutCreateAndRunSaveImageTaskPostDefaultResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunSaveImageTaskPostDefaultResponse
   */
  id?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunSaveImageTaskPostDefaultResponse
   */
  task_url?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunSaveImageTaskPostDefaultResponse
   */
  message: string;
  /**
   *
   * @type {boolean}
   * @memberof ApiV1ShortcutCreateAndRunSaveImageTaskPostDefaultResponse
   */
  error?: boolean;
  /**
   *
   * @type {Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>}
   * @memberof ApiV1ShortcutCreateAndRunSaveImageTaskPostDefaultResponse
   */
  result?: Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse
 */
export interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse
   */
  id?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse
   */
  task_url?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse
   */
  message: string;
  /**
   *
   * @type {boolean}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse
   */
  error?: boolean;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse
   */
  result?: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
 */
export interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  from: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFromEnum;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  type: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultTypeEnum;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  title: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  published_at: string | null;
  /**
   *
   * @type {Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultAuthorsInner>}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  authors?: Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultAuthorsInner>;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  hero_image_url?: string;
  /**
   *
   * @type {Array<string>}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  author_names?: Array<string>;
  /**
   *
   * @type {Array<string>}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  author_pictures?: Array<string>;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpage}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  webpage: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpage;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  play_url?: string;
  /**
   *
   * @type {Array<ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent>}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  transcript?: Array<ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent>;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  content?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  show_notes?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  description?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  extra?: string;
  /**
   *
   * @type {Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   */
  files: Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>;
}

export const ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFromEnum = {
  Webpage: 'webpage',
} as const;

export type ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFromEnum =
  (typeof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFromEnum)[keyof typeof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFromEnum];
export const ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultTypeEnum = {
  Article: 'article',
  Voice: 'voice',
  Video: 'video',
  OtherWebpage: 'other-webpage',
  Pdf: 'pdf',
} as const;

export type ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultTypeEnum =
  (typeof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultTypeEnum)[keyof typeof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultTypeEnum];

/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultAuthorsInner
 */
export interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultAuthorsInner {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultAuthorsInner
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultAuthorsInner
   */
  picture?: string;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
 */
export interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner {
  /**
   * 原始文件地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
   */
  original_url: string;
  /**
   * 文件地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
   */
  url?: string;
  /**
   * 文件名
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
   */
  name?: string;
  /**
   * 文件 MIME 类型
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
   */
  mime_type?: string;
  /**
   * 文件大小, Bytes
   * @type {number}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
   */
  size?: number;
  /**
   * 模糊哈希值
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
   */
  blurhash?: string;
  /**
   * 宽度
   * @type {number}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
   */
  width?: number;
  /**
   * 高度
   * @type {number}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
   */
  height?: number;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInnerAverage}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner
   */
  average?: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInnerAverage;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInnerAverage
 */
export interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInnerAverage {
  /**
   * 红色通道平均值
   * @type {number}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInnerAverage
   */
  r?: number;
  /**
   * 绿色通道平均值
   * @type {number}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInnerAverage
   */
  g?: number;
  /**
   * 蓝色通道平均值
   * @type {number}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInnerAverage
   */
  b?: number;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpage
 */
export interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpage {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpage
   */
  url: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpage
   */
  title: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpage
   */
  description: string;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpageSite}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpage
   */
  site: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpageSite;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpageSite
 */
export interface ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpageSite {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpageSite
   */
  favicon_url: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpageSite
   */
  host: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultWebpageSite
   */
  name: string;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponse
 */
export interface ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponse
   */
  id?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponse
   */
  task_url?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponse
   */
  message: string;
  /**
   *
   * @type {boolean}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponse
   */
  error?: boolean;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponse
   */
  result?: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
 */
export interface ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
   */
  task: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResultTaskEnum;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
   */
  language: string;
  /**
   *
   * @type {number}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
   */
  duration: number;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
   */
  text: string;
  /**
   *
   * @type {Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner>}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
   */
  segments: Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner>;
  /**
   *
   * @type {Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner>}
   * @memberof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
   */
  words?: Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner> | null;
}

export const ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResultTaskEnum = {
  Transcribe: 'transcribe',
} as const;

export type ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResultTaskEnum =
  (typeof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResultTaskEnum)[keyof typeof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResultTaskEnum];

/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse
 */
export interface ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse
   */
  id?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse
   */
  task_url?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse
   */
  message: string;
  /**
   *
   * @type {boolean}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse
   */
  error?: boolean;
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse
   */
  result?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult
 */
export interface ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult {
  /**
   *
   * @type {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult
   */
  content?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent
 */
export interface ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent
   */
  format: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentFormatEnum;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent
   */
  raw: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent
   */
  plain: string;
  /**
   * Plain text content, without any formatting
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent
   */
  text?: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContent
   */
  language?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentLanguageEnum;
}

export const ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentFormatEnum =
  {
    ReaderHtml: 'reader-html',
    Richtext: 'richtext',
    Subtitle: 'subtitle',
    Messages: 'messages',
    LlmOutput: 'llm-output',
  } as const;

export type ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentFormatEnum =
  (typeof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentFormatEnum)[keyof typeof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentFormatEnum];
export const ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentLanguageEnum =
  {
    Am: 'am',
    Ar: 'ar',
    BgBg: 'bg-BG',
    BnBd: 'bn-BD',
    BsBa: 'bs-BA',
    CaEs: 'ca-ES',
    CsCz: 'cs-CZ',
    DaDk: 'da-DK',
    DeDe: 'de-DE',
    ElGr: 'el-GR',
    EnUs: 'en-US',
    Es419: 'es-419',
    EsEs: 'es-ES',
    EtEe: 'et-EE',
    FiFi: 'fi-FI',
    FrCa: 'fr-CA',
    FrFr: 'fr-FR',
    GuIn: 'gu-IN',
    HiIn: 'hi-IN',
    HrHr: 'hr-HR',
    HuHu: 'hu-HU',
    HyAm: 'hy-AM',
    IdId: 'id-ID',
    IsIs: 'is-IS',
    ItIt: 'it-IT',
    JaJp: 'ja-JP',
    KaGe: 'ka-GE',
    Kk: 'kk',
    KnIn: 'kn-IN',
    KoKr: 'ko-KR',
    Lt: 'lt',
    LvLv: 'lv-LV',
    MkMk: 'mk-MK',
    Ml: 'ml',
    Mn: 'mn',
    MrIn: 'mr-IN',
    MsMy: 'ms-MY',
    MyMm: 'my-MM',
    NbNo: 'nb-NO',
    NlNl: 'nl-NL',
    Pa: 'pa',
    PlPl: 'pl-PL',
    PtBr: 'pt-BR',
    PtPt: 'pt-PT',
    RoRo: 'ro-RO',
    RuRu: 'ru-RU',
    SkSk: 'sk-SK',
    SlSi: 'sl-SI',
    SoSo: 'so-SO',
    SqAl: 'sq-AL',
    SrRs: 'sr-RS',
    SvSe: 'sv-SE',
    SwTz: 'sw-TZ',
    TaIn: 'ta-IN',
    TeIn: 'te-IN',
    ThTh: 'th-TH',
    Tl: 'tl',
    TrTr: 'tr-TR',
    UkUa: 'uk-UA',
    Ur: 'ur',
    ViVn: 'vi-VN',
    ZhCn: 'zh-CN',
    ZhHk: 'zh-HK',
    ZhTw: 'zh-TW',
  } as const;

export type ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentLanguageEnum =
  (typeof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentLanguageEnum)[keyof typeof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResultContentLanguageEnum];

/**
 *
 * @export
 * @interface ApiV1ShortcutCreateExtractTextTaskPostRequest
 */
export interface ApiV1ShortcutCreateExtractTextTaskPostRequest {
  /**
   * 任务完成时的回调地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateExtractTextTaskPostRequest
   */
  youget_callback_url?: string;
  /**
   * 任务完成时的回调 HTTP 方法
   * @type {string}
   * @memberof ApiV1ShortcutCreateExtractTextTaskPostRequest
   */
  youget_callback_method?: string;
  /**
   *
   * @type {ApiV1ShortcutCreateExtractTextTaskPostRequestFile}
   * @memberof ApiV1ShortcutCreateExtractTextTaskPostRequest
   */
  file: ApiV1ShortcutCreateExtractTextTaskPostRequestFile;
  /**
   * 是否自动调整图片以满足 Azure OCR 服务要求（包括格式转换、压缩和调整大小），会增加处理时间
   * @type {boolean}
   * @memberof ApiV1ShortcutCreateExtractTextTaskPostRequest
   */
  adaptImage?: boolean;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateExtractTextTaskPostRequestFile
 */
export interface ApiV1ShortcutCreateExtractTextTaskPostRequestFile {
  /**
   * 需要提取文本的文件地址，支持 PDF、JPEG、PNG 等格式
   * @type {string}
   * @memberof ApiV1ShortcutCreateExtractTextTaskPostRequestFile
   */
  url: string;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateSaveImageTaskPostRequest
 */
export interface ApiV1ShortcutCreateSaveImageTaskPostRequest {
  /**
   * 任务完成时的回调地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateSaveImageTaskPostRequest
   */
  youget_callback_url?: string;
  /**
   * 任务完成时的回调 HTTP 方法
   * @type {string}
   * @memberof ApiV1ShortcutCreateSaveImageTaskPostRequest
   */
  youget_callback_method?: string;
  /**
   * 需要保存的图片 URL 列表
   * @type {Array<string>}
   * @memberof ApiV1ShortcutCreateSaveImageTaskPostRequest
   */
  image_urls: Array<string>;
  /**
   * 是否忽略失败的 URL
   * @type {boolean}
   * @memberof ApiV1ShortcutCreateSaveImageTaskPostRequest
   */
  ignore_failed_url?: boolean;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateScrapPageTaskPostRequest
 */
export interface ApiV1ShortcutCreateScrapPageTaskPostRequest {
  /**
   * 任务完成时的回调地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateScrapPageTaskPostRequest
   */
  youget_callback_url?: string;
  /**
   * 任务完成时的回调 HTTP 方法
   * @type {string}
   * @memberof ApiV1ShortcutCreateScrapPageTaskPostRequest
   */
  youget_callback_method?: string;
  /**
   * 需要抓取的目标网页地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateScrapPageTaskPostRequest
   */
  target_url: string;
  /**
   * 是否同时转存图片
   * @type {boolean}
   * @memberof ApiV1ShortcutCreateScrapPageTaskPostRequest
   */
  save_image?: boolean;
  /**
   * 使用 Jina 或默认服务抓取页面
   * @type {string}
   * @memberof ApiV1ShortcutCreateScrapPageTaskPostRequest
   */
  scrape_service?: string;
  /**
   * 预获取的HTML内容，如果提供则跳过抓取阶段直接处理
   * @type {string}
   * @memberof ApiV1ShortcutCreateScrapPageTaskPostRequest
   */
  html?: string;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateTranscribeMediaTaskPostRequest
 */
export interface ApiV1ShortcutCreateTranscribeMediaTaskPostRequest {
  /**
   * 任务完成时的回调地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribeMediaTaskPostRequest
   */
  youget_callback_url?: string;
  /**
   * 任务完成时的回调 HTTP 方法
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribeMediaTaskPostRequest
   */
  youget_callback_method?: string;
  /**
   *
   * @type {ApiV1ShortcutCreateTranscribeMediaTaskPostRequestFile}
   * @memberof ApiV1ShortcutCreateTranscribeMediaTaskPostRequest
   */
  file: ApiV1ShortcutCreateTranscribeMediaTaskPostRequestFile;
  /**
   * 播客的 show notes 或视频的 description, 用于提取关键字辅助转录
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribeMediaTaskPostRequest
   */
  show_notes?: string;
  /**
   * 媒体语言
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribeMediaTaskPostRequest
   */
  language?: string;
  /**
   * 是否需要提取 speaker 标签
   * @type {boolean}
   * @memberof ApiV1ShortcutCreateTranscribeMediaTaskPostRequest
   */
  speaker_labels?: boolean;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateTranscribeMediaTaskPostRequestFile
 */
export interface ApiV1ShortcutCreateTranscribeMediaTaskPostRequestFile {
  /**
   * 需要转录的媒体文件地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribeMediaTaskPostRequestFile
   */
  url: string;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest
 */
export interface ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest {
  /**
   * 任务完成时的回调地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest
   */
  youget_callback_url?: string;
  /**
   * 任务完成时的回调 HTTP 方法
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest
   */
  youget_callback_method?: string;
  /**
   *
   * @type {ApiV1ShortcutCreateTranscribeOfficeTaskPostRequestFile}
   * @memberof ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest
   */
  file: ApiV1ShortcutCreateTranscribeOfficeTaskPostRequestFile;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateTranscribeOfficeTaskPostRequestFile
 */
export interface ApiV1ShortcutCreateTranscribeOfficeTaskPostRequestFile {
  /**
   * 需要解析的 Office 文档文件地址 (Word .docx, PowerPoint .pptx, Excel .xlsx)
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribeOfficeTaskPostRequestFile
   */
  url: string;
  /**
   * 文件的 MIME 类型 (如果 URL 中没有扩展名，请指定此字段)
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribeOfficeTaskPostRequestFile
   */
  mime_type?: string;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateTranscribePdfTaskPostRequest
 */
export interface ApiV1ShortcutCreateTranscribePdfTaskPostRequest {
  /**
   * 任务完成时的回调地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribePdfTaskPostRequest
   */
  youget_callback_url?: string;
  /**
   * 任务完成时的回调 HTTP 方法
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribePdfTaskPostRequest
   */
  youget_callback_method?: string;
  /**
   *
   * @type {ApiV1ShortcutCreateTranscribePdfTaskPostRequestFile}
   * @memberof ApiV1ShortcutCreateTranscribePdfTaskPostRequest
   */
  file: ApiV1ShortcutCreateTranscribePdfTaskPostRequestFile;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutCreateTranscribePdfTaskPostRequestFile
 */
export interface ApiV1ShortcutCreateTranscribePdfTaskPostRequestFile {
  /**
   * 需要解析的 PDF 文件地址
   * @type {string}
   * @memberof ApiV1ShortcutCreateTranscribePdfTaskPostRequestFile
   */
  url: string;
}
/**
 *
 * @export
 * @interface ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse
 */
export interface ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse
   */
  message: string;
  /**
   *
   * @type {boolean}
   * @memberof ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse
   */
  error?: boolean;
  /**
   *
   * @type {string}
   * @memberof ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse
   */
  task_url?: string;
}
/**
 *
 * @export
 * @interface ApiV1TasksGet200ResponseInner
 */
export interface ApiV1TasksGet200ResponseInner {
  /**
   * 任务 id
   * @type {string}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  id: string;
  /**
   * 任务类型
   * @type {string}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  type: ApiV1TasksGet200ResponseInnerTypeEnum;
  /**
   * 任务状态
   * @type {string}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  status: ApiV1TasksGet200ResponseInnerStatusEnum;
  /**
   * 创建者 id
   * @type {string}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  creator_id: string;
  /**
   * 创建时间
   * @type {string}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  created_at: string;
  /**
   * 更新时间
   * @type {string}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  updated_at: string;
  /**
   * 重试次数
   * @type {number}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  retry_times?: number;
  /**
   * 任务参数, json
   * @type {{ [key: string]: any; }}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  payload: { [key: string]: any };
  /**
   * 缓存 key
   * @type {string}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  cache_key?: string;
  /**
   * 执行环境
   * @type {string}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  exec_env?: ApiV1TasksGet200ResponseInnerExecEnvEnum;
  /**
   * 任务元数据
   * @type {{ [key: string]: any; }}
   * @memberof ApiV1TasksGet200ResponseInner
   */
  metadata?: { [key: string]: any };
}

export const ApiV1TasksGet200ResponseInnerTypeEnum = {
  SaveImage: 'save_image',
  ScrapPage: 'scrap_page',
  TranscribeMedia: 'transcribe_media',
  TranscribePdf: 'transcribe_pdf',
  TranscribeOffice: 'transcribe_office',
  ExtractText: 'extract_text',
} as const;

export type ApiV1TasksGet200ResponseInnerTypeEnum =
  (typeof ApiV1TasksGet200ResponseInnerTypeEnum)[keyof typeof ApiV1TasksGet200ResponseInnerTypeEnum];
export const ApiV1TasksGet200ResponseInnerStatusEnum = {
  Pending: 'pending',
  Running: 'running',
  Paused: 'paused',
  Failed: 'failed',
  FailedCanRetry: 'failed_can_retry',
  Completed: 'completed',
  Timeout: 'timeout',
} as const;

export type ApiV1TasksGet200ResponseInnerStatusEnum =
  (typeof ApiV1TasksGet200ResponseInnerStatusEnum)[keyof typeof ApiV1TasksGet200ResponseInnerStatusEnum];
export const ApiV1TasksGet200ResponseInnerExecEnvEnum = {
  AwsLambda: 'aws_lambda',
  VercelFunction: 'vercel_function',
  Local: 'local',
  Unknown: 'unknown',
} as const;

export type ApiV1TasksGet200ResponseInnerExecEnvEnum =
  (typeof ApiV1TasksGet200ResponseInnerExecEnvEnum)[keyof typeof ApiV1TasksGet200ResponseInnerExecEnvEnum];

/**
 *
 * @export
 * @interface ApiV1TasksIdGet200Response
 */
export interface ApiV1TasksIdGet200Response {
  /**
   * 任务 id
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  id: string;
  /**
   * 任务类型
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  type: ApiV1TasksIdGet200ResponseTypeEnum;
  /**
   * 任务状态
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  status: ApiV1TasksIdGet200ResponseStatusEnum;
  /**
   * 创建者 id
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  creator_id: string;
  /**
   * 创建时间
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  created_at: string;
  /**
   * 更新时间
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  updated_at: string;
  /**
   * 重试次数
   * @type {number}
   * @memberof ApiV1TasksIdGet200Response
   */
  retry_times?: number;
  /**
   * 任务参数, json
   * @type {{ [key: string]: any; }}
   * @memberof ApiV1TasksIdGet200Response
   */
  payload: { [key: string]: any };
  /**
   * 日志
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  log?: string;
  /**
   * 任务结果
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  result?: string;
  /**
   * 缓存 key
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  cache_key?: string;
  /**
   * 执行环境
   * @type {string}
   * @memberof ApiV1TasksIdGet200Response
   */
  exec_env?: ApiV1TasksIdGet200ResponseExecEnvEnum;
  /**
   * 任务元数据
   * @type {{ [key: string]: any; }}
   * @memberof ApiV1TasksIdGet200Response
   */
  metadata?: { [key: string]: any };
}

export const ApiV1TasksIdGet200ResponseTypeEnum = {
  SaveImage: 'save_image',
  ScrapPage: 'scrap_page',
  TranscribeMedia: 'transcribe_media',
  TranscribePdf: 'transcribe_pdf',
  TranscribeOffice: 'transcribe_office',
  ExtractText: 'extract_text',
} as const;

export type ApiV1TasksIdGet200ResponseTypeEnum =
  (typeof ApiV1TasksIdGet200ResponseTypeEnum)[keyof typeof ApiV1TasksIdGet200ResponseTypeEnum];
export const ApiV1TasksIdGet200ResponseStatusEnum = {
  Pending: 'pending',
  Running: 'running',
  Paused: 'paused',
  Failed: 'failed',
  FailedCanRetry: 'failed_can_retry',
  Completed: 'completed',
  Timeout: 'timeout',
} as const;

export type ApiV1TasksIdGet200ResponseStatusEnum =
  (typeof ApiV1TasksIdGet200ResponseStatusEnum)[keyof typeof ApiV1TasksIdGet200ResponseStatusEnum];
export const ApiV1TasksIdGet200ResponseExecEnvEnum = {
  AwsLambda: 'aws_lambda',
  VercelFunction: 'vercel_function',
  Local: 'local',
  Unknown: 'unknown',
} as const;

export type ApiV1TasksIdGet200ResponseExecEnvEnum =
  (typeof ApiV1TasksIdGet200ResponseExecEnvEnum)[keyof typeof ApiV1TasksIdGet200ResponseExecEnvEnum];

/**
 *
 * @export
 * @interface ApiV1TasksIdGet4XXResponse
 */
export interface ApiV1TasksIdGet4XXResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdGet4XXResponse
   */
  message: string;
}
/**
 *
 * @export
 * @interface ApiV1TasksIdPostDefaultResponse
 */
export interface ApiV1TasksIdPostDefaultResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdPostDefaultResponse
   */
  message: string;
  /**
   *
   * @type {boolean}
   * @memberof ApiV1TasksIdPostDefaultResponse
   */
  error?: boolean;
}
/**
 *
 * @export
 * @interface ApiV1TasksIdPutRequest
 */
export interface ApiV1TasksIdPutRequest {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdPutRequest
   */
  created_at: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdPutRequest
   */
  status: ApiV1TasksIdPutRequestStatusEnum;
}

export const ApiV1TasksIdPutRequestStatusEnum = {
  Pending: 'pending',
  Running: 'running',
  Paused: 'paused',
  Completed: 'completed',
  Failed: 'failed',
  FailedCanRetry: 'failed_can_retry',
  Timeout: 'timeout',
} as const;

export type ApiV1TasksIdPutRequestStatusEnum =
  (typeof ApiV1TasksIdPutRequestStatusEnum)[keyof typeof ApiV1TasksIdPutRequestStatusEnum];

/**
 *
 * @export
 * @interface ApiV1TasksIdResumeTranscribeMediaPostDefaultResponse
 */
export interface ApiV1TasksIdResumeTranscribeMediaPostDefaultResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostDefaultResponse
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostDefaultResponse
   */
  message: string;
}
/**
 *
 * @export
 * @interface ApiV1TasksIdResumeTranscribeMediaPostRequest
 */
export interface ApiV1TasksIdResumeTranscribeMediaPostRequest {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequest
   */
  task?: ApiV1TasksIdResumeTranscribeMediaPostRequestTaskEnum;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequest
   */
  language?: string;
  /**
   *
   * @type {number}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequest
   */
  duration?: number;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequest
   */
  text?: string;
  /**
   *
   * @type {Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner>}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequest
   */
  segments?: Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner>;
  /**
   *
   * @type {Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner>}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequest
   */
  words?: Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner> | null;
}

export const ApiV1TasksIdResumeTranscribeMediaPostRequestTaskEnum = {
  Transcribe: 'transcribe',
} as const;

export type ApiV1TasksIdResumeTranscribeMediaPostRequestTaskEnum =
  (typeof ApiV1TasksIdResumeTranscribeMediaPostRequestTaskEnum)[keyof typeof ApiV1TasksIdResumeTranscribeMediaPostRequestTaskEnum];

/**
 *
 * @export
 * @interface ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOf
 */
export interface ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOf {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOf
   */
  task?: ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfTaskEnum;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOf
   */
  language?: string;
  /**
   *
   * @type {number}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOf
   */
  duration?: number;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOf
   */
  text?: string;
  /**
   *
   * @type {Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner>}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOf
   */
  segments?: Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner>;
  /**
   *
   * @type {Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner>}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOf
   */
  words?: Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner> | null;
}

export const ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfTaskEnum = {
  Transcribe: 'transcribe',
} as const;

export type ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfTaskEnum =
  (typeof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfTaskEnum)[keyof typeof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfTaskEnum];

/**
 *
 * @export
 * @interface ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner
 */
export interface ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner {
  /**
   *
   * @type {number}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner
   */
  id: number;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner
   */
  text: string;
  /**
   *
   * @type {number}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner
   */
  start: number;
  /**
   *
   * @type {number}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner
   */
  end: number;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner
   */
  language: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner
   */
  speaker?: string | null;
  /**
   *
   * @type {Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner>}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner
   */
  words?: Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner> | null;
}
/**
 *
 * @export
 * @interface ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner
 */
export interface ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner
   */
  word: string;
  /**
   *
   * @type {number}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner
   */
  start: number;
  /**
   *
   * @type {number}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner
   */
  end: number;
  /**
   *
   * @type {number}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner
   */
  score: number;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInnerWordsInner
   */
  speaker?: string | null;
}
/**
 *
 * @export
 * @interface ApiV1TasksInvalidateCachePost200Response
 */
export interface ApiV1TasksInvalidateCachePost200Response {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksInvalidateCachePost200Response
   */
  message: string;
  /**
   *
   * @type {number}
   * @memberof ApiV1TasksInvalidateCachePost200Response
   */
  count: number;
}
/**
 *
 * @export
 * @interface ApiV1TasksInvalidateCachePostRequest
 */
export interface ApiV1TasksInvalidateCachePostRequest {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksInvalidateCachePostRequest
   */
  cache_key: string;
}
/**
 *
 * @export
 * @interface ApiV1TasksPostDefaultResponse
 */
export interface ApiV1TasksPostDefaultResponse {
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksPostDefaultResponse
   */
  id: string;
}
/**
 *
 * @export
 * @interface ApiV1TasksPostRequest
 */
export interface ApiV1TasksPostRequest {
  /**
   * 任务类型
   * @type {string}
   * @memberof ApiV1TasksPostRequest
   */
  type: ApiV1TasksPostRequestTypeEnum;
  /**
   * 创建者 id
   * @type {string}
   * @memberof ApiV1TasksPostRequest
   */
  creator_id: string;
  /**
   *
   * @type {string}
   * @memberof ApiV1TasksPostRequest
   */
  payload: string;
}

export const ApiV1TasksPostRequestTypeEnum = {
  SaveImage: 'save_image',
  ScrapPage: 'scrap_page',
  TranscribeMedia: 'transcribe_media',
  TranscribePdf: 'transcribe_pdf',
  TranscribeOffice: 'transcribe_office',
  ExtractText: 'extract_text',
} as const;

export type ApiV1TasksPostRequestTypeEnum =
  (typeof ApiV1TasksPostRequestTypeEnum)[keyof typeof ApiV1TasksPostRequestTypeEnum];

/**
 *
 * @export
 * @interface Def0
 */
export interface Def0 {
  /**
   * 任务 id
   * @type {string}
   * @memberof Def0
   */
  id: string;
  /**
   * 任务类型
   * @type {string}
   * @memberof Def0
   */
  type: Def0TypeEnum;
  /**
   * 任务状态
   * @type {string}
   * @memberof Def0
   */
  status: Def0StatusEnum;
  /**
   * 创建者 id
   * @type {string}
   * @memberof Def0
   */
  creator_id: string;
  /**
   * 创建时间
   * @type {string}
   * @memberof Def0
   */
  created_at: string;
  /**
   * 更新时间
   * @type {string}
   * @memberof Def0
   */
  updated_at: string;
  /**
   * 重试次数
   * @type {number}
   * @memberof Def0
   */
  retry_times?: number;
  /**
   * 任务参数, json
   * @type {{ [key: string]: any; }}
   * @memberof Def0
   */
  payload: { [key: string]: any };
  /**
   * 日志
   * @type {string}
   * @memberof Def0
   */
  log?: string;
  /**
   * 任务结果
   * @type {string}
   * @memberof Def0
   */
  result?: string;
  /**
   * 缓存 key
   * @type {string}
   * @memberof Def0
   */
  cache_key?: string;
  /**
   * 执行环境
   * @type {string}
   * @memberof Def0
   */
  exec_env?: Def0ExecEnvEnum;
  /**
   * 任务元数据
   * @type {{ [key: string]: any; }}
   * @memberof Def0
   */
  metadata?: { [key: string]: any };
}

export const Def0TypeEnum = {
  SaveImage: 'save_image',
  ScrapPage: 'scrap_page',
  TranscribeMedia: 'transcribe_media',
  TranscribePdf: 'transcribe_pdf',
  TranscribeOffice: 'transcribe_office',
  ExtractText: 'extract_text',
} as const;

export type Def0TypeEnum = (typeof Def0TypeEnum)[keyof typeof Def0TypeEnum];
export const Def0StatusEnum = {
  Pending: 'pending',
  Running: 'running',
  Paused: 'paused',
  Failed: 'failed',
  FailedCanRetry: 'failed_can_retry',
  Completed: 'completed',
  Timeout: 'timeout',
} as const;

export type Def0StatusEnum = (typeof Def0StatusEnum)[keyof typeof Def0StatusEnum];
export const Def0ExecEnvEnum = {
  AwsLambda: 'aws_lambda',
  VercelFunction: 'vercel_function',
  Local: 'local',
  Unknown: 'unknown',
} as const;

export type Def0ExecEnvEnum = (typeof Def0ExecEnvEnum)[keyof typeof Def0ExecEnvEnum];

/**
 *
 * @export
 * @interface Def1
 */
export interface Def1 {
  /**
   *
   * @type {string}
   * @memberof Def1
   */
  task: Def1TaskEnum;
  /**
   *
   * @type {string}
   * @memberof Def1
   */
  language: string;
  /**
   *
   * @type {number}
   * @memberof Def1
   */
  duration: number;
  /**
   *
   * @type {string}
   * @memberof Def1
   */
  text: string;
  /**
   *
   * @type {Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner>}
   * @memberof Def1
   */
  segments: Array<ApiV1TasksIdResumeTranscribeMediaPostRequestAnyOfSegmentsInner>;
  /**
   *
   * @type {Array<object>}
   * @memberof Def1
   */
  words?: Array<object> | null;
}

export const Def1TaskEnum = {
  Transcribe: 'transcribe',
} as const;

export type Def1TaskEnum = (typeof Def1TaskEnum)[keyof typeof Def1TaskEnum];

/**
 *
 * @export
 * @interface Def2
 */
export interface Def2 {
  /**
   * 任务完成时的回调地址
   * @type {string}
   * @memberof Def2
   */
  youget_callback_url?: string;
  /**
   * 任务完成时的回调 HTTP 方法
   * @type {string}
   * @memberof Def2
   */
  youget_callback_method?: string;
}
/**
 *
 * @export
 * @interface Def3
 */
export interface Def3 {
  /**
   * 文件 id
   * @type {string}
   * @memberof Def3
   */
  id: string;
  /**
   * 文件地址
   * @type {string}
   * @memberof Def3
   */
  url: string;
  /**
   * 原始文件地址
   * @type {string}
   * @memberof Def3
   */
  original_url: string;
  /**
   * 文件大小, Bytes
   * @type {number}
   * @memberof Def3
   */
  size: number;
  /**
   * 文件名
   * @type {string}
   * @memberof Def3
   */
  name: string;
  /**
   * 文件 MIME 类型
   * @type {string}
   * @memberof Def3
   */
  mime_type: string;
  /**
   * 上传者 id，如存在则说明是私有文件
   * @type {string}
   * @memberof Def3
   */
  user_id?: string;
  /**
   * 创建时间
   * @type {string}
   * @memberof Def3
   */
  created_at: string;
}

/**
 * DashboardApi - axios parameter creator
 * @export
 */
export const DashboardApiAxiosParamCreator = (configuration?: Configuration) => ({
  /**
   *
   * @summary 仪表板任务列表查询 - 支持服务端分页和过滤
   * @param {string} startDate 开始时间
   * @param {string} endDate 结束时间
   * @param {string} [creatorId] 创建者ID筛选
   * @param {string} [page] 页码
   * @param {string} [limit] 每页数量
   * @param {string} [status] 状态筛选
   * @param {string} [type] 类型筛选
   * @param {string} [searchType] 搜索类型: task_id, creator_id, payload, snip_id
   * @param {string} [searchValue] 搜索值
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1DashboardTasksGet: async (
    startDate: string,
    endDate: string,
    creatorId?: string,
    page?: string,
    limit?: string,
    status?: string,
    type?: string,
    searchType?: string,
    searchValue?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'startDate' is not null or undefined
    assertParamExists('apiV1DashboardTasksGet', 'startDate', startDate);
    // verify required parameter 'endDate' is not null or undefined
    assertParamExists('apiV1DashboardTasksGet', 'endDate', endDate);
    const localVarPath = `/api/v1/dashboard/tasks`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'GET',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    if (startDate !== undefined) {
      localVarQueryParameter['start_date'] = startDate;
    }

    if (endDate !== undefined) {
      localVarQueryParameter['end_date'] = endDate;
    }

    if (creatorId !== undefined) {
      localVarQueryParameter['creator_id'] = creatorId;
    }

    if (page !== undefined) {
      localVarQueryParameter['page'] = page;
    }

    if (limit !== undefined) {
      localVarQueryParameter['limit'] = limit;
    }

    if (status !== undefined) {
      localVarQueryParameter['status'] = status;
    }

    if (type !== undefined) {
      localVarQueryParameter['type'] = type;
    }

    if (searchType !== undefined) {
      localVarQueryParameter['search_type'] = searchType;
    }

    if (searchValue !== undefined) {
      localVarQueryParameter['search_value'] = searchValue;
    }

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 仪表板任务状态统计 - ClickHouse 优化
   * @param {string} startDate 开始时间
   * @param {string} endDate 结束时间
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1DashboardTasksStatsGet: async (
    startDate: string,
    endDate: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'startDate' is not null or undefined
    assertParamExists('apiV1DashboardTasksStatsGet', 'startDate', startDate);
    // verify required parameter 'endDate' is not null or undefined
    assertParamExists('apiV1DashboardTasksStatsGet', 'endDate', endDate);
    const localVarPath = `/api/v1/dashboard/tasks/stats`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'GET',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    if (startDate !== undefined) {
      localVarQueryParameter['start_date'] = startDate;
    }

    if (endDate !== undefined) {
      localVarQueryParameter['end_date'] = endDate;
    }

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
});

/**
 * DashboardApi - functional programming interface
 * @export
 */
export const DashboardApiFp = (configuration?: Configuration) => {
  const localVarAxiosParamCreator = DashboardApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary 仪表板任务列表查询 - 支持服务端分页和过滤
     * @param {string} startDate 开始时间
     * @param {string} endDate 结束时间
     * @param {string} [creatorId] 创建者ID筛选
     * @param {string} [page] 页码
     * @param {string} [limit] 每页数量
     * @param {string} [status] 状态筛选
     * @param {string} [type] 类型筛选
     * @param {string} [searchType] 搜索类型: task_id, creator_id, payload, snip_id
     * @param {string} [searchValue] 搜索值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1DashboardTasksGet(
      startDate: string,
      endDate: string,
      creatorId?: string,
      page?: string,
      limit?: string,
      status?: string,
      type?: string,
      searchType?: string,
      searchValue?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1DashboardTasksGet200Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1DashboardTasksGet(
        startDate,
        endDate,
        creatorId,
        page,
        limit,
        status,
        type,
        searchType,
        searchValue,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DashboardApi.apiV1DashboardTasksGet']?.[localVarOperationServerIndex]
          ?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 仪表板任务状态统计 - ClickHouse 优化
     * @param {string} startDate 开始时间
     * @param {string} endDate 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1DashboardTasksStatsGet(
      startDate: string,
      endDate: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1DashboardTasksStatsGet200Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1DashboardTasksStatsGet(
        startDate,
        endDate,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['DashboardApi.apiV1DashboardTasksStatsGet']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * DashboardApi - factory interface
 * @export
 */
export const DashboardApiFactory = (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) => {
  const localVarFp = DashboardApiFp(configuration);
  return {
    /**
     *
     * @summary 仪表板任务列表查询 - 支持服务端分页和过滤
     * @param {string} startDate 开始时间
     * @param {string} endDate 结束时间
     * @param {string} [creatorId] 创建者ID筛选
     * @param {string} [page] 页码
     * @param {string} [limit] 每页数量
     * @param {string} [status] 状态筛选
     * @param {string} [type] 类型筛选
     * @param {string} [searchType] 搜索类型: task_id, creator_id, payload, snip_id
     * @param {string} [searchValue] 搜索值
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1DashboardTasksGet(
      startDate: string,
      endDate: string,
      creatorId?: string,
      page?: string,
      limit?: string,
      status?: string,
      type?: string,
      searchType?: string,
      searchValue?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1DashboardTasksGet200Response> {
      return localVarFp
        .apiV1DashboardTasksGet(
          startDate,
          endDate,
          creatorId,
          page,
          limit,
          status,
          type,
          searchType,
          searchValue,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 仪表板任务状态统计 - ClickHouse 优化
     * @param {string} startDate 开始时间
     * @param {string} endDate 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1DashboardTasksStatsGet(
      startDate: string,
      endDate: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1DashboardTasksStatsGet200Response> {
      return localVarFp
        .apiV1DashboardTasksStatsGet(startDate, endDate, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DashboardApi - object-oriented interface
 * @export
 * @class DashboardApi
 * @extends {BaseAPI}
 */
export class DashboardApi extends BaseAPI {
  /**
   *
   * @summary 仪表板任务列表查询 - 支持服务端分页和过滤
   * @param {string} startDate 开始时间
   * @param {string} endDate 结束时间
   * @param {string} [creatorId] 创建者ID筛选
   * @param {string} [page] 页码
   * @param {string} [limit] 每页数量
   * @param {string} [status] 状态筛选
   * @param {string} [type] 类型筛选
   * @param {string} [searchType] 搜索类型: task_id, creator_id, payload, snip_id
   * @param {string} [searchValue] 搜索值
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DashboardApi
   */
  public apiV1DashboardTasksGet(
    startDate: string,
    endDate: string,
    creatorId?: string,
    page?: string,
    limit?: string,
    status?: string,
    type?: string,
    searchType?: string,
    searchValue?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return DashboardApiFp(this.configuration)
      .apiV1DashboardTasksGet(
        startDate,
        endDate,
        creatorId,
        page,
        limit,
        status,
        type,
        searchType,
        searchValue,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 仪表板任务状态统计 - ClickHouse 优化
   * @param {string} startDate 开始时间
   * @param {string} endDate 结束时间
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DashboardApi
   */
  public apiV1DashboardTasksStatsGet(
    startDate: string,
    endDate: string,
    options?: RawAxiosRequestConfig,
  ) {
    return DashboardApiFp(this.configuration)
      .apiV1DashboardTasksStatsGet(startDate, endDate, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ExtractTextApi - axios parameter creator
 * @export
 */
export const ExtractTextApiAxiosParamCreator = (configuration?: Configuration) => ({
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 提取文本任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCallbackExtractTextPut: async (
    apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCallbackExtractTextPut',
      'apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult',
      apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult,
    );
    const localVarPath = `/api/v1/shortcut/callbackExtractText`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'PUT',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 创建并立即异步执行提取文本任务
   * @param {ApiV1ShortcutCreateExtractTextTaskPostRequest} apiV1ShortcutCreateExtractTextTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateAndRunExtractTextTaskPost: async (
    apiV1ShortcutCreateExtractTextTaskPostRequest: ApiV1ShortcutCreateExtractTextTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateExtractTextTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateAndRunExtractTextTaskPost',
      'apiV1ShortcutCreateExtractTextTaskPostRequest',
      apiV1ShortcutCreateExtractTextTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createAndRunExtractTextTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    if (sync != null) {
      localVarHeaderParameter['sync'] = String(sync);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateExtractTextTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks type=extract_text 的快捷方式
   * @summary 创建提取文本任务
   * @param {ApiV1ShortcutCreateExtractTextTaskPostRequest} apiV1ShortcutCreateExtractTextTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateExtractTextTaskPost: async (
    apiV1ShortcutCreateExtractTextTaskPostRequest: ApiV1ShortcutCreateExtractTextTaskPostRequest,
    creatorId?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateExtractTextTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateExtractTextTaskPost',
      'apiV1ShortcutCreateExtractTextTaskPostRequest',
      apiV1ShortcutCreateExtractTextTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createExtractTextTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateExtractTextTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks/:id type=extract_text 的快捷方式
   * @summary 执行提取文本任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutRunExtractTextTaskByIdPost: async (
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum,
    useCache?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1TasksPostDefaultResponse' is not null or undefined
    assertParamExists(
      'apiV1ShortcutRunExtractTextTaskByIdPost',
      'apiV1TasksPostDefaultResponse',
      apiV1TasksPostDefaultResponse,
    );
    const localVarPath = `/api/v1/shortcut/runExtractTextTaskById`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksPostDefaultResponse,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
});

/**
 * ExtractTextApi - functional programming interface
 * @export
 */
export const ExtractTextApiFp = (configuration?: Configuration) => {
  const localVarAxiosParamCreator = ExtractTextApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 提取文本任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCallbackExtractTextPut(
      apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1ShortcutCallbackExtractTextPut(
        apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ExtractTextApi.apiV1ShortcutCallbackExtractTextPut']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 创建并立即异步执行提取文本任务
     * @param {ApiV1ShortcutCreateExtractTextTaskPostRequest} apiV1ShortcutCreateExtractTextTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateAndRunExtractTextTaskPost(
      apiV1ShortcutCreateExtractTextTaskPostRequest: ApiV1ShortcutCreateExtractTextTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateAndRunExtractTextTaskPost(
          apiV1ShortcutCreateExtractTextTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ExtractTextApi.apiV1ShortcutCreateAndRunExtractTextTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks type=extract_text 的快捷方式
     * @summary 创建提取文本任务
     * @param {ApiV1ShortcutCreateExtractTextTaskPostRequest} apiV1ShortcutCreateExtractTextTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateExtractTextTaskPost(
      apiV1ShortcutCreateExtractTextTaskPostRequest: ApiV1ShortcutCreateExtractTextTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateExtractTextTaskPost(
          apiV1ShortcutCreateExtractTextTaskPostRequest,
          creatorId,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ExtractTextApi.apiV1ShortcutCreateExtractTextTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks/:id type=extract_text 的快捷方式
     * @summary 执行提取文本任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutRunExtractTextTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutRunExtractTextTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ExtractTextApi.apiV1ShortcutRunExtractTextTaskByIdPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ExtractTextApi - factory interface
 * @export
 */
export const ExtractTextApiFactory = (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) => {
  const localVarFp = ExtractTextApiFp(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 提取文本任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCallbackExtractTextPut(
      apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .apiV1ShortcutCallbackExtractTextPut(
          apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 创建并立即异步执行提取文本任务
     * @param {ApiV1ShortcutCreateExtractTextTaskPostRequest} apiV1ShortcutCreateExtractTextTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateAndRunExtractTextTaskPost(
      apiV1ShortcutCreateExtractTextTaskPostRequest: ApiV1ShortcutCreateExtractTextTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateAndRunExtractTextTaskPost(
          apiV1ShortcutCreateExtractTextTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks type=extract_text 的快捷方式
     * @summary 创建提取文本任务
     * @param {ApiV1ShortcutCreateExtractTextTaskPostRequest} apiV1ShortcutCreateExtractTextTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateExtractTextTaskPost(
      apiV1ShortcutCreateExtractTextTaskPostRequest: ApiV1ShortcutCreateExtractTextTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateExtractTextTaskPost(
          apiV1ShortcutCreateExtractTextTaskPostRequest,
          creatorId,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks/:id type=extract_text 的快捷方式
     * @summary 执行提取文本任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutRunExtractTextTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutRunExtractTextTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ExtractTextApi - object-oriented interface
 * @export
 * @class ExtractTextApi
 * @extends {BaseAPI}
 */
export class ExtractTextApi extends BaseAPI {
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 提取文本任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExtractTextApi
   */
  public apiV1ShortcutCallbackExtractTextPut(
    apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult,
    options?: RawAxiosRequestConfig,
  ) {
    return ExtractTextApiFp(this.configuration)
      .apiV1ShortcutCallbackExtractTextPut(
        apiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 创建并立即异步执行提取文本任务
   * @param {ApiV1ShortcutCreateExtractTextTaskPostRequest} apiV1ShortcutCreateExtractTextTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExtractTextApi
   */
  public apiV1ShortcutCreateAndRunExtractTextTaskPost(
    apiV1ShortcutCreateExtractTextTaskPostRequest: ApiV1ShortcutCreateExtractTextTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return ExtractTextApiFp(this.configuration)
      .apiV1ShortcutCreateAndRunExtractTextTaskPost(
        apiV1ShortcutCreateExtractTextTaskPostRequest,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        creatorId,
        sync,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks type=extract_text 的快捷方式
   * @summary 创建提取文本任务
   * @param {ApiV1ShortcutCreateExtractTextTaskPostRequest} apiV1ShortcutCreateExtractTextTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExtractTextApi
   */
  public apiV1ShortcutCreateExtractTextTaskPost(
    apiV1ShortcutCreateExtractTextTaskPostRequest: ApiV1ShortcutCreateExtractTextTaskPostRequest,
    creatorId?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return ExtractTextApiFp(this.configuration)
      .apiV1ShortcutCreateExtractTextTaskPost(
        apiV1ShortcutCreateExtractTextTaskPostRequest,
        creatorId,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks/:id type=extract_text 的快捷方式
   * @summary 执行提取文本任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExtractTextApi
   */
  public apiV1ShortcutRunExtractTextTaskByIdPost(
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum,
    useCache?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return ExtractTextApiFp(this.configuration)
      .apiV1ShortcutRunExtractTextTaskByIdPost(
        apiV1TasksPostDefaultResponse,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum =
  (typeof ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum)[keyof typeof ApiV1ShortcutCreateAndRunExtractTextTaskPostExecEnvEnum];
/**
 * @export
 */
export const ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum =
  (typeof ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum)[keyof typeof ApiV1ShortcutRunExtractTextTaskByIdPostExecEnvEnum];

/**
 * SaveImageApi - axios parameter creator
 * @export
 */
export const SaveImageApiAxiosParamCreator = (configuration?: Configuration) => ({
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 转存图片任务 回调时的入参
   * @param {Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>} [apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCallbackSaveImagePut: async (
    apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner?: Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    const localVarPath = `/api/v1/shortcut/callbackSaveImage`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'PUT',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 创建并立即异步执行转存图片任务
   * @param {ApiV1ShortcutCreateSaveImageTaskPostRequest} apiV1ShortcutCreateSaveImageTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateAndRunSaveImageTaskPost: async (
    apiV1ShortcutCreateSaveImageTaskPostRequest: ApiV1ShortcutCreateSaveImageTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateSaveImageTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateAndRunSaveImageTaskPost',
      'apiV1ShortcutCreateSaveImageTaskPostRequest',
      apiV1ShortcutCreateSaveImageTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createAndRunSaveImageTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    if (sync != null) {
      localVarHeaderParameter['sync'] = String(sync);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateSaveImageTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks type=save_image 的快捷方式
   * @summary 创建转存图片任务
   * @param {ApiV1ShortcutCreateSaveImageTaskPostRequest} apiV1ShortcutCreateSaveImageTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateSaveImageTaskPost: async (
    apiV1ShortcutCreateSaveImageTaskPostRequest: ApiV1ShortcutCreateSaveImageTaskPostRequest,
    creatorId?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateSaveImageTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateSaveImageTaskPost',
      'apiV1ShortcutCreateSaveImageTaskPostRequest',
      apiV1ShortcutCreateSaveImageTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createSaveImageTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateSaveImageTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks/:id type=save_image 的快捷方式
   * @summary 执行转存图片任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutRunSaveImageTaskByIdPost: async (
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum,
    useCache?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1TasksPostDefaultResponse' is not null or undefined
    assertParamExists(
      'apiV1ShortcutRunSaveImageTaskByIdPost',
      'apiV1TasksPostDefaultResponse',
      apiV1TasksPostDefaultResponse,
    );
    const localVarPath = `/api/v1/shortcut/runSaveImageTaskById`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksPostDefaultResponse,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
});

/**
 * SaveImageApi - functional programming interface
 * @export
 */
export const SaveImageApiFp = (configuration?: Configuration) => {
  const localVarAxiosParamCreator = SaveImageApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 转存图片任务 回调时的入参
     * @param {Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>} [apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCallbackSaveImagePut(
      apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner?: Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1ShortcutCallbackSaveImagePut(
        apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SaveImageApi.apiV1ShortcutCallbackSaveImagePut']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 创建并立即异步执行转存图片任务
     * @param {ApiV1ShortcutCreateSaveImageTaskPostRequest} apiV1ShortcutCreateSaveImageTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateAndRunSaveImageTaskPost(
      apiV1ShortcutCreateSaveImageTaskPostRequest: ApiV1ShortcutCreateSaveImageTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutCreateAndRunSaveImageTaskPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateAndRunSaveImageTaskPost(
          apiV1ShortcutCreateSaveImageTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SaveImageApi.apiV1ShortcutCreateAndRunSaveImageTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks type=save_image 的快捷方式
     * @summary 创建转存图片任务
     * @param {ApiV1ShortcutCreateSaveImageTaskPostRequest} apiV1ShortcutCreateSaveImageTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateSaveImageTaskPost(
      apiV1ShortcutCreateSaveImageTaskPostRequest: ApiV1ShortcutCreateSaveImageTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateSaveImageTaskPost(
          apiV1ShortcutCreateSaveImageTaskPostRequest,
          creatorId,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SaveImageApi.apiV1ShortcutCreateSaveImageTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks/:id type=save_image 的快捷方式
     * @summary 执行转存图片任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutRunSaveImageTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutRunSaveImageTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['SaveImageApi.apiV1ShortcutRunSaveImageTaskByIdPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * SaveImageApi - factory interface
 * @export
 */
export const SaveImageApiFactory = (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) => {
  const localVarFp = SaveImageApiFp(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 转存图片任务 回调时的入参
     * @param {Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>} [apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCallbackSaveImagePut(
      apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner?: Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .apiV1ShortcutCallbackSaveImagePut(
          apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 创建并立即异步执行转存图片任务
     * @param {ApiV1ShortcutCreateSaveImageTaskPostRequest} apiV1ShortcutCreateSaveImageTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateAndRunSaveImageTaskPost(
      apiV1ShortcutCreateSaveImageTaskPostRequest: ApiV1ShortcutCreateSaveImageTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutCreateAndRunSaveImageTaskPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateAndRunSaveImageTaskPost(
          apiV1ShortcutCreateSaveImageTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks type=save_image 的快捷方式
     * @summary 创建转存图片任务
     * @param {ApiV1ShortcutCreateSaveImageTaskPostRequest} apiV1ShortcutCreateSaveImageTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateSaveImageTaskPost(
      apiV1ShortcutCreateSaveImageTaskPostRequest: ApiV1ShortcutCreateSaveImageTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateSaveImageTaskPost(
          apiV1ShortcutCreateSaveImageTaskPostRequest,
          creatorId,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks/:id type=save_image 的快捷方式
     * @summary 执行转存图片任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutRunSaveImageTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutRunSaveImageTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * SaveImageApi - object-oriented interface
 * @export
 * @class SaveImageApi
 * @extends {BaseAPI}
 */
export class SaveImageApi extends BaseAPI {
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 转存图片任务 回调时的入参
   * @param {Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>} [apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SaveImageApi
   */
  public apiV1ShortcutCallbackSaveImagePut(
    apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner?: Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>,
    options?: RawAxiosRequestConfig,
  ) {
    return SaveImageApiFp(this.configuration)
      .apiV1ShortcutCallbackSaveImagePut(
        apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 创建并立即异步执行转存图片任务
   * @param {ApiV1ShortcutCreateSaveImageTaskPostRequest} apiV1ShortcutCreateSaveImageTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SaveImageApi
   */
  public apiV1ShortcutCreateAndRunSaveImageTaskPost(
    apiV1ShortcutCreateSaveImageTaskPostRequest: ApiV1ShortcutCreateSaveImageTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return SaveImageApiFp(this.configuration)
      .apiV1ShortcutCreateAndRunSaveImageTaskPost(
        apiV1ShortcutCreateSaveImageTaskPostRequest,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        creatorId,
        sync,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks type=save_image 的快捷方式
   * @summary 创建转存图片任务
   * @param {ApiV1ShortcutCreateSaveImageTaskPostRequest} apiV1ShortcutCreateSaveImageTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SaveImageApi
   */
  public apiV1ShortcutCreateSaveImageTaskPost(
    apiV1ShortcutCreateSaveImageTaskPostRequest: ApiV1ShortcutCreateSaveImageTaskPostRequest,
    creatorId?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return SaveImageApiFp(this.configuration)
      .apiV1ShortcutCreateSaveImageTaskPost(
        apiV1ShortcutCreateSaveImageTaskPostRequest,
        creatorId,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks/:id type=save_image 的快捷方式
   * @summary 执行转存图片任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SaveImageApi
   */
  public apiV1ShortcutRunSaveImageTaskByIdPost(
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum,
    useCache?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return SaveImageApiFp(this.configuration)
      .apiV1ShortcutRunSaveImageTaskByIdPost(
        apiV1TasksPostDefaultResponse,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum =
  (typeof ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum)[keyof typeof ApiV1ShortcutCreateAndRunSaveImageTaskPostExecEnvEnum];
/**
 * @export
 */
export const ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum =
  (typeof ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum)[keyof typeof ApiV1ShortcutRunSaveImageTaskByIdPostExecEnvEnum];

/**
 * ScrapPageApi - axios parameter creator
 * @export
 */
export const ScrapPageApiAxiosParamCreator = (configuration?: Configuration) => ({
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 网页爬取任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCallbackScrapPagePut: async (
    apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCallbackScrapPagePut',
      'apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult',
      apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult,
    );
    const localVarPath = `/api/v1/shortcut/callbackScrapPage`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'PUT',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 创建并立即异步执行网页爬取任务
   * @param {ApiV1ShortcutCreateScrapPageTaskPostRequest} apiV1ShortcutCreateScrapPageTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateAndRunScrapPageTaskPost: async (
    apiV1ShortcutCreateScrapPageTaskPostRequest: ApiV1ShortcutCreateScrapPageTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateScrapPageTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateAndRunScrapPageTaskPost',
      'apiV1ShortcutCreateScrapPageTaskPostRequest',
      apiV1ShortcutCreateScrapPageTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createAndRunScrapPageTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    if (sync != null) {
      localVarHeaderParameter['sync'] = String(sync);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateScrapPageTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks type=scrap_page 的快捷方式
   * @summary 创建网页爬取任务
   * @param {ApiV1ShortcutCreateScrapPageTaskPostRequest} apiV1ShortcutCreateScrapPageTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateScrapPageTaskPost: async (
    apiV1ShortcutCreateScrapPageTaskPostRequest: ApiV1ShortcutCreateScrapPageTaskPostRequest,
    creatorId?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateScrapPageTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateScrapPageTaskPost',
      'apiV1ShortcutCreateScrapPageTaskPostRequest',
      apiV1ShortcutCreateScrapPageTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createScrapPageTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateScrapPageTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks/:id type=scrap_page 的快捷方式
   * @summary 执行网页爬取任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutRunScrapPageTaskByIdPost: async (
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum,
    useCache?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1TasksPostDefaultResponse' is not null or undefined
    assertParamExists(
      'apiV1ShortcutRunScrapPageTaskByIdPost',
      'apiV1TasksPostDefaultResponse',
      apiV1TasksPostDefaultResponse,
    );
    const localVarPath = `/api/v1/shortcut/runScrapPageTaskById`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksPostDefaultResponse,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
});

/**
 * ScrapPageApi - functional programming interface
 * @export
 */
export const ScrapPageApiFp = (configuration?: Configuration) => {
  const localVarAxiosParamCreator = ScrapPageApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 网页爬取任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCallbackScrapPagePut(
      apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1ShortcutCallbackScrapPagePut(
        apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ScrapPageApi.apiV1ShortcutCallbackScrapPagePut']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 创建并立即异步执行网页爬取任务
     * @param {ApiV1ShortcutCreateScrapPageTaskPostRequest} apiV1ShortcutCreateScrapPageTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateAndRunScrapPageTaskPost(
      apiV1ShortcutCreateScrapPageTaskPostRequest: ApiV1ShortcutCreateScrapPageTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateAndRunScrapPageTaskPost(
          apiV1ShortcutCreateScrapPageTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ScrapPageApi.apiV1ShortcutCreateAndRunScrapPageTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks type=scrap_page 的快捷方式
     * @summary 创建网页爬取任务
     * @param {ApiV1ShortcutCreateScrapPageTaskPostRequest} apiV1ShortcutCreateScrapPageTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateScrapPageTaskPost(
      apiV1ShortcutCreateScrapPageTaskPostRequest: ApiV1ShortcutCreateScrapPageTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateScrapPageTaskPost(
          apiV1ShortcutCreateScrapPageTaskPostRequest,
          creatorId,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ScrapPageApi.apiV1ShortcutCreateScrapPageTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks/:id type=scrap_page 的快捷方式
     * @summary 执行网页爬取任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutRunScrapPageTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutRunScrapPageTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['ScrapPageApi.apiV1ShortcutRunScrapPageTaskByIdPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * ScrapPageApi - factory interface
 * @export
 */
export const ScrapPageApiFactory = (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) => {
  const localVarFp = ScrapPageApiFp(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 网页爬取任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCallbackScrapPagePut(
      apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .apiV1ShortcutCallbackScrapPagePut(
          apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 创建并立即异步执行网页爬取任务
     * @param {ApiV1ShortcutCreateScrapPageTaskPostRequest} apiV1ShortcutCreateScrapPageTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateAndRunScrapPageTaskPost(
      apiV1ShortcutCreateScrapPageTaskPostRequest: ApiV1ShortcutCreateScrapPageTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateAndRunScrapPageTaskPost(
          apiV1ShortcutCreateScrapPageTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks type=scrap_page 的快捷方式
     * @summary 创建网页爬取任务
     * @param {ApiV1ShortcutCreateScrapPageTaskPostRequest} apiV1ShortcutCreateScrapPageTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateScrapPageTaskPost(
      apiV1ShortcutCreateScrapPageTaskPostRequest: ApiV1ShortcutCreateScrapPageTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateScrapPageTaskPost(
          apiV1ShortcutCreateScrapPageTaskPostRequest,
          creatorId,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks/:id type=scrap_page 的快捷方式
     * @summary 执行网页爬取任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutRunScrapPageTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutRunScrapPageTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ScrapPageApi - object-oriented interface
 * @export
 * @class ScrapPageApi
 * @extends {BaseAPI}
 */
export class ScrapPageApi extends BaseAPI {
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 网页爬取任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ScrapPageApi
   */
  public apiV1ShortcutCallbackScrapPagePut(
    apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult,
    options?: RawAxiosRequestConfig,
  ) {
    return ScrapPageApiFp(this.configuration)
      .apiV1ShortcutCallbackScrapPagePut(
        apiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResult,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 创建并立即异步执行网页爬取任务
   * @param {ApiV1ShortcutCreateScrapPageTaskPostRequest} apiV1ShortcutCreateScrapPageTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ScrapPageApi
   */
  public apiV1ShortcutCreateAndRunScrapPageTaskPost(
    apiV1ShortcutCreateScrapPageTaskPostRequest: ApiV1ShortcutCreateScrapPageTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return ScrapPageApiFp(this.configuration)
      .apiV1ShortcutCreateAndRunScrapPageTaskPost(
        apiV1ShortcutCreateScrapPageTaskPostRequest,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        creatorId,
        sync,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks type=scrap_page 的快捷方式
   * @summary 创建网页爬取任务
   * @param {ApiV1ShortcutCreateScrapPageTaskPostRequest} apiV1ShortcutCreateScrapPageTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ScrapPageApi
   */
  public apiV1ShortcutCreateScrapPageTaskPost(
    apiV1ShortcutCreateScrapPageTaskPostRequest: ApiV1ShortcutCreateScrapPageTaskPostRequest,
    creatorId?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return ScrapPageApiFp(this.configuration)
      .apiV1ShortcutCreateScrapPageTaskPost(
        apiV1ShortcutCreateScrapPageTaskPostRequest,
        creatorId,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks/:id type=scrap_page 的快捷方式
   * @summary 执行网页爬取任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ScrapPageApi
   */
  public apiV1ShortcutRunScrapPageTaskByIdPost(
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum,
    useCache?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return ScrapPageApiFp(this.configuration)
      .apiV1ShortcutRunScrapPageTaskByIdPost(
        apiV1TasksPostDefaultResponse,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum =
  (typeof ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum)[keyof typeof ApiV1ShortcutCreateAndRunScrapPageTaskPostExecEnvEnum];
/**
 * @export
 */
export const ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum =
  (typeof ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum)[keyof typeof ApiV1ShortcutRunScrapPageTaskByIdPostExecEnvEnum];

/**
 * TaskApi - axios parameter creator
 * @export
 */
export const TaskApiAxiosParamCreator = (configuration?: Configuration) => ({
  /**
   * Check Azure transcription status
   * @param {ApiV***********************************} [apiV***********************************]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1CheckAzureTranscriptionPost: async (
    apiV***********************************?: ApiV***********************************,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    const localVarPath = `/api/v1/check-azure-transcription`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV***********************************,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 获取任务列表
   * @param {string} startDate 开始时间
   * @param {string} endDate 结束时间
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1TasksGet: async (
    startDate: string,
    endDate: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'startDate' is not null or undefined
    assertParamExists('apiV1TasksGet', 'startDate', startDate);
    // verify required parameter 'endDate' is not null or undefined
    assertParamExists('apiV1TasksGet', 'endDate', endDate);
    const localVarPath = `/api/v1/tasks`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'GET',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    if (startDate !== undefined) {
      localVarQueryParameter['start_date'] = startDate;
    }

    if (endDate !== undefined) {
      localVarQueryParameter['end_date'] = endDate;
    }

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 根据 id 查询任务状态
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1TasksIdGet: async (
    id: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'id' is not null or undefined
    assertParamExists('apiV1TasksIdGet', 'id', id);
    const localVarPath = `/api/v1/tasks/{id}`.replace(`{${'id'}}`, encodeURIComponent(String(id)));
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'GET',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 获取任务日志
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1TasksIdLogGet: async (
    id: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'id' is not null or undefined
    assertParamExists('apiV1TasksIdLogGet', 'id', id);
    const localVarPath = `/api/v1/tasks/{id}/log`.replace(
      `{${'id'}}`,
      encodeURIComponent(String(id)),
    );
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'GET',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 执行任务
   * @param {string} id
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1TasksIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1TasksIdPost: async (
    id: string,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1TasksIdPostExecEnvEnum,
    useCache?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'id' is not null or undefined
    assertParamExists('apiV1TasksIdPost', 'id', id);
    const localVarPath = `/api/v1/tasks/{id}`.replace(`{${'id'}}`, encodeURIComponent(String(id)));
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 更新任务
   * @param {string} id
   * @param {ApiV1TasksIdPutRequest} apiV1TasksIdPutRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1TasksIdPut: async (
    id: string,
    apiV1TasksIdPutRequest: ApiV1TasksIdPutRequest,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'id' is not null or undefined
    assertParamExists('apiV1TasksIdPut', 'id', id);
    // verify required parameter 'apiV1TasksIdPutRequest' is not null or undefined
    assertParamExists('apiV1TasksIdPut', 'apiV1TasksIdPutRequest', apiV1TasksIdPutRequest);
    const localVarPath = `/api/v1/tasks/{id}`.replace(`{${'id'}}`, encodeURIComponent(String(id)));
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'PUT',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksIdPutRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 获取任务结果
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1TasksIdResultGet: async (
    id: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'id' is not null or undefined
    assertParamExists('apiV1TasksIdResultGet', 'id', id);
    const localVarPath = `/api/v1/tasks/{id}/result`.replace(
      `{${'id'}}`,
      encodeURIComponent(String(id)),
    );
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'GET',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 恢复执行音视频转录任务（由三方系统回调使用）
   * @param {string} id
   * @param {ApiV1TasksIdResumeTranscribeMediaPostRequest} [apiV1TasksIdResumeTranscribeMediaPostRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1TasksIdResumeTranscribeMediaPost: async (
    id: string,
    apiV1TasksIdResumeTranscribeMediaPostRequest?: ApiV1TasksIdResumeTranscribeMediaPostRequest,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'id' is not null or undefined
    assertParamExists('apiV1TasksIdResumeTranscribeMediaPost', 'id', id);
    const localVarPath = `/api/v1/tasks/{id}/resume-transcribe-media`.replace(
      `{${'id'}}`,
      encodeURIComponent(String(id)),
    );
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksIdResumeTranscribeMediaPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary Batch invalidate cache for tasks with the same cache key
   * @param {ApiV1TasksInvalidateCachePostRequest} apiV1TasksInvalidateCachePostRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1TasksInvalidateCachePost: async (
    apiV1TasksInvalidateCachePostRequest: ApiV1TasksInvalidateCachePostRequest,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1TasksInvalidateCachePostRequest' is not null or undefined
    assertParamExists(
      'apiV1TasksInvalidateCachePost',
      'apiV1TasksInvalidateCachePostRequest',
      apiV1TasksInvalidateCachePostRequest,
    );
    const localVarPath = `/api/v1/tasks/invalidate-cache`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksInvalidateCachePostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 创建任务
   * @param {ApiV1TasksPostRequest} apiV1TasksPostRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1TasksPost: async (
    apiV1TasksPostRequest: ApiV1TasksPostRequest,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1TasksPostRequest' is not null or undefined
    assertParamExists('apiV1TasksPost', 'apiV1TasksPostRequest', apiV1TasksPostRequest);
    const localVarPath = `/api/v1/tasks`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
});

/**
 * TaskApi - functional programming interface
 * @export
 */
export const TaskApiFp = (configuration?: Configuration) => {
  const localVarAxiosParamCreator = TaskApiAxiosParamCreator(configuration);
  return {
    /**
     * Check Azure transcription status
     * @param {ApiV***********************************} [apiV***********************************]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1CheckAzureTranscriptionPost(
      apiV***********************************?: ApiV***********************************,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1CheckAzureTranscriptionPost(
        apiV***********************************,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1CheckAzureTranscriptionPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 获取任务列表
     * @param {string} startDate 开始时间
     * @param {string} endDate 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1TasksGet(
      startDate: string,
      endDate: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<ApiV1TasksGet200ResponseInner>>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1TasksGet(
        startDate,
        endDate,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1TasksGet']?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 根据 id 查询任务状态
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1TasksIdGet(
      id: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksIdGet200Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1TasksIdGet(id, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1TasksIdGet']?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 获取任务日志
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1TasksIdLogGet(
      id: string,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1TasksIdLogGet(id, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1TasksIdLogGet']?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 执行任务
     * @param {string} id
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1TasksIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1TasksIdPost(
      id: string,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1TasksIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksIdPostDefaultResponse>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1TasksIdPost(
        id,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1TasksIdPost']?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 更新任务
     * @param {string} id
     * @param {ApiV1TasksIdPutRequest} apiV1TasksIdPutRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1TasksIdPut(
      id: string,
      apiV1TasksIdPutRequest: ApiV1TasksIdPutRequest,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksIdGet4XXResponse>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1TasksIdPut(
        id,
        apiV1TasksIdPutRequest,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1TasksIdPut']?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 获取任务结果
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1TasksIdResultGet(
      id: string,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1TasksIdResultGet(id, options);
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1TasksIdResultGet']?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 恢复执行音视频转录任务（由三方系统回调使用）
     * @param {string} id
     * @param {ApiV1TasksIdResumeTranscribeMediaPostRequest} [apiV1TasksIdResumeTranscribeMediaPostRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1TasksIdResumeTranscribeMediaPost(
      id: string,
      apiV1TasksIdResumeTranscribeMediaPostRequest?: ApiV1TasksIdResumeTranscribeMediaPostRequest,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1TasksIdResumeTranscribeMediaPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1TasksIdResumeTranscribeMediaPost(
          id,
          apiV1TasksIdResumeTranscribeMediaPostRequest,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1TasksIdResumeTranscribeMediaPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Batch invalidate cache for tasks with the same cache key
     * @param {ApiV1TasksInvalidateCachePostRequest} apiV1TasksInvalidateCachePostRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1TasksInvalidateCachePost(
      apiV1TasksInvalidateCachePostRequest: ApiV1TasksInvalidateCachePostRequest,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1TasksInvalidateCachePost200Response>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1TasksInvalidateCachePost(
        apiV1TasksInvalidateCachePostRequest,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1TasksInvalidateCachePost']?.[localVarOperationServerIndex]
          ?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 创建任务
     * @param {ApiV1TasksPostRequest} apiV1TasksPostRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1TasksPost(
      apiV1TasksPostRequest: ApiV1TasksPostRequest,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksPostDefaultResponse>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.apiV1TasksPost(
        apiV1TasksPostRequest,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TaskApi.apiV1TasksPost']?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * TaskApi - factory interface
 * @export
 */
export const TaskApiFactory = (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) => {
  const localVarFp = TaskApiFp(configuration);
  return {
    /**
     * Check Azure transcription status
     * @param {ApiV***********************************} [apiV***********************************]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1CheckAzureTranscriptionPost(
      apiV***********************************?: ApiV***********************************,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .apiV1CheckAzureTranscriptionPost(apiV***********************************, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 获取任务列表
     * @param {string} startDate 开始时间
     * @param {string} endDate 结束时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1TasksGet(
      startDate: string,
      endDate: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<Array<ApiV1TasksGet200ResponseInner>> {
      return localVarFp
        .apiV1TasksGet(startDate, endDate, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 根据 id 查询任务状态
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1TasksIdGet(
      id: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksIdGet200Response> {
      return localVarFp.apiV1TasksIdGet(id, options).then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 获取任务日志
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1TasksIdLogGet(id: string, options?: RawAxiosRequestConfig): AxiosPromise<string> {
      return localVarFp.apiV1TasksIdLogGet(id, options).then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 执行任务
     * @param {string} id
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1TasksIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1TasksIdPost(
      id: string,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1TasksIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksIdPostDefaultResponse> {
      return localVarFp
        .apiV1TasksIdPost(id, force, callbackWhenError, execEnv, useCache, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 更新任务
     * @param {string} id
     * @param {ApiV1TasksIdPutRequest} apiV1TasksIdPutRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1TasksIdPut(
      id: string,
      apiV1TasksIdPutRequest: ApiV1TasksIdPutRequest,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksIdGet4XXResponse> {
      return localVarFp
        .apiV1TasksIdPut(id, apiV1TasksIdPutRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 获取任务结果
     * @param {string} id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1TasksIdResultGet(id: string, options?: RawAxiosRequestConfig): AxiosPromise<string> {
      return localVarFp
        .apiV1TasksIdResultGet(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 恢复执行音视频转录任务（由三方系统回调使用）
     * @param {string} id
     * @param {ApiV1TasksIdResumeTranscribeMediaPostRequest} [apiV1TasksIdResumeTranscribeMediaPostRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1TasksIdResumeTranscribeMediaPost(
      id: string,
      apiV1TasksIdResumeTranscribeMediaPostRequest?: ApiV1TasksIdResumeTranscribeMediaPostRequest,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksIdResumeTranscribeMediaPostDefaultResponse> {
      return localVarFp
        .apiV1TasksIdResumeTranscribeMediaPost(
          id,
          apiV1TasksIdResumeTranscribeMediaPostRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Batch invalidate cache for tasks with the same cache key
     * @param {ApiV1TasksInvalidateCachePostRequest} apiV1TasksInvalidateCachePostRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1TasksInvalidateCachePost(
      apiV1TasksInvalidateCachePostRequest: ApiV1TasksInvalidateCachePostRequest,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksInvalidateCachePost200Response> {
      return localVarFp
        .apiV1TasksInvalidateCachePost(apiV1TasksInvalidateCachePostRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 创建任务
     * @param {ApiV1TasksPostRequest} apiV1TasksPostRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1TasksPost(
      apiV1TasksPostRequest: ApiV1TasksPostRequest,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksPostDefaultResponse> {
      return localVarFp
        .apiV1TasksPost(apiV1TasksPostRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * TaskApi - object-oriented interface
 * @export
 * @class TaskApi
 * @extends {BaseAPI}
 */
export class TaskApi extends BaseAPI {
  /**
   * Check Azure transcription status
   * @param {ApiV***********************************} [apiV***********************************]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1CheckAzureTranscriptionPost(
    apiV***********************************?: ApiV***********************************,
    options?: RawAxiosRequestConfig,
  ) {
    return TaskApiFp(this.configuration)
      .apiV1CheckAzureTranscriptionPost(apiV***********************************, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 获取任务列表
   * @param {string} startDate 开始时间
   * @param {string} endDate 结束时间
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1TasksGet(startDate: string, endDate: string, options?: RawAxiosRequestConfig) {
    return TaskApiFp(this.configuration)
      .apiV1TasksGet(startDate, endDate, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 根据 id 查询任务状态
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1TasksIdGet(id: string, options?: RawAxiosRequestConfig) {
    return TaskApiFp(this.configuration)
      .apiV1TasksIdGet(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 获取任务日志
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1TasksIdLogGet(id: string, options?: RawAxiosRequestConfig) {
    return TaskApiFp(this.configuration)
      .apiV1TasksIdLogGet(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 执行任务
   * @param {string} id
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1TasksIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1TasksIdPost(
    id: string,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1TasksIdPostExecEnvEnum,
    useCache?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TaskApiFp(this.configuration)
      .apiV1TasksIdPost(id, force, callbackWhenError, execEnv, useCache, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 更新任务
   * @param {string} id
   * @param {ApiV1TasksIdPutRequest} apiV1TasksIdPutRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1TasksIdPut(
    id: string,
    apiV1TasksIdPutRequest: ApiV1TasksIdPutRequest,
    options?: RawAxiosRequestConfig,
  ) {
    return TaskApiFp(this.configuration)
      .apiV1TasksIdPut(id, apiV1TasksIdPutRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 获取任务结果
   * @param {string} id
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1TasksIdResultGet(id: string, options?: RawAxiosRequestConfig) {
    return TaskApiFp(this.configuration)
      .apiV1TasksIdResultGet(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 恢复执行音视频转录任务（由三方系统回调使用）
   * @param {string} id
   * @param {ApiV1TasksIdResumeTranscribeMediaPostRequest} [apiV1TasksIdResumeTranscribeMediaPostRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1TasksIdResumeTranscribeMediaPost(
    id: string,
    apiV1TasksIdResumeTranscribeMediaPostRequest?: ApiV1TasksIdResumeTranscribeMediaPostRequest,
    options?: RawAxiosRequestConfig,
  ) {
    return TaskApiFp(this.configuration)
      .apiV1TasksIdResumeTranscribeMediaPost(
        id,
        apiV1TasksIdResumeTranscribeMediaPostRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Batch invalidate cache for tasks with the same cache key
   * @param {ApiV1TasksInvalidateCachePostRequest} apiV1TasksInvalidateCachePostRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1TasksInvalidateCachePost(
    apiV1TasksInvalidateCachePostRequest: ApiV1TasksInvalidateCachePostRequest,
    options?: RawAxiosRequestConfig,
  ) {
    return TaskApiFp(this.configuration)
      .apiV1TasksInvalidateCachePost(apiV1TasksInvalidateCachePostRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 创建任务
   * @param {ApiV1TasksPostRequest} apiV1TasksPostRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TaskApi
   */
  public apiV1TasksPost(
    apiV1TasksPostRequest: ApiV1TasksPostRequest,
    options?: RawAxiosRequestConfig,
  ) {
    return TaskApiFp(this.configuration)
      .apiV1TasksPost(apiV1TasksPostRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const ApiV1TasksIdPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1TasksIdPostExecEnvEnum =
  (typeof ApiV1TasksIdPostExecEnvEnum)[keyof typeof ApiV1TasksIdPostExecEnvEnum];

/**
 * TranscribeMediaApi - axios parameter creator
 * @export
 */
export const TranscribeMediaApiAxiosParamCreator = (configuration?: Configuration) => ({
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 音视频转录任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCallbackTranscribeMediaPut: async (
    apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCallbackTranscribeMediaPut',
      'apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult',
      apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult,
    );
    const localVarPath = `/api/v1/shortcut/callbackTranscribeMedia`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'PUT',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 创建并立即异步执行音视频转录任务
   * @param {ApiV1ShortcutCreateTranscribeMediaTaskPostRequest} apiV1ShortcutCreateTranscribeMediaTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateAndRunTranscribeMediaTaskPost: async (
    apiV1ShortcutCreateTranscribeMediaTaskPostRequest: ApiV1ShortcutCreateTranscribeMediaTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateTranscribeMediaTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateAndRunTranscribeMediaTaskPost',
      'apiV1ShortcutCreateTranscribeMediaTaskPostRequest',
      apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createAndRunTranscribeMediaTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    if (sync != null) {
      localVarHeaderParameter['sync'] = String(sync);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks type=transcribe_media 的快捷方式
   * @summary 创建音视频转录任务
   * @param {ApiV1ShortcutCreateTranscribeMediaTaskPostRequest} apiV1ShortcutCreateTranscribeMediaTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateTranscribeMediaTaskPost: async (
    apiV1ShortcutCreateTranscribeMediaTaskPostRequest: ApiV1ShortcutCreateTranscribeMediaTaskPostRequest,
    creatorId?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateTranscribeMediaTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateTranscribeMediaTaskPost',
      'apiV1ShortcutCreateTranscribeMediaTaskPostRequest',
      apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createTranscribeMediaTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks/:id type=transcribe_media 的快捷方式 <p>若指定 <code>exec_env</code> 为 <code>vercel_function</code>，则无法使用 ffmpeg 相关的预转录等高级功能</p> <p>预转录完成后，回调 <code>youget_callback_url</code> 时会添加 header <code>x-youget-partial-result: 1</code>
   * @summary 执行音视频转录任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutRunTranscribeMediaTaskByIdPost: async (
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum,
    useCache?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1TasksPostDefaultResponse' is not null or undefined
    assertParamExists(
      'apiV1ShortcutRunTranscribeMediaTaskByIdPost',
      'apiV1TasksPostDefaultResponse',
      apiV1TasksPostDefaultResponse,
    );
    const localVarPath = `/api/v1/shortcut/runTranscribeMediaTaskById`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksPostDefaultResponse,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
});

/**
 * TranscribeMediaApi - functional programming interface
 * @export
 */
export const TranscribeMediaApiFp = (configuration?: Configuration) => {
  const localVarAxiosParamCreator = TranscribeMediaApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 音视频转录任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCallbackTranscribeMediaPut(
      apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCallbackTranscribeMediaPut(
          apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribeMediaApi.apiV1ShortcutCallbackTranscribeMediaPut']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 创建并立即异步执行音视频转录任务
     * @param {ApiV1ShortcutCreateTranscribeMediaTaskPostRequest} apiV1ShortcutCreateTranscribeMediaTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateAndRunTranscribeMediaTaskPost(
      apiV1ShortcutCreateTranscribeMediaTaskPostRequest: ApiV1ShortcutCreateTranscribeMediaTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateAndRunTranscribeMediaTaskPost(
          apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribeMediaApi.apiV1ShortcutCreateAndRunTranscribeMediaTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks type=transcribe_media 的快捷方式
     * @summary 创建音视频转录任务
     * @param {ApiV1ShortcutCreateTranscribeMediaTaskPostRequest} apiV1ShortcutCreateTranscribeMediaTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateTranscribeMediaTaskPost(
      apiV1ShortcutCreateTranscribeMediaTaskPostRequest: ApiV1ShortcutCreateTranscribeMediaTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateTranscribeMediaTaskPost(
          apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
          creatorId,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribeMediaApi.apiV1ShortcutCreateTranscribeMediaTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks/:id type=transcribe_media 的快捷方式 <p>若指定 <code>exec_env</code> 为 <code>vercel_function</code>，则无法使用 ffmpeg 相关的预转录等高级功能</p> <p>预转录完成后，回调 <code>youget_callback_url</code> 时会添加 header <code>x-youget-partial-result: 1</code>
     * @summary 执行音视频转录任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutRunTranscribeMediaTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutRunTranscribeMediaTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribeMediaApi.apiV1ShortcutRunTranscribeMediaTaskByIdPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * TranscribeMediaApi - factory interface
 * @export
 */
export const TranscribeMediaApiFactory = (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) => {
  const localVarFp = TranscribeMediaApiFp(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 音视频转录任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCallbackTranscribeMediaPut(
      apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .apiV1ShortcutCallbackTranscribeMediaPut(
          apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 创建并立即异步执行音视频转录任务
     * @param {ApiV1ShortcutCreateTranscribeMediaTaskPostRequest} apiV1ShortcutCreateTranscribeMediaTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateAndRunTranscribeMediaTaskPost(
      apiV1ShortcutCreateTranscribeMediaTaskPostRequest: ApiV1ShortcutCreateTranscribeMediaTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateAndRunTranscribeMediaTaskPost(
          apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks type=transcribe_media 的快捷方式
     * @summary 创建音视频转录任务
     * @param {ApiV1ShortcutCreateTranscribeMediaTaskPostRequest} apiV1ShortcutCreateTranscribeMediaTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateTranscribeMediaTaskPost(
      apiV1ShortcutCreateTranscribeMediaTaskPostRequest: ApiV1ShortcutCreateTranscribeMediaTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateTranscribeMediaTaskPost(
          apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
          creatorId,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks/:id type=transcribe_media 的快捷方式 <p>若指定 <code>exec_env</code> 为 <code>vercel_function</code>，则无法使用 ffmpeg 相关的预转录等高级功能</p> <p>预转录完成后，回调 <code>youget_callback_url</code> 时会添加 header <code>x-youget-partial-result: 1</code>
     * @summary 执行音视频转录任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutRunTranscribeMediaTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutRunTranscribeMediaTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * TranscribeMediaApi - object-oriented interface
 * @export
 * @class TranscribeMediaApi
 * @extends {BaseAPI}
 */
export class TranscribeMediaApi extends BaseAPI {
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 音视频转录任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult} apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribeMediaApi
   */
  public apiV1ShortcutCallbackTranscribeMediaPut(
    apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribeMediaApiFp(this.configuration)
      .apiV1ShortcutCallbackTranscribeMediaPut(
        apiV1ShortcutCreateAndRunTranscribeMediaTaskPostDefaultResponseResult,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 创建并立即异步执行音视频转录任务
   * @param {ApiV1ShortcutCreateTranscribeMediaTaskPostRequest} apiV1ShortcutCreateTranscribeMediaTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribeMediaApi
   */
  public apiV1ShortcutCreateAndRunTranscribeMediaTaskPost(
    apiV1ShortcutCreateTranscribeMediaTaskPostRequest: ApiV1ShortcutCreateTranscribeMediaTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribeMediaApiFp(this.configuration)
      .apiV1ShortcutCreateAndRunTranscribeMediaTaskPost(
        apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        creatorId,
        sync,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks type=transcribe_media 的快捷方式
   * @summary 创建音视频转录任务
   * @param {ApiV1ShortcutCreateTranscribeMediaTaskPostRequest} apiV1ShortcutCreateTranscribeMediaTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribeMediaApi
   */
  public apiV1ShortcutCreateTranscribeMediaTaskPost(
    apiV1ShortcutCreateTranscribeMediaTaskPostRequest: ApiV1ShortcutCreateTranscribeMediaTaskPostRequest,
    creatorId?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribeMediaApiFp(this.configuration)
      .apiV1ShortcutCreateTranscribeMediaTaskPost(
        apiV1ShortcutCreateTranscribeMediaTaskPostRequest,
        creatorId,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks/:id type=transcribe_media 的快捷方式 <p>若指定 <code>exec_env</code> 为 <code>vercel_function</code>，则无法使用 ffmpeg 相关的预转录等高级功能</p> <p>预转录完成后，回调 <code>youget_callback_url</code> 时会添加 header <code>x-youget-partial-result: 1</code>
   * @summary 执行音视频转录任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribeMediaApi
   */
  public apiV1ShortcutRunTranscribeMediaTaskByIdPost(
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum,
    useCache?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribeMediaApiFp(this.configuration)
      .apiV1ShortcutRunTranscribeMediaTaskByIdPost(
        apiV1TasksPostDefaultResponse,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum =
  (typeof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum)[keyof typeof ApiV1ShortcutCreateAndRunTranscribeMediaTaskPostExecEnvEnum];
/**
 * @export
 */
export const ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum =
  (typeof ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum)[keyof typeof ApiV1ShortcutRunTranscribeMediaTaskByIdPostExecEnvEnum];

/**
 * TranscribeOfficeApi - axios parameter creator
 * @export
 */
export const TranscribeOfficeApiAxiosParamCreator = (configuration?: Configuration) => ({
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] Office 转录任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult} [apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCallbackTranscribeOfficePut: async (
    apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    const localVarPath = `/api/v1/shortcut/callbackTranscribeOffice`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'PUT',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 创建并立即异步执行Office 转录任务
   * @param {ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest} apiV1ShortcutCreateTranscribeOfficeTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateAndRunTranscribeOfficeTaskPost: async (
    apiV1ShortcutCreateTranscribeOfficeTaskPostRequest: ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateTranscribeOfficeTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateAndRunTranscribeOfficeTaskPost',
      'apiV1ShortcutCreateTranscribeOfficeTaskPostRequest',
      apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createAndRunTranscribeOfficeTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    if (sync != null) {
      localVarHeaderParameter['sync'] = String(sync);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks type=transcribe_office 的快捷方式
   * @summary 创建Office 转录任务
   * @param {ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest} apiV1ShortcutCreateTranscribeOfficeTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateTranscribeOfficeTaskPost: async (
    apiV1ShortcutCreateTranscribeOfficeTaskPostRequest: ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
    creatorId?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateTranscribeOfficeTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateTranscribeOfficeTaskPost',
      'apiV1ShortcutCreateTranscribeOfficeTaskPostRequest',
      apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createTranscribeOfficeTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks/:id type=transcribe_office 的快捷方式
   * @summary 执行Office 转录任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutRunTranscribeOfficeTaskByIdPost: async (
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum,
    useCache?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1TasksPostDefaultResponse' is not null or undefined
    assertParamExists(
      'apiV1ShortcutRunTranscribeOfficeTaskByIdPost',
      'apiV1TasksPostDefaultResponse',
      apiV1TasksPostDefaultResponse,
    );
    const localVarPath = `/api/v1/shortcut/runTranscribeOfficeTaskById`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksPostDefaultResponse,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
});

/**
 * TranscribeOfficeApi - functional programming interface
 * @export
 */
export const TranscribeOfficeApiFp = (configuration?: Configuration) => {
  const localVarAxiosParamCreator = TranscribeOfficeApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] Office 转录任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult} [apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCallbackTranscribeOfficePut(
      apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCallbackTranscribeOfficePut(
          apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribeOfficeApi.apiV1ShortcutCallbackTranscribeOfficePut']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 创建并立即异步执行Office 转录任务
     * @param {ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest} apiV1ShortcutCreateTranscribeOfficeTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateAndRunTranscribeOfficeTaskPost(
      apiV1ShortcutCreateTranscribeOfficeTaskPostRequest: ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateAndRunTranscribeOfficeTaskPost(
          apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap[
          'TranscribeOfficeApi.apiV1ShortcutCreateAndRunTranscribeOfficeTaskPost'
        ]?.[localVarOperationServerIndex]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks type=transcribe_office 的快捷方式
     * @summary 创建Office 转录任务
     * @param {ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest} apiV1ShortcutCreateTranscribeOfficeTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateTranscribeOfficeTaskPost(
      apiV1ShortcutCreateTranscribeOfficeTaskPostRequest: ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateTranscribeOfficeTaskPost(
          apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
          creatorId,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribeOfficeApi.apiV1ShortcutCreateTranscribeOfficeTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks/:id type=transcribe_office 的快捷方式
     * @summary 执行Office 转录任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutRunTranscribeOfficeTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutRunTranscribeOfficeTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribeOfficeApi.apiV1ShortcutRunTranscribeOfficeTaskByIdPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * TranscribeOfficeApi - factory interface
 * @export
 */
export const TranscribeOfficeApiFactory = (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) => {
  const localVarFp = TranscribeOfficeApiFp(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] Office 转录任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult} [apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCallbackTranscribeOfficePut(
      apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .apiV1ShortcutCallbackTranscribeOfficePut(
          apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 创建并立即异步执行Office 转录任务
     * @param {ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest} apiV1ShortcutCreateTranscribeOfficeTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateAndRunTranscribeOfficeTaskPost(
      apiV1ShortcutCreateTranscribeOfficeTaskPostRequest: ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateAndRunTranscribeOfficeTaskPost(
          apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks type=transcribe_office 的快捷方式
     * @summary 创建Office 转录任务
     * @param {ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest} apiV1ShortcutCreateTranscribeOfficeTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateTranscribeOfficeTaskPost(
      apiV1ShortcutCreateTranscribeOfficeTaskPostRequest: ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateTranscribeOfficeTaskPost(
          apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
          creatorId,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks/:id type=transcribe_office 的快捷方式
     * @summary 执行Office 转录任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutRunTranscribeOfficeTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutRunTranscribeOfficeTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * TranscribeOfficeApi - object-oriented interface
 * @export
 * @class TranscribeOfficeApi
 * @extends {BaseAPI}
 */
export class TranscribeOfficeApi extends BaseAPI {
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] Office 转录任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult} [apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribeOfficeApi
   */
  public apiV1ShortcutCallbackTranscribeOfficePut(
    apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribeOfficeApiFp(this.configuration)
      .apiV1ShortcutCallbackTranscribeOfficePut(
        apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 创建并立即异步执行Office 转录任务
   * @param {ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest} apiV1ShortcutCreateTranscribeOfficeTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribeOfficeApi
   */
  public apiV1ShortcutCreateAndRunTranscribeOfficeTaskPost(
    apiV1ShortcutCreateTranscribeOfficeTaskPostRequest: ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribeOfficeApiFp(this.configuration)
      .apiV1ShortcutCreateAndRunTranscribeOfficeTaskPost(
        apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        creatorId,
        sync,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks type=transcribe_office 的快捷方式
   * @summary 创建Office 转录任务
   * @param {ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest} apiV1ShortcutCreateTranscribeOfficeTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribeOfficeApi
   */
  public apiV1ShortcutCreateTranscribeOfficeTaskPost(
    apiV1ShortcutCreateTranscribeOfficeTaskPostRequest: ApiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
    creatorId?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribeOfficeApiFp(this.configuration)
      .apiV1ShortcutCreateTranscribeOfficeTaskPost(
        apiV1ShortcutCreateTranscribeOfficeTaskPostRequest,
        creatorId,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks/:id type=transcribe_office 的快捷方式
   * @summary 执行Office 转录任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribeOfficeApi
   */
  public apiV1ShortcutRunTranscribeOfficeTaskByIdPost(
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum,
    useCache?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribeOfficeApiFp(this.configuration)
      .apiV1ShortcutRunTranscribeOfficeTaskByIdPost(
        apiV1TasksPostDefaultResponse,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum =
  (typeof ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum)[keyof typeof ApiV1ShortcutCreateAndRunTranscribeOfficeTaskPostExecEnvEnum];
/**
 * @export
 */
export const ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum =
  (typeof ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum)[keyof typeof ApiV1ShortcutRunTranscribeOfficeTaskByIdPostExecEnvEnum];

/**
 * TranscribePdfApi - axios parameter creator
 * @export
 */
export const TranscribePdfApiAxiosParamCreator = (configuration?: Configuration) => ({
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 解析 PDF 任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult} [apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCallbackTranscribePdfPut: async (
    apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    const localVarPath = `/api/v1/shortcut/callbackTranscribePdf`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'PUT',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   *
   * @summary 创建并立即异步执行解析 PDF 任务
   * @param {ApiV1ShortcutCreateTranscribePdfTaskPostRequest} apiV1ShortcutCreateTranscribePdfTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateAndRunTranscribePdfTaskPost: async (
    apiV1ShortcutCreateTranscribePdfTaskPostRequest: ApiV1ShortcutCreateTranscribePdfTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateTranscribePdfTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateAndRunTranscribePdfTaskPost',
      'apiV1ShortcutCreateTranscribePdfTaskPostRequest',
      apiV1ShortcutCreateTranscribePdfTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createAndRunTranscribePdfTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    if (sync != null) {
      localVarHeaderParameter['sync'] = String(sync);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateTranscribePdfTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks type=transcribe_pdf 的快捷方式
   * @summary 创建解析 PDF 任务
   * @param {ApiV1ShortcutCreateTranscribePdfTaskPostRequest} apiV1ShortcutCreateTranscribePdfTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutCreateTranscribePdfTaskPost: async (
    apiV1ShortcutCreateTranscribePdfTaskPostRequest: ApiV1ShortcutCreateTranscribePdfTaskPostRequest,
    creatorId?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1ShortcutCreateTranscribePdfTaskPostRequest' is not null or undefined
    assertParamExists(
      'apiV1ShortcutCreateTranscribePdfTaskPost',
      'apiV1ShortcutCreateTranscribePdfTaskPostRequest',
      apiV1ShortcutCreateTranscribePdfTaskPostRequest,
    );
    const localVarPath = `/api/v1/shortcut/createTranscribePdfTask`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (creatorId != null) {
      localVarHeaderParameter['creator_id'] = String(creatorId);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1ShortcutCreateTranscribePdfTaskPostRequest,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
  /**
   * POST /tasks/:id type=transcribe_pdf 的快捷方式
   * @summary 执行解析 PDF 任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   */
  apiV1ShortcutRunTranscribePdfTaskByIdPost: async (
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum,
    useCache?: string,
    options: RawAxiosRequestConfig = {},
  ): Promise<RequestArgs> => {
    // verify required parameter 'apiV1TasksPostDefaultResponse' is not null or undefined
    assertParamExists(
      'apiV1ShortcutRunTranscribePdfTaskByIdPost',
      'apiV1TasksPostDefaultResponse',
      apiV1TasksPostDefaultResponse,
    );
    const localVarPath = `/api/v1/shortcut/runTranscribePdfTaskById`;
    // use dummy base URL string because the URL constructor only accepts absolute URLs.
    const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
    let baseOptions;
    if (configuration) {
      baseOptions = configuration.baseOptions;
    }

    const localVarRequestOptions = {
      method: 'POST',
      ...baseOptions,
      ...options,
    };
    const localVarHeaderParameter = {} as any;
    const localVarQueryParameter = {} as any;

    localVarHeaderParameter['Content-Type'] = 'application/json';

    if (force != null) {
      localVarHeaderParameter['force'] = String(force);
    }
    if (callbackWhenError != null) {
      localVarHeaderParameter['callback_when_error'] = String(callbackWhenError);
    }
    if (execEnv != null) {
      localVarHeaderParameter['exec_env'] = String(execEnv);
    }
    if (useCache != null) {
      localVarHeaderParameter['use_cache'] = String(useCache);
    }
    setSearchParams(localVarUrlObj, localVarQueryParameter);
    const headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
    localVarRequestOptions.headers = {
      ...localVarHeaderParameter,
      ...headersFromBaseOptions,
      ...options.headers,
    };
    localVarRequestOptions.data = serializeDataIfNeeded(
      apiV1TasksPostDefaultResponse,
      localVarRequestOptions,
      configuration,
    );

    return {
      url: toPathString(localVarUrlObj),
      options: localVarRequestOptions,
    };
  },
});

/**
 * TranscribePdfApi - functional programming interface
 * @export
 */
export const TranscribePdfApiFp = (configuration?: Configuration) => {
  const localVarAxiosParamCreator = TranscribePdfApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 解析 PDF 任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult} [apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCallbackTranscribePdfPut(
      apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCallbackTranscribePdfPut(
          apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribePdfApi.apiV1ShortcutCallbackTranscribePdfPut']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary 创建并立即异步执行解析 PDF 任务
     * @param {ApiV1ShortcutCreateTranscribePdfTaskPostRequest} apiV1ShortcutCreateTranscribePdfTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateAndRunTranscribePdfTaskPost(
      apiV1ShortcutCreateTranscribePdfTaskPostRequest: ApiV1ShortcutCreateTranscribePdfTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateAndRunTranscribePdfTaskPost(
          apiV1ShortcutCreateTranscribePdfTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribePdfApi.apiV1ShortcutCreateAndRunTranscribePdfTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks type=transcribe_pdf 的快捷方式
     * @summary 创建解析 PDF 任务
     * @param {ApiV1ShortcutCreateTranscribePdfTaskPostRequest} apiV1ShortcutCreateTranscribePdfTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutCreateTranscribePdfTaskPost(
      apiV1ShortcutCreateTranscribePdfTaskPostRequest: ApiV1ShortcutCreateTranscribePdfTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ApiV1TasksPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutCreateTranscribePdfTaskPost(
          apiV1ShortcutCreateTranscribePdfTaskPostRequest,
          creatorId,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribePdfApi.apiV1ShortcutCreateTranscribePdfTaskPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     * POST /tasks/:id type=transcribe_pdf 的快捷方式
     * @summary 执行解析 PDF 任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async apiV1ShortcutRunTranscribePdfTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.apiV1ShortcutRunTranscribePdfTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['TranscribePdfApi.apiV1ShortcutRunTranscribePdfTaskByIdPost']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * TranscribePdfApi - factory interface
 * @export
 */
export const TranscribePdfApiFactory = (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) => {
  const localVarFp = TranscribePdfApiFp(configuration);
  return {
    /**
     *
     * @summary [仅供类型推导，请勿直接调用] 解析 PDF 任务 回调时的入参
     * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult} [apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCallbackTranscribePdfPut(
      apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .apiV1ShortcutCallbackTranscribePdfPut(
          apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary 创建并立即异步执行解析 PDF 任务
     * @param {ApiV1ShortcutCreateTranscribePdfTaskPostRequest} apiV1ShortcutCreateTranscribePdfTaskPostRequest
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {string} [creatorId]
     * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateAndRunTranscribePdfTaskPost(
      apiV1ShortcutCreateTranscribePdfTaskPostRequest: ApiV1ShortcutCreateTranscribePdfTaskPostRequest,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum,
      useCache?: string,
      creatorId?: string,
      sync?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateAndRunTranscribePdfTaskPost(
          apiV1ShortcutCreateTranscribePdfTaskPostRequest,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          creatorId,
          sync,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks type=transcribe_pdf 的快捷方式
     * @summary 创建解析 PDF 任务
     * @param {ApiV1ShortcutCreateTranscribePdfTaskPostRequest} apiV1ShortcutCreateTranscribePdfTaskPostRequest
     * @param {string} [creatorId]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutCreateTranscribePdfTaskPost(
      apiV1ShortcutCreateTranscribePdfTaskPostRequest: ApiV1ShortcutCreateTranscribePdfTaskPostRequest,
      creatorId?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1TasksPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutCreateTranscribePdfTaskPost(
          apiV1ShortcutCreateTranscribePdfTaskPostRequest,
          creatorId,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * POST /tasks/:id type=transcribe_pdf 的快捷方式
     * @summary 执行解析 PDF 任务
     * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
     * @param {string} [force] 是否强制执行
     * @param {string} [callbackWhenError] 是否在错误时回调
     * @param {ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
     * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    apiV1ShortcutRunTranscribePdfTaskByIdPost(
      apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
      force?: string,
      callbackWhenError?: string,
      execEnv?: ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum,
      useCache?: string,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<ApiV1ShortcutRunTranscribePdfTaskByIdPostDefaultResponse> {
      return localVarFp
        .apiV1ShortcutRunTranscribePdfTaskByIdPost(
          apiV1TasksPostDefaultResponse,
          force,
          callbackWhenError,
          execEnv,
          useCache,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * TranscribePdfApi - object-oriented interface
 * @export
 * @class TranscribePdfApi
 * @extends {BaseAPI}
 */
export class TranscribePdfApi extends BaseAPI {
  /**
   *
   * @summary [仅供类型推导，请勿直接调用] 解析 PDF 任务 回调时的入参
   * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult} [apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribePdfApi
   */
  public apiV1ShortcutCallbackTranscribePdfPut(
    apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribePdfApiFp(this.configuration)
      .apiV1ShortcutCallbackTranscribePdfPut(
        apiV1ShortcutCreateAndRunTranscribePdfTaskPostDefaultResponseResult,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary 创建并立即异步执行解析 PDF 任务
   * @param {ApiV1ShortcutCreateTranscribePdfTaskPostRequest} apiV1ShortcutCreateTranscribePdfTaskPostRequest
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {string} [creatorId]
   * @param {string} [sync] 是否同步执行, 0 为异步, 1 为同步
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribePdfApi
   */
  public apiV1ShortcutCreateAndRunTranscribePdfTaskPost(
    apiV1ShortcutCreateTranscribePdfTaskPostRequest: ApiV1ShortcutCreateTranscribePdfTaskPostRequest,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum,
    useCache?: string,
    creatorId?: string,
    sync?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribePdfApiFp(this.configuration)
      .apiV1ShortcutCreateAndRunTranscribePdfTaskPost(
        apiV1ShortcutCreateTranscribePdfTaskPostRequest,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        creatorId,
        sync,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks type=transcribe_pdf 的快捷方式
   * @summary 创建解析 PDF 任务
   * @param {ApiV1ShortcutCreateTranscribePdfTaskPostRequest} apiV1ShortcutCreateTranscribePdfTaskPostRequest
   * @param {string} [creatorId]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribePdfApi
   */
  public apiV1ShortcutCreateTranscribePdfTaskPost(
    apiV1ShortcutCreateTranscribePdfTaskPostRequest: ApiV1ShortcutCreateTranscribePdfTaskPostRequest,
    creatorId?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribePdfApiFp(this.configuration)
      .apiV1ShortcutCreateTranscribePdfTaskPost(
        apiV1ShortcutCreateTranscribePdfTaskPostRequest,
        creatorId,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * POST /tasks/:id type=transcribe_pdf 的快捷方式
   * @summary 执行解析 PDF 任务
   * @param {ApiV1TasksPostDefaultResponse} apiV1TasksPostDefaultResponse
   * @param {string} [force] 是否强制执行
   * @param {string} [callbackWhenError] 是否在错误时回调
   * @param {ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum} [execEnv] 指定执行环境，默认自动判断
   * @param {string} [useCache] 是否使用缓存，传 \&#39;0\&#39; 不使用
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof TranscribePdfApi
   */
  public apiV1ShortcutRunTranscribePdfTaskByIdPost(
    apiV1TasksPostDefaultResponse: ApiV1TasksPostDefaultResponse,
    force?: string,
    callbackWhenError?: string,
    execEnv?: ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum,
    useCache?: string,
    options?: RawAxiosRequestConfig,
  ) {
    return TranscribePdfApiFp(this.configuration)
      .apiV1ShortcutRunTranscribePdfTaskByIdPost(
        apiV1TasksPostDefaultResponse,
        force,
        callbackWhenError,
        execEnv,
        useCache,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum =
  (typeof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum)[keyof typeof ApiV1ShortcutCreateAndRunTranscribePdfTaskPostExecEnvEnum];
/**
 * @export
 */
export const ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum = {
  VercelFunction: 'vercel_function',
  AwsLambda: 'aws_lambda',
} as const;
export type ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum =
  (typeof ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum)[keyof typeof ApiV1ShortcutRunTranscribePdfTaskByIdPostExecEnvEnum];
