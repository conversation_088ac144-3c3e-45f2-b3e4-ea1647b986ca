/**
 * Board Domain Service - 看板领域服务
 * 处理看板相关的业务逻辑
 *
 * Migrated from:
 * - youapp/src/lib/domain/board/index.ts
 */

import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { WebpageExists } from '@/common/errors';
import { rankBetween } from '@/common/utils';
import { BoardDAO } from '@/dao/board';
import { BoardDO } from '@/dao/board/types';
import { BoardItemDAO } from '@/dao/board-item';
import { BoardGroupDomainService } from '../board-group';
import {
  type Article,
  type OtherWebpage,
  type Snip,
  SnipDomainService,
  type Video,
  type Voice,
} from '../snip';
import { ThoughtDomainService } from '../thought';
import {
  type Board,
  BoardStatusEnum,
  BoardTemplateEnum,
  BoardTypeEnum,
  type CreateBoardFromTemplateParam,
  type CreateBoardParam,
  type PatchBoardParam,
} from './types';

@Injectable()
export class BoardDomainService {
  private readonly logger = new Logger(BoardDomainService.name);

  constructor(
    private readonly boardDao: BoardDAO,
    private readonly boardItemDao: BoardItemDAO,
    @Inject(forwardRef(() => BoardGroupDomainService))
    private readonly boardGroupDomain: BoardGroupDomainService,
    private readonly snipDomain: SnipDomainService,
    private readonly thoughtDomain: ThoughtDomainService,
    private readonly eventBus: EventBus,
  ) {}

  async create(param: CreateBoardParam): Promise<Board> {
    // 获取当前space下的rank范围
    const range = await this.getRankRange(param.space_id);
    const boardDO = await this.boardDao.insert({
      name: param.name,
      description: param.description,
      icon: param.icon,
      space_id: param.space_id,
      creator_id: param.user_id,
      // 使用rankBetween生成合适的rank值
      rank: rankBetween(range[0], range[1]),
    });
    return this.do2board(boardDO);
  }

  async createDefaultBoard(param: { space_id: string; creator_id: string }): Promise<Board> {
    // 获取当前space下的rank范围
    const range = await this.getRankRange(param.space_id);
    const boardDO = await this.boardDao.insert({
      ...param,
      name: 'Chaos',
      description: '',
      icon: {
        name: 'Database',
        color: '--foreground',
      },
      type: BoardTypeEnum.DEFAULT,
      // 使用rankBetween生成合适的rank值
      rank: rankBetween(range[0], range[1]),
    });
    return this.do2board(boardDO);
  }

  async getDefaultBoard(space_id: string): Promise<Board> {
    const boardDO = await this.boardDao.selectDefault(space_id);
    return this.do2board(boardDO);
  }

  /**
   * 获取rank范围，用于确定新的board的rank值
   * @param space_id 空间ID
   * @param rank_after_id 可选，要在其后面插入的boardID
   * @returns rank范围 [开始值, 结束值]
   */
  private async getRankRange(
    space_id: string,
    rank_after_id?: string,
  ): Promise<[string | undefined, string | undefined]> {
    if (rank_after_id) {
      // 如果指定了rank_after_id，则获取对应board的rank值
      const rankAfterBoard = await this.boardDao.selectById(rank_after_id);

      if (rankAfterBoard.space_id !== space_id) {
        throw new Error('Cannot move board across different spaces');
      }

      // 查找排在rank_after_id之后的第一个board
      const nextBoard = await this.boardDao.trySelectNextRank(space_id, rankAfterBoard.rank);

      // 返回 [rankAfterBoard.rank, nextBoard?.rank]
      return [rankAfterBoard.rank, nextBoard?.rank];
    } else {
      // 如果没有指定rank_after_id，则将board放到最前面
      // 获取space下的第一个board
      const firstBoard = await this.boardDao.trySelectFirst(space_id);

      // 返回 [undefined, firstBoard?.rank]
      return [undefined, firstBoard?.rank];
    }
  }

  async createFromTemplate(param: CreateBoardFromTemplateParam): Promise<Board> {
    let template: BoardDO;
    switch (param.template) {
      case BoardTemplateEnum.TEMPLATE_1:
        template = await this.boardDao.selectById(process.env.BOARD_TEMPLATE_1!);
        break;
      case BoardTemplateEnum.TEMPLATE_2:
        template = await this.boardDao.selectById(process.env.BOARD_TEMPLATE_2!);
        break;
      case BoardTemplateEnum.TEMPLATE_3:
        template = await this.boardDao.selectById(process.env.BOARD_TEMPLATE_3!);
        break;
    }
    // 获取当前space下的rank范围
    const range = await this.getRankRange(param.space_id);

    // 复制 board
    const board = await this.boardDao.insert({
      name: template.name,
      description: template.description,
      icon: {
        name: template.icon_name,
        color: template.icon_color,
      },
      space_id: param.space_id,
      creator_id: param.user_id,
      // 使用rankBetween生成合适的rank值
      rank: rankBetween(range[0], range[1]),
    });

    const templateItems = await this.boardItemDao.selectByBoardId({
      board_id: template.id,
    });

    const templateBoardGroupItems = templateItems.filter((it) => !!it.board_group_id);
    const boardGroupMap = new Map<string, string>();
    // 复制 board_groups
    for await (const templateBoardGroupItem of templateBoardGroupItems) {
      const boardGroup = await this.boardGroupDomain.clone(
        templateBoardGroupItem.board_group_id!,
        param.user_id,
        board.id,
      );
      await this.boardItemDao.insert({
        board_id: board.id,
        board_group_id: boardGroup.id,
        rank: templateBoardGroupItem.rank,
      });
      boardGroupMap.set(templateBoardGroupItem.board_group_id!, boardGroup.id);
    }

    // 复制 board_items
    for await (const templateItem of templateItems) {
      const parentBoardGroupId =
        templateItem.parent_board_group_id && boardGroupMap.get(templateItem.parent_board_group_id);
      if (templateItem.snip_id) {
        try {
          const snip = await this.snipDomain.clone(
            templateItem.snip_id,
            param.user_id,
            param.space_id,
          );
          await this.boardItemDao.insert({
            board_id: board.id,
            snip_id: snip.id!,
            rank: templateItem.rank,
            parent_board_group_id: parentBoardGroupId,
          });
        } catch (e) {
          if (e instanceof WebpageExists) {
            // 如果冲突了，则直接根据 normalized_url 获取已有的 snip
            const templateSnip = await this.snipDomain.getById(templateItem.snip_id);
            const existSnip = (await this.snipDomain.tryGetWebpageByNormalizedUrl(
              param.space_id,
              (templateSnip as Article | Voice | Video | OtherWebpage).webpage!.normalized_url!,
            )) as Snip;
            // 将已有的 snip 直接移动到新 board，克隆 rank
            await this.boardItemDao.deleteBySnipId(existSnip.id!);
            await this.boardItemDao.insert({
              board_id: board.id,
              snip_id: existSnip.id!,
              rank: templateItem.rank,
              parent_board_group_id: parentBoardGroupId,
            });
          } else {
            this.logger.warn(
              `clone snip error: template_snip_id '${templateItem.snip_id}', user_id '${param.user_id}', space_id '${param.space_id}'`,
            );
          }
        }
      } else if (templateItem.thought_id) {
        const thought = await this.thoughtDomain.clone(
          templateItem.thought_id!,
          param.user_id,
          param.space_id,
        );
        await this.boardItemDao.insert({
          board_id: board.id,
          thought_id: thought.id,
          rank: templateItem.rank,
          parent_board_group_id: parentBoardGroupId,
        });
      }
    }
    // 通知更新 hero_image_urls
    this.notifyBoardItemsChange(board.id);
    return this.do2board(board);
  }

  async rankAfter(id: string, rank_after_id?: string): Promise<Board> {
    // 获取当前要调整rank的board
    const board = await this.boardDao.selectById(id);
    const space_id = board.space_id;

    // 使用改造后的getRankRange方法获取rank范围
    const [beforeRank, afterRank] = await this.getRankRange(space_id, rank_after_id);

    // 生成新的rank值
    const newRank = rankBetween(beforeRank, afterRank);

    // 更新board的rank
    const updated = await this.boardDao.update({
      id,
      rank: newRank,
    });

    return this.do2board(updated);
  }

  async patch(param: PatchBoardParam): Promise<Board> {
    const { id, ...rest } = param;
    await this.boardDao.update({
      id,
      ...rest,
      icon_name: rest.icon?.name,
      icon_color: rest.icon?.color,
    });
    return this.getById(id);
  }

  async tryGetById(id: string): Promise<Board | null> {
    const boardDO = await this.boardDao.trySelectById(id);
    return boardDO ? this.do2board(boardDO) : null;
  }

  async getById(id: string): Promise<Board> {
    return this.do2board(await this.boardDao.selectById(id));
  }

  async delete(id: string): Promise<void> {
    await this.boardDao.softDelete(id);
  }

  async pin(id: string): Promise<void> {
    await this.boardDao.update({
      id,
      pinned_at: new Date(),
    });
  }

  async unpin(id: string): Promise<void> {
    await this.boardDao.update({
      id,
      pinned_at: null,
    });
  }

  async archive(id: string): Promise<Board> {
    const updated = await this.boardDao.update({
      id,
      status: BoardStatusEnum.OTHER,
    });
    return this.do2board(updated);
  }

  async unarchive(id: string): Promise<Board> {
    const updated = await this.boardDao.update({
      id,
      status: BoardStatusEnum.IN_PROGRESS,
    });
    return this.do2board(updated);
  }

  async list(space_id: string, fuzzy_name?: string, status?: BoardStatusEnum): Promise<Board[]> {
    const boardDOs = await this.boardDao.list({
      space_id,
      fuzzy_name,
      status,
    });
    return boardDOs.map((boardDO: BoardDO) => this.do2board(boardDO));
  }

  async listBySnipId(snip_id: string): Promise<Board[]> {
    const boardDOs = await this.boardDao.listBySnipId(snip_id);
    return boardDOs.map((boardDO: BoardDO) => this.do2board(boardDO));
  }

  async listByThoughtId(thought_id: string): Promise<Board[]> {
    const boardDOs = await this.boardDao.listByThoughtId(thought_id);
    return boardDOs.map((boardDO: BoardDO) => this.do2board(boardDO));
  }

  async notifyBoardItemsChange(board_id: string) {
    // Update board's updated_at timestamp when board items change
    // 使用 Promise 替代 runInBackground
    this.boardDao
      .update({
        id: board_id,
      })
      .catch((error) => {
        this.logger.error('Failed to update board timestamp', error);
      });
  }

  async listSimpleBoardsByEntityIds(snipIds: string[], thoughtIds: string[]) {
    const boardInfos = await this.boardItemDao.selectBoardsInfoByEntityIds(snipIds, thoughtIds);

    return boardInfos.map((boardInfo) => ({
      id: boardInfo.id,
      name: boardInfo.name,
      icon: {
        name: boardInfo.icon_name,
        color: boardInfo.icon_color,
      },
      snip_ids: boardInfo.snip_ids,
      thought_ids: boardInfo.thought_ids,
    }));
  }

  async getBriefById(id: string) {
    const boardDO = await this.boardDao.selectById(id);
    return {
      id: boardDO.id,
      name: boardDO.name,
      icon: {
        name: boardDO.icon_name,
        color: boardDO.icon_color,
      },
    };
  }

  async listBriefsByIds(ids: string[]) {
    const boardDOs = await this.boardDao.selectByIds(ids);
    return boardDOs.map((boardDO: BoardDO) => ({
      id: boardDO.id,
      name: boardDO.name,
      icon: {
        name: boardDO.icon_name,
        color: boardDO.icon_color,
      },
    }));
  }

  async countAllByCreatorId(creator_id: string) {
    const count = await this.boardDao.countByCreatorId(creator_id);
    return count;
  }

  do2board(boardDO: BoardDO): Board {
    return {
      id: boardDO.id,
      space_id: boardDO.space_id,
      creator_id: boardDO.creator_id,
      created_at: boardDO.created_at,
      updated_at: boardDO.updated_at,
      name: boardDO.name,
      description: boardDO.description,
      icon: {
        name: boardDO.icon_name,
        color: boardDO.icon_color,
      },
      status: boardDO.status as BoardStatusEnum,
      type: boardDO.type as BoardTypeEnum,
      pinned_at: boardDO.pinned_at ?? undefined,
      hero_image_urls: boardDO.hero_image_urls ?? undefined,
      intro: boardDO.intro ?? undefined,
    };
  }
}
