export interface Board {
  /**
   * 唯一标识
   */
  id: string;
  /**
   * 空间 ID
   */
  space_id: string;
  /**
   * 创建人 ID
   */
  creator_id: string;
  /**
   * 创建时间
   */
  created_at: Date;
  /**
   * 更新时间
   */
  updated_at: Date;
  /**
   * 名称
   */
  name: string;
  /**
   * 描述
   */
  description: string;
  /**
   * 图标
   */
  icon: Icon;
  /**
   * 置顶时间
   */
  pinned_at?: Date;
  /**
   * 状态
   */
  status: BoardStatusEnum;
  /**
   * 类型
   */
  type: BoardTypeEnum;
  /**
   * 头图
   */
  hero_image_urls?: string[];
  /**
   * 简介
   */
  intro?: string;
}

export interface Icon {
  name: string;
  color: string;
}

export interface CreateBoardParam {
  space_id: string;
  user_id: string;
  name: string;
  description: string;
  icon: Icon;
  pinned_at?: Date;
}

export interface CreateBoardFromTemplateParam {
  space_id: string;
  user_id: string;
  template: BoardTemplateEnum;
}

export interface PatchBoardParam {
  id: string;
  name?: string;
  description?: string;
  icon?: Icon;
  status?: BoardStatusEnum;
}

export enum BoardTypeEnum {
  /** @deprecated */
  SMART = 'smart',
  DEFAULT = 'default',
  NORMAL = 'normal',
}

export enum BoardStatusEnum {
  IN_PROGRESS = 'in-progress',
  OTHER = 'other',
}

export enum BoardTemplateEnum {
  TEMPLATE_1 = 'template-1',
  TEMPLATE_2 = 'template-2',
  TEMPLATE_3 = 'template-3',
}
