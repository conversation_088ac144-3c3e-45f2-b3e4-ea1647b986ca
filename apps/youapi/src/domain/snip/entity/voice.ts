import {
  ContentFormatEnum,
  DefaultLanguage<PERSON><PERSON>,
  type <PERSON><PERSON>num,
  ReaderHtmlHandler,
  SubtitleHandler,
} from '@repo/common';
import { z } from 'zod';
import { Processing } from '@/common/errors';
import { getLanguageEnum, VisibilityEnum } from '@/common/types';
import { extractImageUrlsFromMarkdown, SafeParse } from '@/common/utils';
import { InsertSnipDOParam, SnipDO } from '@/dao/snip/types';
import { StreamMessage } from '@/infra/youllm';
import type { ChatSnipContext } from '../../chat/types';
import {
  type AsFile,
  type Author,
  type CallLLMParams,
  OnWebhookCompletionActionEnum,
  type ReaderHTMLContent,
  SnipFromEnum,
  type SnipStatusEnum,
  SnipTypeEnum,
  type TransferredFileMetaWithUrl,
  type UploadFileMeta,
  type WebpageMeta,
} from '../types';
import Snip, { type HeroImageMetadata } from './snip';
import UnknownWebpage from './unknown-webpage';

export default class Voice extends Snip implements AsFile {
  file: UploadFileMeta;

  readonly play_url?: string;
  readonly webpage?: WebpageMeta;
  readonly published_at?: Date;
  hero_image_url?: string;
  authors?: Author[];
  show_notes?: ReaderHTMLContent;

  constructor(param: {
    id?: string;
    created_at?: Date;
    updated_at?: Date;
    space_id: string;
    creator_id: string;
    webpage?: WebpageMeta;
    play_url?: string;
    title?: string;
    hero_image_url?: string;
    show_notes?: ReaderHTMLContent;
    authors?: Author[];
    published_at?: Date;
    status?: SnipStatusEnum | null;
    extra?: string;
    file?: UploadFileMeta;
    from?: SnipFromEnum;
    visibility?: VisibilityEnum;
  }) {
    super({
      from: SnipFromEnum.WEBPAGE,
      ...param,
      type: SnipTypeEnum.VOICE,
      title: param.title || param.webpage?.title || param.file?.name || '',
    });
    this.webpage = param.webpage;
    this.play_url = param.play_url;
    this.show_notes = param.show_notes;
    this.authors = param.authors;
    this.published_at = param.published_at;
    this.hero_image_url = param.hero_image_url;
    this.file = param.file!;
    this.extra = param.extra;
  }

  async create() {
    if (this.id) {
      return;
    }
    let insertParam: InsertSnipDOParam;
    if (this.file?.storage_url) {
      const play_url = this.fileDomain.getPlayUrlByStorageUrl(this.file.storage_url);
      insertParam = {
        space_id: this.space_id,
        creator_id: this.creator_id,
        type: this.type,
        from: this.from,
        file_storage_url: this.file.storage_url,
        file_name: this.file.name,
        file_mime_type: this.file.mime_type,
        file_size: this.file.size,
        status: this.status,
        play_url,
        title: this.title,
        extra: this.extra,
        hero_image_url: this.hero_image_url,
      } as InsertSnipDOParam;
    } else {
      insertParam = {
        space_id: this.space_id,
        creator_id: this.creator_id,
        type: this.type,
        from: this.from,
        status: this.status,
        play_url: this.play_url,
        webpage_url: this.webpage?.url,
        webpage_normalized_url: this.webpage?.normalized_url,
        webpage_title: this.webpage?.title,
        webpage_description: this.webpage?.description,
        webpage_site_name: this.webpage?.site.name,
        webpage_site_host: this.webpage?.site.host,
        webpage_site_favicon_url: this.webpage?.site.favicon_url,
        title: this.title,
        content_language:
          this.show_notes?.language ?? getLanguageEnum(this.show_notes?.plain) ?? null,
        content_format: this.show_notes?.format,
        content_raw: this.show_notes?.raw,
        content_plain: this.show_notes?.plain,
        hero_image_url: this.hero_image_url,
        authors: this.authors,
        published_at: this.published_at,
        extra: this.extra,
      } as InsertSnipDOParam;
    }
    const snipDO = await this.snipDAO.insert(insertParam);
    this.fill(snipDO);
  }

  async update() {
    if (!this.id) {
      return;
    }
    await this.snipDAO.update(this.id, {
      type: this.type,
      status: this.status,
      play_url: this.play_url,
      webpage_title: this.webpage?.title,
      webpage_description: this.webpage?.description,
      webpage_site_name: this.webpage?.site.name,
      webpage_site_host: this.webpage?.site.host,
      webpage_site_favicon_url: this.webpage?.site.favicon_url,
      title: this.title,
      hero_image_url: this.hero_image_url,
      authors: this.authors,
      published_at: this.published_at,
      content_language:
        this.show_notes?.language ?? getLanguageEnum(this.show_notes?.plain) ?? null,
      content_format: this.show_notes?.format,
      content_raw: this.show_notes?.raw,
      content_plain: this.show_notes?.plain,
      extra: this.extra,
    });
  }

  canExtractHeroImageUrl(): boolean {
    return !this.status && !!this.hero_image_url;
  }

  extractHeroImageUrl() {
    return this.hero_image_url;
  }

  extractImageUrls(): string[] {
    let imageUrls: string[] = this.show_notes?.plain
      ? extractImageUrlsFromMarkdown(this.show_notes.plain)
      : [];
    if (this.hero_image_url) {
      imageUrls.push(this.hero_image_url);
    }

    if (Array.isArray(this.authors)) {
      const authorPictures = this.authors
        .map((author) => z.string().url().safeParse(author.picture).data)
        .filter((url): url is string => Boolean(url));

      if (authorPictures.length) {
        imageUrls.push(...authorPictures);
      }
    }

    // remove duplicated urls
    imageUrls = Array.from(new Set(imageUrls));

    return imageUrls;
  }

  replaceImageUrls(images: TransferredFileMetaWithUrl[]) {
    images.forEach((image) => {
      if (this.show_notes) {
        this.show_notes.raw = this.show_notes.raw.replaceAll(image.original_url, image.url);
        this.show_notes.plain = this.show_notes.plain?.replaceAll(image.original_url, image.url);
      }

      // if current image is hero image, update snip metadata
      if (this.hero_image_url === image.original_url) {
        const heroImageMetadata: HeroImageMetadata = {};

        if (image.blurhash) {
          heroImageMetadata.blurhash = image.blurhash;
        }
        if (image.width) {
          heroImageMetadata.width = image.width;
        }
        if (image.height) {
          heroImageMetadata.height = image.height;
        }
        if (image.average) {
          heroImageMetadata.average = image.average;
        }

        this.extra = JSON.stringify({
          ...SafeParse(this.extra ?? '', false, {}),
          hero_image_metadata: heroImageMetadata,
        });
      }

      this.hero_image_url = this.hero_image_url?.replace(image.original_url, image.url);
      this.authors?.forEach((author) => {
        author.picture = author.picture?.replace(image.original_url, image.url);
      });
    });
  }

  async callOverview(param: CallLLMParams) {
    const { userId } = param;
    const transcripts = await this.blockDomain.queryTranscriptContentBySnip({
      snip_id: this.id!,
    });
    if (!transcripts.length) {
      await this.extractTranscript({
        creator_id: userId,
        on_completion: OnWebhookCompletionActionEnum.CALL_OVERVIEW,
      });
      throw new Processing();
    }

    const transcript =
      transcripts.find((t) => t.format === ContentFormatEnum.SUBTITLE) ?? transcripts[0];
    const handler = SubtitleHandler.fromRaw(transcript.raw || '');
    const cues = handler.getCues();

    // 多发言人场景
    const speakers: string[] = [];
    cues.forEach((cue) => {
      if (cue.speaker && !speakers.includes(cue.speaker)) {
        speakers.push(cue.speaker);
      }
    });

    const speakerLabels = speakers.length > 1 ? speakers.length.toString() : undefined;

    return (await this.youllm.overview(
      {
        pageData: {
          type: this.type,
          webpage: this.webpage,
          show_notes: this.show_notes,
          transcript,
          title: this.webpage?.title || this.title,
        },
        aiLanguage: param.responseLanguage,
        aiSecondLanguage: param.secondResponseLanguage,
        enableBilingual: !!param.secondResponseLanguage,
        speakerLabels,
      },
      true,
      {
        traceArgs: {
          metadata: {
            snipId: this.id!,
            url: this.webpage?.url,
          },
        },
        regenerate: param.regenerate ?? false,
      },
    )) as AsyncIterable<StreamMessage>;
  }

  async callTranslate(param: CallLLMParams) {
    const { responseLanguage } = param;
    const transcripts = await this.blockDomain.queryTranscriptContentBySnip({
      snip_id: this.id!,
    });
    if (!transcripts.length) {
      throw new Error('Transcript not found');
    }

    const transcript = SubtitleHandler.fromRaw(transcripts[0].raw as string);
    return (await this.youllm.translateTranscript(
      {
        language: responseLanguage || DefaultLanguageKey,
        plainContent: transcript.toLLMInput() || '',
      },
      true,
      {
        traceArgs: {
          metadata: {
            snipId: this.id!,
            url: this.webpage?.url,
          },
        },
        regenerate: param.regenerate ?? false,
      },
    )) as AsyncIterable<StreamMessage>;
  }

  async callFormattedTranscript(param: CallLLMParams) {
    const { formatted_transcript_segment } = param;
    return this.youllm.subtitlePunctuation(
      {
        text: formatted_transcript_segment || '',
        name_candidates: this.authors?.map((author) => author.name).join('\n') || '',
      },
      true,
      {
        traceArgs: {
          metadata: {
            snipId: this.id!,
          },
        },
        regenerate: param.regenerate ?? false,
      },
    );
  }

  extractContent() {
    return this.show_notes ? ReaderHtmlHandler.fromVO(this.show_notes) : null;
  }

  async buildChatContext(): Promise<ChatSnipContext> {
    const overviews = await this.blockDomain.queryOverviewContentBySnip({
      snip_id: this.id!,
    });
    const plainOverview = overviews.find((t) => !!t.plain)?.plain || '';
    const transcripts = await this.blockDomain.queryTranscriptContentBySnip({
      snip_id: this.id!,
    });
    const plainTranscript = transcripts.find((t) => !!t.plain)?.plain || '';
    const plainShowNotes = this.extractContent()?.toLLMInput() || '';

    return {
      ...(await super.buildChatContext()),
      title: this.title || this.webpage?.title || '',
      description: plainOverview || plainShowNotes || '',
      content: plainTranscript || 'No transcript available, please respond by asking to generate',
      relatedId: this.id!,
      describeSelf: 'podcast/voice/media/webpage/recording',
    };
  }

  static of(snipDO: SnipDO) {
    if (!snipDO.file_storage_url) {
      return new Voice({
        ...snipDO,
        from: (snipDO.from as SnipFromEnum) ?? undefined,
        status: (snipDO.status as SnipStatusEnum) ?? undefined,
        webpage: {
          url: snipDO.webpage_url!,
          normalized_url: snipDO.webpage_normalized_url!,
          title: snipDO.title ?? '',
          description: snipDO.webpage_description!,
          site: {
            name: snipDO.webpage_site_name!,
            host: snipDO.webpage_site_host!,
            favicon_url: snipDO.webpage_site_favicon_url!,
          },
        },
        play_url: snipDO.play_url ?? undefined,
        title: snipDO.title ?? '',
        show_notes: {
          format: ContentFormatEnum.READER_HTML,
          raw: snipDO.content_raw ?? '',
          plain: snipDO.content_plain ?? undefined,
          language: (snipDO.content_language as LanguageEnum) ?? undefined,
        },
        hero_image_url: snipDO.hero_image_url ?? undefined,
        authors: snipDO.authors ?? undefined,
        published_at: snipDO.published_at ?? undefined,
        extra: snipDO.extra ?? undefined,
        visibility: (snipDO.visibility as VisibilityEnum) ?? undefined,
      });
    } else {
      const file: UploadFileMeta = {
        storage_url: snipDO.file_storage_url!,
        name: snipDO.file_name!,
        mime_type: snipDO.file_mime_type!,
        size: snipDO.file_size!,
      };
      return new Voice({
        ...snipDO,
        from: (snipDO.from as SnipFromEnum) ?? undefined,
        status: (snipDO.status as SnipStatusEnum) ?? undefined,
        file,
        title: snipDO.title ?? '',
        play_url: snipDO.play_url ?? undefined,
        hero_image_url: snipDO.hero_image_url ?? undefined,
        authors: snipDO.authors ?? undefined,
        published_at: snipDO.published_at ?? undefined,
        extra: snipDO.extra ?? undefined,
        visibility: (snipDO.visibility as VisibilityEnum) ?? undefined,
      });
    }
  }

  static ofUnknownWebpage(
    unknownWebpage: UnknownWebpage,
    param: {
      webpage: WebpageMeta;
      play_url?: string;
      title: string;
      hero_image_url?: string;
      show_notes: ReaderHTMLContent;
      authors?: Author[];
      published_at?: Date;
      extra?: string;
      visibility?: VisibilityEnum;
    },
  ) {
    return new Voice({
      ...unknownWebpage,
      ...param,
      status: null,
      visibility: param.visibility ?? VisibilityEnum.PUBLIC,
    });
  }
}
