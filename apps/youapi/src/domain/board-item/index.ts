/**
 * Board Item Domain Service - 看板项目领域服务
 * 处理看板项目相关的业务逻辑
 *
 * Migrated from:
 * - youapp/src/lib/domain/board-item/index.ts
 */

import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { isEqual } from 'lodash';

import { rankBetween, rankNBetween } from '@/common/utils';
import { BoardItemDAO } from '@/dao/board-item';
import { BoardItemDO } from '@/dao/board-item/types';
// TODO: Import app services when available
// import { deleteBoardGroup } from "@/app/board-group";
// import { deleteSnip } from "@/app/snip";
// import { deleteThought } from "@/app/thought";

import { BoardDomainService } from '../board';
import { BoardGroupDomainService } from '../board-group';
import { SnipDomainService } from '../snip';
import { ThoughtDomainService } from '../thought';
import {
  type BoardItem,
  BoardItemTypeEnum,
  type DirectoryItem,
  type GroupDirectoryItem,
  type PutEntitiesToBoardParam,
  type PutEntityToBoardParam,
  type SnipDirectoryItem,
  type ThoughtDirectoryItem,
} from './types';

@Injectable()
export class BoardItemDomainService {
  private readonly logger = new Logger(BoardItemDomainService.name);

  constructor(
    private readonly boardItemDAO: BoardItemDAO,
    @Inject(forwardRef(() => BoardDomainService))
    private readonly boardDomain: BoardDomainService,
    @Inject(forwardRef(() => BoardGroupDomainService))
    private readonly boardGroupDomain: BoardGroupDomainService,
    private readonly snipDomain: SnipDomainService,
    @Inject(forwardRef(() => ThoughtDomainService))
    private readonly thoughtDomain: ThoughtDomainService,
  ) {}
  /**
   * 将 entity 声明到 board 上，幂等！
   */
  async putEntityToBoard(param: PutEntityToBoardParam) {
    const parent_board_group_id = param.parent_board_group_id;
    const range = await this.getRankRange(parent_board_group_id, param.rank_after);
    const temp: {
      snip_id?: string;
      thought_id?: string;
      board_group_id?: string;
      chat_id?: string;
    } = {};
    switch (param.entity_type) {
      case BoardItemTypeEnum.SNIP:
        temp.snip_id = param.entity_id;
        break;
      case BoardItemTypeEnum.THOUGHT:
        temp.thought_id = param.entity_id;
        break;
      case BoardItemTypeEnum.CHAT:
        temp.chat_id = param.entity_id;
        break;
      case BoardItemTypeEnum.BOARD_GROUP:
        temp.board_group_id = param.entity_id;
        break;
    }
    const boardItemDO = await this.boardItemDAO.insert({
      board_id: param.board_id,
      parent_board_group_id: parent_board_group_id,
      rank: rankBetween(range[0], range[1]),
      ...temp,
    });

    // 通知 board 更新头图、intro 等信息
    this.boardDomain.notifyBoardItemsChange(param.board_id);

    return this.do2entity(boardItemDO);
  }

  /**
   * 将 entities 声明到 board 上，幂等！
   */
  async putEntitiesToBoard(param: PutEntitiesToBoardParam) {
    const parent_board_group_id = param.parent_board_group_id;
    const range = await this.getRankRange(parent_board_group_id, param.rank_after);
    const ranks = rankNBetween(param.items.length, range[0], range[1]);
    const boardItemDOs = await Promise.all(
      param.items.map(async (item, index) => {
        const temp: {
          snip_id?: string;
          thought_id?: string;
          board_group_id?: string;
          chat_id?: string;
        } = {};
        switch (item.entity_type) {
          case BoardItemTypeEnum.SNIP:
            temp.snip_id = item.entity_id;
            break;
          case BoardItemTypeEnum.THOUGHT:
            temp.thought_id = item.entity_id;
            break;
          case BoardItemTypeEnum.CHAT:
            temp.chat_id = item.entity_id;
            break;
          case BoardItemTypeEnum.BOARD_GROUP:
            temp.board_group_id = item.entity_id;
            break;
        }
        return await this.boardItemDAO.insert({
          board_id: param.board_id,
          parent_board_group_id: parent_board_group_id,
          rank: ranks[index],
          ...temp,
        });
      }),
    );

    // 通知 board 更新头图、intro 等信息
    this.boardDomain.notifyBoardItemsChange(param.board_id);

    return boardItemDOs.map(this.do2entity);
  }

  /**
   * 声明将 entity 移动到指定 board（会删除其他 board 的关联），幂等
   */
  async moveEntityToBoard(param: PutEntityToBoardParam) {
    if (param.entity_type === BoardItemTypeEnum.SNIP) {
      await this.deleteBySnipId(param.entity_id);
    } else if (param.entity_type === BoardItemTypeEnum.THOUGHT) {
      await this.deleteByThoughtId(param.entity_id);
    } else if (param.entity_type === BoardItemTypeEnum.CHAT) {
      await this.deleteByChatId(param.entity_id);
    }
    await this.putEntityToBoard(param);
  }

  async delete(id: string): Promise<void> {
    const boardItemDO = await this.boardItemDAO.selectById(id);
    if (boardItemDO) {
      await this.boardItemDAO.delete({ id });
      // 通知 board 更新头图、intro 等信息
      this.boardDomain.notifyBoardItemsChange(boardItemDO.board_id);
    }
  }

  async deleteByBoardId(board_id: string) {
    await this.boardItemDAO.deleteByBoardId(board_id);
  }

  async deleteBySnipId(snip_id: string) {
    const boardItemDOs = await this.boardItemDAO.selectBySnipId(snip_id);
    await this.boardItemDAO.deleteBySnipId(snip_id);
    // 通知 board 更新头图
    boardItemDOs.forEach((item) => {
      this.boardDomain.notifyBoardItemsChange(item.board_id);
    });
  }

  async deleteByChatId(chat_id: string) {
    const boardItemDOs = await this.boardItemDAO.selectByChatId(chat_id);
    await this.boardItemDAO.deleteByChatId(chat_id);
    // 通知 board 更新头图
    boardItemDOs.forEach((item) => {
      this.boardDomain.notifyBoardItemsChange(item.board_id);
    });
  }

  async deleteByThoughtId(thought_id: string) {
    const boardItemDOs = await this.boardItemDAO.selectByThoughtId(thought_id);
    await this.boardItemDAO.deleteByThoughtId(thought_id);
    // 通知 board 更新头图
    boardItemDOs.forEach((item) => {
      this.boardDomain.notifyBoardItemsChange(item.board_id);
    });
  }

  // async deleteBySnipIdBoardIds(snip_id: string, board_ids: string[]) {
  //   await this.boardItemDAO.deleteBySnipIdBoardIds(snip_id, board_ids);
  //   // 通知 board 更新头图
  //   board_ids.forEach((board_id) => {
  //     this.boardDomain.notifyBoardItemsChange(board_id);
  //   });
  // }

  async list(board_id: string): Promise<BoardItem[]> {
    const boardItemDOs = await this.boardItemDAO.selectByBoardId({ board_id });
    return boardItemDOs.map(this.do2entity);
  }

  async listByParentGroupId(
    parent_board_group_id: string,
    include_self = false,
  ): Promise<BoardItem[]> {
    let boardItemDOs = await this.boardItemDAO.selectByParentBoardGroupId(parent_board_group_id);
    if (!include_self) {
      boardItemDOs = boardItemDOs.filter((i) => i.parent_board_group_id === parent_board_group_id);
    }

    return boardItemDOs.map(this.do2entity);
  }

  async listByCursorExcludeGroup(
    board_id: string,
    limit: number = 10,
    starting_after?: string,
  ): Promise<{
    data: BoardItem[];
    has_more: boolean;
  }> {
    const boardItemDOs = await this.boardItemDAO.selectByCursorExcludeGroup(
      board_id,
      limit + 1, // 多查一条用于判断是否还有更多
      starting_after,
    );

    const hasMore = boardItemDOs.length > limit;
    const items = boardItemDOs.slice(0, limit).map(this.do2entity);

    return {
      data: items,
      has_more: hasMore,
    };
  }

  async getById(id: string): Promise<BoardItem> {
    const boardItemDO = await this.boardItemDAO.selectById(id);
    return this.do2entity(boardItemDO);
  }

  async tryGetByEntityId(
    entity_type: BoardItemTypeEnum,
    entity_id: string,
  ): Promise<BoardItem | undefined> {
    let result: BoardItemDO[];
    switch (entity_type) {
      case BoardItemTypeEnum.SNIP:
        result = await this.boardItemDAO.selectBySnipId(entity_id);
        break;
      case BoardItemTypeEnum.THOUGHT:
        result = await this.boardItemDAO.selectByThoughtId(entity_id);
        break;
      case BoardItemTypeEnum.CHAT:
        result = await this.boardItemDAO.selectByChatId(entity_id);
        break;
      case BoardItemTypeEnum.BOARD_GROUP:
        result = await this.boardItemDAO.selectByBoardGroupId(entity_id);
        break;
      default:
        result = [];
    }
    return result?.length > 0 ? this.do2entity(result[0]) : undefined;
  }

  async rankAfter(
    id: string,
    parent_board_group_id?: string,
    rank_after_id?: string,
  ): Promise<BoardItem> {
    const range = await this.getRankRange(parent_board_group_id, rank_after_id);
    const updatedBoardItemDO = await this.boardItemDAO.update({
      id,
      // 没传 parent_board_group_id 时，要把 parent_board_group_id 更新为 null 才对
      parent_board_group_id: parent_board_group_id ?? null,
      rank: rankBetween(range[0], range[1]),
    });
    return this.do2entity(updatedBoardItemDO);
  }

  async batchRankAfter(ids: string[], parent_board_group_id?: string, rank_after?: string) {
    const range = await this.getRankRange(parent_board_group_id, rank_after);
    const ranks = rankNBetween(ids.length, range[0], range[1]);
    await Promise.all(
      ids.map(async (id, index) => {
        await this.boardItemDAO.update({
          id,
          parent_board_group_id: parent_board_group_id,
          rank: ranks[index],
        });
      }),
    );
  }

  async getRankRange(parent_board_group_id?: string, rank_after_id?: string) {
    if (rank_after_id) {
      const target = await this.boardItemDAO.selectById(rank_after_id);
      const next = await this.boardItemDAO.trySelectNextRank(target.rank, parent_board_group_id);
      return [target.rank, next?.rank];
    } else {
      // 插到第一个之前
      const first = await this.boardItemDAO.trySelectFirst(parent_board_group_id);
      return [undefined, first?.rank];
    }
  }

  async getEntityIdsByBoardIds(entityType: 'snip' | 'thought' | 'chat', boardIds: string[]) {
    const result = await this.boardItemDAO.getEntityIdsByBoardIds(entityType, boardIds);
    return result.map((item) => item.entity_id);
  }

  async getEntityIdsByBoardGroupIds(
    entityType: 'snip' | 'thought' | 'chat',
    boardGroupIds: string[],
  ) {
    const result = await this.boardItemDAO.getEntityIdsByBoardGroupIds(entityType, boardGroupIds);
    return result.map((item) => item.entity_id);
  }

  /**
   * 类型保护：判断是否为 group
   */
  private isGroup(item: DirectoryItem): item is GroupDirectoryItem {
    return (
      item.entity_type === BoardItemTypeEnum.BOARD_GROUP ||
      // 兼容大模型可能填错的类型
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      item.entity_type === 'group'
    );
  }

  /**
   * 类型保护：判断是否为 snip 或 thought
   */
  private isSnipOrThought(item: DirectoryItem): item is SnipDirectoryItem | ThoughtDirectoryItem {
    return (
      item.entity_type === BoardItemTypeEnum.SNIP || item.entity_type === BoardItemTypeEnum.THOUGHT
    );
  }

  /**
   * TODO 移动到 app 层
   * 一次性声明（重组）board 的目录结构
   */
  async organizeDirectoryStructure(
    board_id: string,
    new_directory_structure: DirectoryItem[],
  ): Promise<void> {
    const board = await this.boardDomain.getById(board_id);
    const user_id = board.creator_id;

    // 1. 顶层 group/snip/thought 统一排序
    const topRanks = rankNBetween(new_directory_structure.length, undefined, undefined);

    const groupIdSet = new Set<string>();
    const snipIdSet = new Set<string>();
    const thoughtIdSet = new Set<string>();
    for (let i = 0; i < new_directory_structure.length; i++) {
      const item = new_directory_structure[i];
      try {
        if (this.isGroup(item)) {
          // group 处理
          let board_group_id: string;

          const hasThoughts =
            item.children &&
            item.children.length > 0 &&
            item.children.some((child) => child.entity_type === BoardItemTypeEnum.THOUGHT);
          const icon = hasThoughts
            ? {
                name: 'GroupFeather',
                color: '--function-purple',
              }
            : undefined;
          if (!item.entity_id) {
            // 先创建 group
            const group = await this.boardGroupDomain.create({
              board_id,
              // 兼容大模型可能把 name 填错成 title
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-expect-error
              name: item.name || item.title,
              user_id,
              icon,
            });
            board_group_id = group.id;
            groupIdSet.add(board_group_id);
            // 再插入 board_item
            await this.boardItemDAO.insert({
              board_id,
              parent_board_group_id: undefined,
              rank: topRanks[i],
              board_group_id: group.id,
            });
          } else {
            // 已有
            board_group_id = item.entity_id;
            groupIdSet.add(board_group_id);
            const boardGroup = await this.boardGroupDomain.getById(item.entity_id);
            // 更新
            if (boardGroup.name !== item.name || !isEqual(boardGroup.icon, icon)) {
              await this.boardGroupDomain.patch({
                id: item.entity_id,
                name: item.name,
                icon,
              });
            }
            // 更新 rank
            const groupItem = await this.tryGetByEntityId(
              BoardItemTypeEnum.BOARD_GROUP,
              item.entity_id,
            );
            if (!groupItem) {
              throw new Error(`Board group item not found by group id: ${item.entity_id}`);
            }
            await this.boardItemDAO.update({
              id: groupItem.id,
              rank: topRanks[i],
            });
          }
          // 2. group 的 children 单独排序
          const children = item.children;
          const childRanks = rankNBetween(children.length, undefined, undefined);
          for (let j = 0; j < children.length; j++) {
            const child = children[j];
            try {
              switch (child.entity_type) {
                case BoardItemTypeEnum.SNIP:
                  snipIdSet.add(child.entity_id);
                  await this.boardItemDAO.updateBySnipId(child.entity_id, {
                    parent_board_group_id: board_group_id,
                    rank: childRanks[j],
                  });
                  break;
                case BoardItemTypeEnum.THOUGHT:
                  thoughtIdSet.add(child.entity_id);
                  await this.boardItemDAO.updateByThoughtId(child.entity_id, {
                    parent_board_group_id: board_group_id,
                    rank: childRanks[j],
                  });
                  break;
              }
            } catch (error) {
              console.warn(
                `Error in organizeDirectoryStructure: ${error}, child: ${JSON.stringify(child)}`,
              );
            }
          }
        } else if (this.isSnipOrThought(item)) {
          // 顶层 snip/thought
          switch (item.entity_type) {
            case BoardItemTypeEnum.SNIP:
              snipIdSet.add(item.entity_id);
              await this.boardItemDAO.updateBySnipId(item.entity_id, {
                parent_board_group_id: null,
                rank: topRanks[i],
              });
              break;
            case BoardItemTypeEnum.THOUGHT:
              thoughtIdSet.add(item.entity_id);
              await this.boardItemDAO.updateByThoughtId(item.entity_id, {
                parent_board_group_id: null,
                rank: topRanks[i],
              });
              break;
          }
        }
      } catch (error) {
        console.warn(
          `Error in organizeDirectoryStructure: ${error}, item: ${JSON.stringify(item)}`,
        );
      }
    }

    // 不在新目录中的 groups、snip、thought 统一删除
    try {
      const boardItems = await this.list(board_id);
      for (const item of boardItems) {
        if (item.board_group_id && !groupIdSet.has(item.board_group_id)) {
          await this.boardGroupDomain.delete(item.board_group_id);
        }
        if (item.snip_id && !snipIdSet.has(item.snip_id)) {
          // TODO: Add delete method to SnipDomainService
          this.logger.warn(`Snip deletion pending: ${item.snip_id}`);
        }
        if (item.thought_id && !thoughtIdSet.has(item.thought_id)) {
          await this.thoughtDomain.delete(item.thought_id);
        }
      }
    } catch (error) {
      console.warn(`Error in organizeDirectoryStructure: ${error}, board_id: ${board_id}`);
    }
  }

  private do2entity(boardItemDO: BoardItemDO): BoardItem {
    // eslint-disable-next-line unused-imports/no-unused-vars
    const { deleted_at, ...rest } = boardItemDO;
    return {
      ...rest,
      parent_board_group_id: rest.parent_board_group_id ?? undefined,
      snip_id: rest.snip_id ?? undefined,
      thought_id: rest.thought_id ?? undefined,
      board_group_id: rest.board_group_id ?? undefined,
      chat_id: rest.chat_id ?? undefined,
    };
  }
}
