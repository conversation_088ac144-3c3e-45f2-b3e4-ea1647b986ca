export interface BoardItem {
  /**
   * 唯一标识
   */
  id: string;
  /**
   * 创作板 ID
   */
  board_id: string;
  /**
   * 所属分组 ID
   */
  parent_board_group_id?: string;
  /**
   * 创建时间
   */
  created_at: Date;
  /**
   * 更新时间
   */
  updated_at: Date;
  /**
   * 排序值
   */
  rank: string;
  /**
   * 关联的 snip 的 ID
   */
  snip_id?: string;
  /**
   * 关联的 thought 的 ID
   */
  thought_id?: string;
  /**
   * 关联的 thought 的 ID
   */
  board_group_id?: string;
  /**
   * 关联的 chat 的 ID
   */
  chat_id?: string;
}

export interface PutEntityToBoardParam {
  board_id: string;
  parent_board_group_id?: string;
  /**
   * 排在某个 rank 值之后
   */
  rank_after?: string;
  entity_type: BoardItemTypeEnum;
  entity_id: string;
}

export interface PutEntitiesToBoardParam {
  board_id: string;
  parent_board_group_id?: string;
  /**
   * 排在某个 rank 值之后
   */
  rank_after?: string;
  items: {
    entity_type: BoardItemTypeEnum;
    entity_id: string;
  }[];
}

export enum BoardItemTypeEnum {
  SNIP = 'snip',
  /**
   * @deprecated
   */
  CHAT = 'chat',
  THOUGHT = 'thought',
  BOARD_GROUP = 'board_group',
}

// 用于给大模型一次性声明新目录结构的类型
export type DirectoryItem = GroupDirectoryItem | SnipDirectoryItem | ThoughtDirectoryItem;

export interface GroupDirectoryItem {
  entity_id?: string;
  entity_type: BoardItemTypeEnum.BOARD_GROUP;
  name: string;
  children: (SnipDirectoryItem | ThoughtDirectoryItem)[];
}

export interface SnipDirectoryItem {
  entity_id: string;
  entity_type: BoardItemTypeEnum.SNIP;
  title: string;
}

export interface ThoughtDirectoryItem {
  entity_id: string;
  entity_type: BoardItemTypeEnum.THOUGHT;
  title: string;
}
