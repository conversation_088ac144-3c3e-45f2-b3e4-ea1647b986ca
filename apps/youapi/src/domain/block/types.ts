import { ProcessStatusEnum } from '@repo/common';

import type { SnipFeatureEnum } from '../../common/types/snip.types';

import type { Content } from '../content/types';

export interface OneSnipParams {
  snip_id: string;
}

export interface QuerySnipBlocksParams {
  snip_id: string;
  action_id?: string;
}

export interface QuerySnipsBlocksParams {
  snip_ids: string[];
}

export interface OneBlockParams {
  block_id: string;
}

export interface OneSnipBlockParams {
  snip_id: string;
  block_id: string;
}

export interface MoveOneSnipBlockParams {
  snip_id: string;
  block_id: string;
  target_block_id: string;
}

export interface OneBlockContentParams {
  block_id: string;
  content_id: string;
}

export interface Block {
  id: string;
  updated_at: Date;
  type: SnipFeatureEnum;
  status: ProcessStatusEnum;
  snip_id: string;
  current_content_id?: string;
  contents: Content[];

  // Block 展示逻辑 - 取消
  // is_deleted: boolean;
  // display: BlockDisplayEnum;
  // action_id: string;

  // placement 逻辑 - 取消
  // rank: number;
  // height: BlockHeightEnum;

  // 关联 Note 逻辑 - 取消
  // related_snip_id?: string;
}
