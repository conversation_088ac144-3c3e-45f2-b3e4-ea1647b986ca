/**
 * Block Domain Service - 区块领域服务
 * 处理区块相关的业务逻辑
 *
 * Migrated from:
 * - youapp/src/lib/domain/block/index.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { type ContentFormatEnum, type ContentVO, ProcessStatusEnum } from '@repo/common';

import { BlockDisplayEnum, SnipFeatureEnum } from '../../common/types/snip.types';

// Temporary ActionDO type definition until action DAO is migrated
interface ActionDO {
  id: string;
  result_block_type: SnipFeatureEnum;
  result_block_format: ContentFormatEnum;
}

import type { PagingParam } from '../../common/types';
import { runConcurrently } from '../../common/utils';
import { BlockDAO } from '../../dao/block';
import { BlockDO } from '../../dao/block/types';
import { ContentDAO } from '../../dao/content';
import { InsertBlockContentParam } from '../../dao/content/types';
import { SnipDAO } from '../../dao/snip';
import { ContentDomainService } from '../content';
import type { Content } from '../content/types';
import { PartialSnipUpdatedEvent } from '../events';
import type { Block, OneBlockParams, QuerySnipBlocksParams, QuerySnipsBlocksParams } from './types';

@Injectable()
export class BlockDomainService {
  private readonly logger = new Logger(BlockDomainService.name);

  constructor(
    private readonly blockDao: BlockDAO,
    private readonly contentDao: ContentDAO,
    private readonly snipDao: SnipDAO,
    private readonly contentDomain: ContentDomainService,
    private readonly eventBus: EventBus,
  ) {}

  public toBlock(data: BlockDO | null): Block | null {
    if (!data) return null;

    let current_content_id: string;
    if (data.current_content_id) {
      current_content_id = data.current_content_id;
    } else if (data.contents?.length) {
      current_content_id = data.contents[0].id;
    }

    const block = {
      id: data.id,
      updated_at: data.updated_at,
      type: data.type as SnipFeatureEnum,
      snip_id: data.snip_id || '',
      current_content_id, // 存量数据里可能没有 current_content_id，这里给前端做个兼容
      contents: data.contents?.map((c) => this.contentDomain.toContent(c)) as Content[],
    };
    return this.fillStatus(block);
  }

  public fillStatus(data: Omit<Block, 'status'>): Block {
    if (!data.contents) {
      data.contents = [];
    }

    const block = { ...data, status: ProcessStatusEnum.DONE } as Block;
    block.status = data.contents.reduce<ProcessStatusEnum>((acc, c) => {
      if (c.status === ProcessStatusEnum.ING || acc === ProcessStatusEnum.ING) {
        return ProcessStatusEnum.ING;
      }
      if (c.status === ProcessStatusEnum.FAIL) {
        return ProcessStatusEnum.FAIL;
      }
      return acc;
    }, ProcessStatusEnum.DONE) as ProcessStatusEnum;

    return block;
  }

  async getById(param: OneBlockParams) {
    const block = await this.blockDao.selectOneById(param.block_id);
    return this.toBlock(block);
  }

  async getByContentId(param: { content_id: string }) {
    const block = await this.blockDao.selectOneByContentId(param.content_id);
    return this.toBlock(block);
  }

  async getBlocksByAction(param: QuerySnipBlocksParams) {
    const { action_id, snip_id } = param;
    const blocks = await this.blockDao.selectManyBySnip([snip_id], {
      action_id,
      include_deleted: false,
    });
    return blocks.map((b) => this.toBlock(b)) as Block[];
  }

  async getBlocksForSnipDetail(param: QuerySnipBlocksParams) {
    const { snip_id } = param;
    const blocks = await this.blockDao.selectManyBySnip([snip_id], {
      display: [BlockDisplayEnum.SHOW],
      status: [ProcessStatusEnum.ING, ProcessStatusEnum.DONE],
      include_deleted: false,
    });
    return blocks.map((b) => this.toBlock(b)) as Block[];
  }

  async getBlocksForPlugin(param: QuerySnipsBlocksParams) {
    const { snip_ids } = param;
    const blocks = await this.blockDao.selectManyBySnip(snip_ids, {
      type: [SnipFeatureEnum.OVERVIEW],
      status: [ProcessStatusEnum.DONE],
      display: [BlockDisplayEnum.SHOW],
      include_deleted: false,
    });
    return blocks.map((b) => this.toBlock(b)) as Block[];
  }

  async querySummaryContentBySnip(param: { snip_id: string; status?: ProcessStatusEnum[] }) {
    const { snip_id, status } = param;
    const blockDOs = await this.blockDao.selectManyBySnip([snip_id], {
      type: [SnipFeatureEnum.RAW_SUMMARY],
      status: status || [ProcessStatusEnum.DONE],
    });
    if (!blockDOs?.length) return [];
    const blocks = blockDOs.map((b) => this.toBlock(b)) as Block[];
    return blocks.flatMap((b) => b.contents);
  }

  async querySummaryContentByURL(param: { url: string; status?: ProcessStatusEnum[] }) {
    const { url, status } = param;
    const blockDOs = await this.blockDao.selectManyByURL(url, {
      type: [SnipFeatureEnum.RAW_SUMMARY],
      status: status || [ProcessStatusEnum.DONE],
    });
    if (!blockDOs?.length) return [];
    const blocks = blockDOs.map((b) => this.toBlock(b)) as Block[];
    return blocks.flatMap((b) => b.contents);
  }

  async queryOverviewContentBySnip(param: { snip_id: string; status?: ProcessStatusEnum[] }) {
    const { snip_id, status } = param;
    const blockDOs = await this.blockDao.selectManyBySnip([snip_id], {
      type: [SnipFeatureEnum.OVERVIEW],
      status: status || [ProcessStatusEnum.DONE],
      display: [BlockDisplayEnum.SHOW],
    });
    if (!blockDOs?.length) return [];
    const blocks = blockDOs.map((b) => this.toBlock(b)) as Block[];
    return blocks.flatMap((b) => b.contents);
  }

  async queryTranscriptContentBySnip(param: {
    snip_id: string;
    status?: ProcessStatusEnum[];
  }): Promise<Content[]> {
    const { snip_id, status } = param;
    const blockDOs = await this.blockDao.selectManyBySnip([snip_id], {
      type: [SnipFeatureEnum.TRANSCRIPT],
      status: status || [ProcessStatusEnum.DONE],
      display: [BlockDisplayEnum.SHOW],
    });
    if (!blockDOs?.length) return [];
    const blocks = blockDOs.map((b) => this.toBlock(b)) as Block[];
    return blocks.flatMap((b) => b.contents);
  }

  async getBlockContent(param: { content_id: string }) {
    return this.contentDomain.getById(param.content_id);
  }

  private async syncSnipChanges(
    snip_id: string | null,
    update_options: {
      timestamp: boolean;
      search: boolean;
    } = {
      timestamp: true,
      search: false,
    },
  ) {
    if (!snip_id) return;

    const { timestamp, search } = update_options;

    // Update snip timestamp
    const syncUpdatedAt = async () => {
      if (!timestamp) return;
      await this.snipDao.update(snip_id, { updated_at: new Date() });
    };

    // Update snip extension to search domain using EventBus
    const syncSearch = async () => {
      if (!search) return;
      const snip = await this.snipDao.selectById(snip_id);
      if (!snip) return;

      const blocks = await this.blockDao.selectManyBySnip([snip_id], {
        type: [SnipFeatureEnum.OVERVIEW, SnipFeatureEnum.TRANSCRIPT, SnipFeatureEnum.RAW_SUMMARY],
        status: [ProcessStatusEnum.DONE],
      });

      const featureMap = blocks.reduce<Record<string, Content[]>>((accu, curr) => {
        if (!curr || !curr.contents?.length || typeof curr.type !== 'string') {
          return accu;
        }
        if (!accu[curr.type]) {
          accu[curr.type] = [];
        }
        curr.contents.forEach((c) => {
          const content = this.contentDomain.toContent(c) as Content;
          accu[curr.type!].push(content);
        });
        return accu;
      }, {});

      if (Object.keys(featureMap).length === 0) return;

      // Use EventBus to publish event instead of runInBackground
      this.eventBus.publish(
        new PartialSnipUpdatedEvent({
          id: snip_id,
          space_id: snip.space_id,
          creator_id: snip.creator_id,
          overview: featureMap.overview || [],
          transcript: featureMap.transcript || [],
          raw_summary: featureMap.raw_summary || [],
        }),
      );
    };

    // Use Promise.all with proper error handling instead of runInBackground
    try {
      await Promise.all([syncUpdatedAt(), syncSearch()]);
    } catch (error) {
      this.logger.error('Failed to sync snip changes', error);
    }
  }

  /**
   * 当插件创建 Snip 时，保存对应的 block
   */
  async insertBySnipContents(param: {
    snip_id: string;
    contents: ContentVO[];
    block_type: SnipFeatureEnum;
    display?: BlockDisplayEnum;
    action_id?: string;
  }) {
    if (!param.contents?.length) {
      this.logger.warn('No contents to insert');
      return;
    }

    const { snip_id, action_id = null, block_type, contents, display } = param;

    const block = await this.blockDao.insertOne({
      snip_id,
      action_id,
      type: block_type,
      display: display || BlockDisplayEnum.SHOW,
    });

    const insertedContents = await runConcurrently(
      contents.map(
        (content_vo) => () =>
          this.contentDao.insertOne({
            format: content_vo.format,
            raw: content_vo.raw,
            plain: content_vo.plain,
            status: content_vo.status || ProcessStatusEnum.DONE,
            language: content_vo.language,
            block_id: block.id,
            snip_id,
          } as any),
      ),
    );

    await this.setCurrentContent({
      id: block.id,
      content_id: insertedContents[0].id,
    });

    const newBlock = this.toBlock({
      ...block,
      contents: insertedContents,
    }) as Block;

    await this.syncSnipChanges(snip_id, { timestamp: true, search: true });
    return newBlock;
  }

  /**
   * 通过用户点击 action 创建 block
   */
  async insertBySnipAction(param: { snip_id: string; action: ActionDO; content?: ContentVO }) {
    const { snip_id, action, content: content_vo } = param;

    const block = await this.blockDao.insertOne({
      snip_id,
      action_id: action.id,
      type: action.result_block_type,
      display: BlockDisplayEnum.SHOW,
    });

    const content = await this.contentDao.insertOne(
      content_vo
        ? ({
            format: content_vo.format,
            raw: content_vo.raw,
            plain: content_vo.plain,
            status: ProcessStatusEnum.DONE,
            language: content_vo.language,
            block_id: block.id,
            snip_id,
          } as any)
        : ({
            format: action.result_block_format as ContentFormatEnum,
            status: ProcessStatusEnum.ING,
            block_id: block.id,
            snip_id,
          } as any),
    );

    await this.setCurrentContent({ id: block.id, content_id: content.id });

    const newBlock = this.toBlock({
      ...block,
      contents: [content],
    }) as Block;

    await this.syncSnipChanges(snip_id);
    return newBlock;
  }

  async insertRawSummary(param: { snip_id?: string; origin_url?: string; content: ContentVO }) {
    const { snip_id, origin_url, content: content_vo } = param;

    const block = await this.blockDao.insertOne({
      type: SnipFeatureEnum.RAW_SUMMARY,
      display: BlockDisplayEnum.HIDE,
      ...(snip_id ? { snip_id } : { origin_url }),
    });

    const content = await this.contentDao.insertOne({
      format: content_vo.format,
      raw: content_vo.raw,
      plain: content_vo.plain,
      status: ProcessStatusEnum.DONE,
      language: content_vo.language,
      block_id: block.id,
      ...(snip_id ? { snip_id } : {}),
    } as any);

    await this.setCurrentContent({ id: block.id, content_id: content.id });

    const newBlock = this.toBlock({
      ...block,
      contents: [content],
    }) as Block;

    if (snip_id) {
      await this.syncSnipChanges(snip_id, { timestamp: false, search: true });
    }

    return newBlock;
  }

  /**
   * 在现有的 Block 里新增 content，用于多语言或失败重试
   */
  async insertBlockContent(param: InsertBlockContentParam, setCurrent = true) {
    const content = await this.contentDao.insertOne(param);

    if (setCurrent) {
      await this.setCurrentContent({
        id: param.block_id,
        content_id: content.id,
      });
    }

    const block = await this.blockDao.selectOneById(param.block_id);
    await this.syncSnipChanges(block?.snip_id as string);

    return this.contentDomain.toContent(content) as Content;
  }

  /**
   * 更新单个 Block 中的 ContentVO
   */
  async updateContentVO(param: { content_id: string; content_vo: ContentVO }) {
    const { content_id, content_vo } = param;

    await this.contentDomain.updateContent(content_id, content_vo);
    const block = await this.blockDao.selectOneByContentId(content_id);

    if (block) {
      await this.syncSnipChanges(block.snip_id);
      const newBlock = this.toBlock(block) as Block;
      return newBlock;
    }
  }

  /**
   * 清空单条 VO 内容
   */
  async clearContent(param: { content_id: string }) {
    const { content_id } = param;
    await this.contentDao.updateOne(content_id, { raw: '', plain: '' });
  }

  /**
   * 更新单个 Content 的 trace_id，以便用户在前端针对 content 反馈效果
   */
  async updateContentTraceId(param: { content_id: string; trace_id: string }) {
    await this.contentDomain.setContentTraceId(param.content_id, param.trace_id);
  }

  async setCurrentContent(param: { id: string; content_id: string }) {
    await this.blockDao.updateOne(param.id, {
      current_content_id: param.content_id,
    });
  }

  async setAsInProgress({ content_id }: { content_id: string }) {
    await this.contentDomain.setStatusToInProgress(content_id);
  }

  async setAsDone({ content_id }: { content_id: string }) {
    await this.contentDomain.setStatusToDone(content_id);
    const block = await this.blockDao.selectOneByContentId(content_id);

    if (block) {
      await this.syncSnipChanges(block.snip_id, {
        timestamp: true,
        search: true,
      });
    }
  }

  async setAsFail({ content_id }: { content_id: string }) {
    await this.contentDomain.setStatusToFail(content_id);
    const block = await this.blockDao.selectOneByContentId(content_id);

    if (block) {
      await this.syncSnipChanges(block.snip_id);
    }
  }

  async deleteBySnip(param: { snip_id: string }) {
    const blocks = await this.blockDao.selectManyBySnip([param.snip_id]);
    await runConcurrently(blocks.map((b) => () => this.deleteBlock({ block_id: b!.id })));
  }

  async deleteBlock(param: OneBlockParams) {
    const result = await this.blockDao.deleteOneById(param.block_id);
    return result;
  }

  async listBlocksForSearchSyncOnly(
    timeStart: Date,
    timeEnd: Date,
    paging: Required<PagingParam>,
    userId?: string,
  ) {
    const blocks = await this.blockDao.superDangerousSelectAllByUpdatedAtRange(
      timeStart,
      timeEnd,
      paging,
      userId,
    );
    return blocks.map((b) => this.toBlock(b));
  }
}
