/**
 * New Board Workflow Service - 新建板块工作流服务
 * 处理创建新板块的AI助手工作流程
 *
 * Migrated from:
 * - youapp/src/domain/chat/agent/new_board.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { ChatPromptClient } from 'langfuse-core';
import { ChatCompletionContentPartText } from 'openai/resources/index.mjs';
import { AIAskPromptService } from '@/infra/youllm/prompt';
import type { CompletionStreamChunk } from '../../../common/types';
import {
  type ChatBoardOrigin,
  ChatOriginTypeEnum,
  type ChatSnipOrigin,
  type ChatThoughtOrigin,
  type ChatWebpageOrigin,
  CompletionBlockTypeEnum,
  type LLMs,
  MessageRoleEnum,
  TOOL_TYPES,
} from '../../../common/types';
import { jsonToXml } from '../../../common/utils';
import { CompletionBlockDAO, MessageDAO } from '../../../dao/chat';
import {
  PromptName,
  type YLChatCompletionContentPart,
  type YLChatCompletionOptions,
} from '../../../infra/youllm';
import { ChatLLMRunner } from '../../../infra/youllm/llm_service/runner';
import { BoardDomainService } from '../../board';
import { YouLLMService } from '../../llm';
import { BoardContextBuilder } from '../../llm/context/board';
import { UserDomainService } from '../../user';
import { ToolCallChatStreamHandler } from '../ask_ai/handler';
import { ChatQueryDomainService } from '../query';
import { ToolCallService } from '../tool_call';
import type { ToolDefinition } from '../tool_call/types';
import type { AssistantMessage, ChatDetail, CompletionBlock, Message, UserMessage } from '../types';

@Injectable()
export class NewBoardWorkflowService {
  private readonly logger = new Logger(NewBoardWorkflowService.name);

  constructor(
    private readonly youllm: YouLLMService,
    private readonly boardDomain: BoardDomainService,
    private readonly boardContextBuilder: BoardContextBuilder,
    private readonly chatQueryDomain: ChatQueryDomainService,
    private readonly userDomain: UserDomainService,
    private readonly toolCallService: ToolCallService,
    private readonly messageDAO: MessageDAO,
    private readonly completionBlockDAO: CompletionBlockDAO,
  ) {}

  /**
   * Get tool definitions with proper dependency injection
   * 获取工具定义，使用依赖注入模式
   */
  protected getToolDefinitions(): ToolDefinition[] {
    return [
      this.toolCallService.getToolCall(TOOL_TYPES.LIBRARY_SEARCH)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.GOOGLE_SEARCH)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.CREATE_SNIP_BY_URL)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.EDIT_THOUGHT)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.ORGANIZE_DIRECTORY_STRUCTURE)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.UPDATE_PLAN)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.IMAGE_GENERATE)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.DIAGRAM_GENERATE)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.AUDIO_GENERATE)?.tool_definition,
    ].filter(Boolean) as ToolDefinition[];
  }

  /**
   * Get filtered tools based on user's board count
   * 根据用户的板块数量获取过滤后的工具
   */
  protected async getFilteredTools(userId: string): Promise<ToolDefinition[]> {
    const boardCount = await this.boardDomain.countAllByCreatorId(userId);
    const allTools = this.getToolDefinitions();

    // If user has no boards (except Unsorted/Chaos), don't include library_search
    if (boardCount <= 1) {
      return allTools.filter((tool) => tool.function.name !== 'library_search');
    }

    return allTools;
  }

  protected getTraceArgs(chat: ChatDetail) {
    return {
      sessionId: chat.id as string,
      metadata: {
        chatId: chat.id as string,
        ...(chat.origin?.type === ChatOriginTypeEnum.BOARD && {
          boardId: (chat.origin as ChatBoardOrigin)?.id || '',
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.SNIP && {
          snipId: (chat.origin as ChatSnipOrigin)?.id || '',
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.THOUGHT && {
          thoughtId: (chat.origin as ChatThoughtOrigin)?.id || '',
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.WEBPAGE && {
          url: (chat.origin as ChatWebpageOrigin)?.url || '',
        }),
      },
    };
  }

  async *generate(args: {
    chat: ChatDetail;
    runner: ChatLLMRunner;
    userId?: string; // Add userId parameter for proper user context
  }): AsyncGenerator<CompletionStreamChunk<any>, void, unknown> {
    let chat = args.chat;
    const { runner, userId } = args;

    // Get user context from domain service
    const user = userId ? await this.userDomain.selectOneById(userId) : null;

    if (!user) {
      this.logger.warn('No user context available, using temporary user');
    }

    const userContext = user || { id: 'temp-user-id', name: 'temp-user-name' };

    const assistant_message_index = (chat.messages as Message[]).findLastIndex(
      (m: Message) => m.role === MessageRoleEnum.ASSISTANT,
    );
    if (assistant_message_index === -1) {
      throw new Error('No assistant message found in chat');
    }

    // Get filtered tools based on user's board count
    const finalTools = await this.getFilteredTools(userContext.id);
    const handler = new ToolCallChatStreamHandler(
      chat,
      runner,
      this.messageDAO,
      this.completionBlockDAO,
      this.chatQueryDomain,
      this.toolCallService,
      20,
      finalTools,
    );

    const prompt = (await runner.runSpan({
      name: 'fetch-board-create-prompt',
      fn: async (span) =>
        runner.prompt_manager.fetchPrompt(
          {
            name: PromptName.BoardCreatePrompt,
            prefer: 'local',
          },
          span,
          true,
        ),
    })) as ChatPromptClient;

    yield* handler.handleStream(async (blocks: CompletionBlock[], will_exceed_limit: boolean) => {
      // update chat board_id so tools can access it
      const tool_blocks = blocks.filter((b) => b.type === CompletionBlockTypeEnum.TOOL);
      if (!chat.board_id && tool_blocks.length > 0) {
        chat = (await this.chatQueryDomain.getDetailById(chat.id as string)) as ChatDetail;
        handler.setChat(chat);
      }

      type BoardContext = Awaited<ReturnType<typeof this.boardContextBuilder.getChatContext>>;
      let board_context: BoardContext = {} as BoardContext;
      if (chat.board_id) {
        board_context = await this.boardContextBuilder.getChatContext(
          chat.board_id as string,
          chat,
        );
      }

      // update blocks so tools can access
      if (blocks.length) {
        ((chat.messages as Message[])[assistant_message_index] as AssistantMessage).blocks = blocks;
        handler.setChat(chat);
      }

      // Get tools with proper filtering logic
      let tools = [...finalTools];
      if (tool_blocks.length === 0) {
        // If no tool blocks, only include create_board tool
        const createBoardTool = this.toolCallService.getToolCall(TOOL_TYPES.CREATE_BOARD);
        tools = createBoardTool ? [createBoardTool.tool_definition] : [];
      }

      const options: YLChatCompletionOptions = {
        tools,
        tool_choice: will_exceed_limit ? 'none' : 'auto',
        model: ((chat.messages as Message[])[assistant_message_index] as AssistantMessage)
          .model as LLMs,
      };

      const prompt_stop =
        tool_blocks.length > 15 ||
        board_context.sources?.length > Math.floor(Math.random() * 5) + 10;
      const context = jsonToXml({
        board_context: chat.board_id ? board_context : undefined,
        instruction:
          blocks.length === 0
            ? 'You will now help user to build objective for this board and call create_board tool to create a board no matter what the input is.'
            : will_exceed_limit
              ? 'You have now collected enough materials on this topic. Conclude with an outro summarizing what has been done so far.'
              : prompt_stop
                ? 'You have now collected enough materials on this topic, optionally write a research style report and save it using edit_thought tool. Conclude with an outro summarizing what has been done so far.'
                : undefined,
      });

      const messages = await (runner.prompt_manager as AIAskPromptService).buildFromChatMessages({
        prompt: prompt,
        model: runner.model_name,
        messages: context
          ? [
              ...(chat.messages as Message[]),
              {
                role: MessageRoleEnum.USER,
                content: context,
              } as UserMessage,
            ]
          : (chat.messages as Message[]),
        variables: {
          currentDatetime: new Date().toISOString(),
        },
        systemMessageVariables: {
          userName: userContext.name || 'User',
        },
      });
      const last_user_message = messages.findLast(
        (m) =>
          m.role === 'user' &&
          m.content.length &&
          (m.content[0] as YLChatCompletionContentPart)?.type === 'text',
      );
      if (last_user_message) {
        last_user_message.content = [
          {
            type: 'text',
            text: `Here is USER's query, Claude will use the language of user's query as the working language. Use the language specified by user in messages as the working language when explicitly provided. All thinking and responses must be in the working language. "board"/"snip"/"thought" are proper nouns in this conversation, and they should not be translated regardless of the working language.

<user_query>
${(last_user_message.content[0] as ChatCompletionContentPartText).text}
</user_query>
              `,
          },
        ] as YLChatCompletionContentPart[];
      }

      return this.youllm.streamChatCompletionWithMessages(prompt, {
        runner,
        messages,
        traceArgs: this.getTraceArgs(chat),
        modelOptions: options,
      });
    });

    // Use NestJS event system for cleanup instead of direct calls
    try {
      await runner.cleanup();
    } catch (err) {
      this.logger.error('Failed to cleanup runner', err);
    }
  }
}
