import type { SearchResultTypeEnum } from '@/common/types';

import { ContextBuildingResult } from '../context';

/**
 * Convert ContextBuildingResult to SearchResult array
 * Merges chunks for each unique id+type combination
 * @param result Context building result
 * @returns Array of search results
 */
export function convertToSearchResults(result: ContextBuildingResult) {
  const chunks = result.atReferencesRetrieved.flatMap((item) => item.chunks);
  // Group by id+type combination
  const groupedResults = chunks.reduce(
    (acc, ref) => {
      const key = `${ref.id}-${ref.type}`;
      if (!acc[key]) {
        acc[key] = {
          entity_id: ref.id,
          entity_type: ref.type as SearchResultTypeEnum,
          chunks: [],
          url: `https://youmind.ai/${ref.type.replace(/[^s]$/, '$&s')}/${ref.id}`,
        };
      }

      if (acc[key].entity_title === undefined) {
        acc[key].entity_title = ref.title;
      }

      acc[key].chunks.push(ref.chunk);
      return acc;
    },
    {} as Record<
      string,
      {
        entity_id: string;
        entity_type: SearchResultTypeEnum;
        chunks: string[];
        entity_title?: string;
        url?: string;
      }
    >,
  );

  return Object.values(groupedResults);
}

export function convertToContextString(result: ContextBuildingResult) {
  if (result.atReferencesRetrieved.length === 0) {
    return '';
  }

  const entities = result.atReferencesRetrieved
    .map((atRef) => {
      const entityType = atRef.atReferenceType; // "board" or "board_group"

      // Group chunks by their type (snip, thought, etc.) and then by id
      const chunksByType = atRef.chunks.reduce(
        (acc, chunk) => {
          if (!acc[chunk.type]) {
            acc[chunk.type] = {};
          }
          if (!acc[chunk.type][chunk.id]) {
            acc[chunk.type][chunk.id] = {
              chunks: [],
              title: chunk.title,
            };
          }
          acc[chunk.type][chunk.id].chunks.push(chunk.chunk);
          return acc;
        },
        {} as Record<string, Record<string, { chunks: string[]; title?: string }>>,
      );

      // Build the nested structure
      let content = `  <${entityType}_id>${atRef.atReferenceId}</${entityType}_id>\n`;

      Object.entries(chunksByType).forEach(([type, entitiesOfType]) => {
        const pluralType = type + 's'; // snip -> snips, thought -> thoughts
        content += `  <${pluralType}>\n`;

        Object.entries(entitiesOfType).forEach(([id, data]) => {
          content += `    <${type}>\n`;
          content += `      <${type}_id>${id}</${type}_id>\n`;
          if (data.title && data.title.trim()) {
            content += `      <title>${data.title}</title>\n`;
          }
          content += `      <chunks>${data.chunks.join('\n')}</chunks>\n`;
          content += `    </${type}>\n`;
        });

        content += `  </${pluralType}>\n`;
      });

      return `<${entityType}>\n${content}</${entityType}>`;
    })
    .join('\n\n');

  return entities;
}
