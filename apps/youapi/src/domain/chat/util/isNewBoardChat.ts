import { Chat<PERSON><PERSON><PERSON>num, CompletionBlockType<PERSON>num, MessageRoleEnum, TOOL_TYPES } from '@/common/types';

import type { ChatDetail } from '../types';

export function isNewBoardChat(chat: ChatDetail) {
  return chat.mode === ChatModeEnum.NEW_BOARD;
}

export function isCustomAssistantChat(chat: ChatDetail) {
  return [ChatModeEnum.ASSISTANT_PREVIEW, ChatModeEnum.CUSTOM_ASSISTANT].includes(chat.mode);
}

export function isInsideNewBoardWorkflow(chat: ChatDetail) {
  const last_assistant_message = chat.messages?.findLast(
    (m) => m.role === MessageRoleEnum.ASSISTANT,
  );
  const blocks = last_assistant_message?.blocks || [];
  return blocks.some(
    (b) => b.type === CompletionBlockTypeEnum.TOOL && b.tool_name === TOOL_TYPES.CREATE_BOARD,
  );
}

export function getCurrentBoardIdByChat(chat: ChatDetail) {
  const last_user_message = chat.messages?.findLast((m) => m.role === MessageRoleEnum.USER);
  return last_user_message?.board_id || chat.board_id || '';
}
