import {
  ChatCompletionAssistantMessageParam,
  ChatCompletionToolMessageParam,
} from 'openai/resources/index.mjs';
import {
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  LLMs,
  MODEL_DEFINITION,
} from '@/common/types';
import {
  YLChatCompletionContentPart,
  YLChatRequestAssistantMessage,
  YLChatRequestToolMessage,
} from '@/infra/youllm';

import type { CompletionBlock } from '../types';

// Convert CompletionBlock[] to LLM (AssistantMessageParam | ToolMessageParam)[]
// Continuous reasoning and content blocks are merged into one assistant message
// Tool blocks have individual tool messages
// Each tool message should precede with an assistant message that contains tool call (id/name/arguments), this assistant message's content array should also be non-empty

// here is an example:
// [
//   {
//     role: "assistant",
//     content: [{ type: "thinking", thinking: "..." }, { type: "text", text: "..." }],
//     tool_calls: [{ id: "...", type: "function", function: { name: "...", arguments: "..." } }],
//   },
//   {
//     role: "tool",
//     content: [{ type: "text", text: "..." }],
//     tool_call_id: "...",
//   },
// ]

export function blocksToLLMMessages(
  blocks: CompletionBlock[],
  model: LLMs,
  is_last_message: boolean = false,
): Array<ChatCompletionToolMessageParam | ChatCompletionAssistantMessageParam> {
  const messages: (YLChatRequestAssistantMessage | YLChatRequestToolMessage)[] = [];

  let reasoning_content: string = '';
  const model_definition = MODEL_DEFINITION.get(model);
  for (let i = 0; i < blocks.length; i++) {
    const block = blocks[i];

    if (block.type === CompletionBlockTypeEnum.REASONING && block.data) {
      reasoning_content = block.data;
      if (!(model_definition?.from === 'anthropic' && is_last_message)) continue;

      const currentAssistantMessage: YLChatRequestAssistantMessage = {
        role: 'assistant',
        content: [] as YLChatCompletionContentPart[],
      };
      messages.push(currentAssistantMessage);

      if (block.data) {
        (currentAssistantMessage.content as YLChatCompletionContentPart[]).push({
          type: 'thinking',
          thinking: block.data,
          ...(block.extra.signature ? { signature: block.extra.signature || '' } : {}),
        });
      }
      if (block.extra.redacted_thinking) {
        (currentAssistantMessage.content as YLChatCompletionContentPart[]).push({
          type: 'redacted_thinking',
          data: block.extra.redacted_thinking,
        });
      }

      continue;
    }

    if (block.type === CompletionBlockTypeEnum.CONTENT && block.data) {
      let currentAssistantMessage = messages[messages.length - 1] as YLChatRequestAssistantMessage;
      if (currentAssistantMessage?.role !== 'assistant') {
        currentAssistantMessage = {
          role: 'assistant',
          content: [] as YLChatCompletionContentPart[],
        };
        messages.push(currentAssistantMessage);
      }
      let content = block.data;
      if (is_last_message && reasoning_content && model === LLMs.DEEPSEEK_REASONER) {
        content = `<thinking>${reasoning_content}</thinking>\n\n${content}`;
      }
      (currentAssistantMessage.content as YLChatCompletionContentPart[]).push({
        type: 'text',
        text: content,
      });

      continue;
    }

    if (
      block.type === CompletionBlockTypeEnum.TOOL &&
      block.status !== CompletionBlockStatusEnum.ING
    ) {
      let currentAssistantMessage = messages[messages.length - 1] as YLChatRequestAssistantMessage;
      if (currentAssistantMessage?.role !== 'assistant') {
        currentAssistantMessage = {
          role: 'assistant',
          content: [
            {
              type: 'text',
              text: 'Invoking tool call', // Gemini 在 content 为空时会报错
            },
          ] as YLChatCompletionContentPart[],
          tool_calls: [],
        };
        messages.push(currentAssistantMessage);
      } else if (!currentAssistantMessage.tool_calls) {
        currentAssistantMessage.tool_calls = [];
      }

      currentAssistantMessage.tool_calls!.push({
        id: block.tool_id,
        type: 'function',
        function: {
          name: block.tool_name,
          arguments: JSON.stringify(block.tool_arguments),
        },
      });
      messages.push({
        role: 'tool',
        content:
          block.status === CompletionBlockStatusEnum.ERROR
            ? `ERROR: ${block.extra?.error?.message}`
            : block.tool_response
              ? block.tool_response
              : JSON.stringify(block.tool_result),
        tool_call_id: block.tool_id,
      });
    }
  }

  return messages as Array<ChatCompletionToolMessageParam | ChatCompletionAssistantMessageParam>;
}
