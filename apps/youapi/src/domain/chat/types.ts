import { z } from 'zod';
import {
  type AtReference,
  AtReferenceSchema,
  BoardPanelViewSchema as BoardPanelSchema,
  ChatModeEnum,
  type Chat<PERSON>rigin,
  ChatOriginSchema,
  type ChatOriginTypeEnum,
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  type EditCommand,
  EditCommandSchema,
  type LLMs,
  MessageEventSchema,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
  type PagingParam,
  PagingResultSchema,
  type UseToolParam,
  type UseToolSchema,
} from '../../common/types';

export const ChatSchema = z.object({
  id: z.string(),
  creator_id: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  title: z.string(),
  origin: ChatOriginSchema,
  board_id: z.string().optional(),
  mode: z.nativeEnum(ChatModeEnum),
  show_new_board_suggestion: z.boolean(),
  new_board_chat_id: z.string().optional(),
});
export const PlainChatSchema = ChatSchema.extend({
  created_at: z.string(),
  updated_at: z.string(),
});

export const UserMessageSchema = z.object({
  id: z.string(),
  chat_id: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  role: z.literal(MessageRoleEnum.USER),
  status: z.nativeEnum(MessageStatusEnum),
  content: z.string(),
  origin: ChatOriginSchema,
  selection: z.string().optional(),
  at_references: z.array(AtReferenceSchema).optional(),
  board_id: z.string().optional(),
  tools: z
    .record(
      z.string(),
      z.object({
        use_tool: z.enum(['auto', 'required', 'none']).optional(),
      }),
    )
    .optional(),
  command: EditCommandSchema.optional(),
  mode: z.nativeEnum(MessageModeEnum).optional().default(MessageModeEnum.ASK),
  shortcut: z
    .object({
      id: z.string().uuid(),
      name: z.string(),
    })
    .optional(),
  // @deprecated
  board_panels: z.array(BoardPanelSchema).optional(),
});

export const BaseCompletionBlockSchema = z.object({
  id: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  type: z.nativeEnum(CompletionBlockTypeEnum),
  status: z.nativeEnum(CompletionBlockStatusEnum),
  message_id: z.string(),
  extra: z.record(z.string(), z.any()).default({}),
});
export const CompletionContentBlockSchema = BaseCompletionBlockSchema.extend({
  type: z.literal(CompletionBlockTypeEnum.CONTENT),
  data: z.string(),
});
export const CompletionReasoningBlockSchema = BaseCompletionBlockSchema.extend({
  type: z.literal(CompletionBlockTypeEnum.REASONING),
  data: z.string(),
});
export const CompletionToolBlockSchema = BaseCompletionBlockSchema.extend({
  type: z.literal(CompletionBlockTypeEnum.TOOL),
  tool_id: z.string(),
  tool_name: z.string(),
  tool_arguments: z.record(z.string(), z.any()).default({}),
  tool_result: z.record(z.string(), z.any()).default({}),
  tool_response: z.string(),
  tool_generate_elapsed_ms: z.number().optional(),
  tool_execute_elapsed_ms: z.number().optional(),
});
export type ToolCompletionBlock = z.infer<typeof CompletionToolBlockSchema>;
export const CompletionBlockSchema = z.discriminatedUnion('type', [
  CompletionContentBlockSchema,
  CompletionReasoningBlockSchema,
  CompletionToolBlockSchema,
]);
export const PlainCompletionBlockSchema = z.discriminatedUnion('type', [
  CompletionContentBlockSchema.extend({
    created_at: z.string(),
    updated_at: z.string(),
  }),
  CompletionReasoningBlockSchema.extend({
    created_at: z.string(),
    updated_at: z.string(),
  }),
  CompletionToolBlockSchema.extend({
    created_at: z.string(),
    updated_at: z.string(),
  }),
]);

export const AssistantMessageSchema = z.object({
  id: z.string(),
  chat_id: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  role: z.literal(MessageRoleEnum.ASSISTANT),
  status: z.nativeEnum(MessageStatusEnum),
  model: z.string(),
  trace_id: z.string(),
  blocks: z.array(CompletionBlockSchema),
  error: z.record(z.string(), z.any()).optional(),
  // @deprecated, use blocks instead
  content: z.string(),
  // @deprecated, use blocks instead
  reasoning: z.string(),
  // @deprecated, use blocks instead
  events: z.array(MessageEventSchema).optional(),
  // @deprecated, use blocks instead
  reasoning_begin_at: z.string().optional(),
  // @deprecated, use blocks instead
  reasoning_end_at: z.string().optional(),
});

export const MessageSchema = z.discriminatedUnion('role', [
  UserMessageSchema,
  AssistantMessageSchema,
]);

export const PlainMessageSchema = z.discriminatedUnion('role', [
  UserMessageSchema.extend({
    created_at: z.string(),
    updated_at: z.string(),
  }),
  AssistantMessageSchema.extend({
    created_at: z.string(),
    updated_at: z.string(),
  }),
]);

export const ChatDetailSchema = ChatSchema.extend({
  messages: z.array(MessageSchema),
});

export type Chat = z.infer<typeof ChatSchema>;
export type PlainChat = z.infer<typeof PlainChatSchema>;
export type CompletionContentBlock = z.infer<typeof CompletionContentBlockSchema>;
export type CompletionReasoningBlock = z.infer<typeof CompletionReasoningBlockSchema>;
export type CompletionToolBlock<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  T extends Record<string, any> = Record<string, any>,
> = z.infer<typeof CompletionToolBlockSchema> & {
  tool_result: T;
  generate_elapsed_ms?: number;
  execute_elapsed_ms?: number;
};
export type CompletionBlock = z.infer<typeof CompletionBlockSchema>;
export type PlainCompletionBlock = z.infer<typeof PlainCompletionBlockSchema>;
export type UserMessage = z.infer<typeof UserMessageSchema>;
export type AssistantMessage = z.infer<typeof AssistantMessageSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type PlainMessage = z.infer<typeof PlainMessageSchema>;
export type ChatDetail = z.infer<typeof ChatDetailSchema>;

export const ChatDetailPagingResultSchema = z.object({
  paging: PagingResultSchema,
  data: z.array(ChatDetailSchema),
});
export interface ListByUserParam {
  query: PagingParam;
  user_id: string;
}

export interface QueryByOriginParam {
  origin: ChatOrigin;
  user_id: string;
}

export interface CreateChatParam {
  user_id: string;
  origin: ChatOrigin;
  mode: ChatModeEnum;
  title?: string;
  board_id?: string;
  custom_assistant_id?: string;
  show_new_board_suggestion?: boolean;
}

export interface ChatMessage {
  message: string;
  selection?: string;
}

export interface CreateChatWithMessageParam {
  user_id: string;
  origin: ChatOrigin;
  message: string;
  selection?: string;
}

export interface SaveMessagesParam {
  user_id: string;
  user_message: string;
  user_message_at?: AtReference[];
  user_message_origin?: ChatOrigin;
  user_message_selection?: string;
  user_message_command?: EditCommand;
  user_message_tools?: z.infer<typeof UseToolSchema>;
  user_message_mode?: MessageModeEnum;
  user_message_shortcut?: {
    id: string;
    name: string;
  };
  assistant_message: string;
  assistant_model?: LLMs;
  // 以下三选一
  origin?: ChatOrigin;
  chat_id?: string;
  board_id?: string;
  mode?: ChatModeEnum;
  custom_assistant_id?: string;
}

export interface SendMessageParam {
  chat: Chat;
  user_id: string;
  message: string;
  origin: ChatOrigin;
  selection?: string;
  command?: EditCommand;
  at_references?: AtReference[];
  board_id?: string;
  tools?: UseToolParam;
  mode: MessageModeEnum;
  shortcut?: {
    id: string;
    name: string;
  };
}

interface ChatBaseContext {
  type: ChatOriginTypeEnum;
  url: string;
  title: string;
  content: string; // full content
  description: string; // short content, less than 1000 words
  describeSelf: string;
}
interface ChatReferenceId {
  relatedId: string;
  relatedPropName: string;
}
export type ChatWebpageContext = ChatBaseContext & {
  type: ChatOriginTypeEnum.WEBPAGE;
};
export type ChatUnknownContext = ChatBaseContext &
  Partial<ChatReferenceId> & {
    type: ChatOriginTypeEnum.UNKNOWN;
  };
export type ChatSnipContext = ChatBaseContext &
  ChatReferenceId & {
    type: ChatOriginTypeEnum.SNIP;
    fileUrl?: string;
  };
export type ChatThoughtContext = ChatBaseContext &
  ChatReferenceId & {
    type: ChatOriginTypeEnum.THOUGHT;
  };
export type ChatBoardContext = ChatBaseContext &
  ChatReferenceId & {
    type: ChatOriginTypeEnum.BOARD;
    context: string;
    fileUrl?: string;
  };

export type ChatContext =
  | ChatSnipContext
  | ChatBoardContext
  | ChatThoughtContext
  | ChatUnknownContext
  | ChatWebpageContext;

export enum SourceTypeEnum {
  SNIP = 'snip',
  CHAT = 'chat',
  BOARD = 'board',
  BOARD_GROUP = 'board_group',
  THOUGHT = 'thought',
}
export const SourceSchema = z.object({
  entity_type: z.nativeEnum(SourceTypeEnum),
  entity_id: z.string().uuid(),
});
export type Source = z.infer<typeof SourceSchema>;
