/**
 * Google Search Tool Service - 谷歌搜索工具服务
 * 在互联网上搜索内容，支持网页、视频、学术论文和本地事件
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/google_search.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import z from 'zod';
import { InvalidArguments } from '../../../common/errors';
import {
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  InternetSearchResultSchema,
  TOOL_TYPES,
} from '../../../common/types';
import { francToIso6391 } from '../../../common/utils';
import { YouLLMService } from '../../llm';
import { WebSearchDomainService } from '../../web-search';
import { StreamChunkUnion } from '../ask_ai/handler';
import { BaseToolService } from './base.service';
import type { ToolCallResult, ToolDefinition, ToolFunctionParameters } from './types';

interface LanguageDetectionResult {
  text: string;
  declaredLang: string;
  francDetected?: string;
  aiDetected?: string;
  finalLang: string;
  corrected: boolean;
  method: 'declared' | 'ai';
}

const GoogleSearchToolResultSchema = z.object({
  // query: z.string(),
  results: z.array(InternetSearchResultSchema),
  temp: z.object({
    status: z.enum([
      'start-scraping',
      'finish-scraping',
      'adding-video-search',
      'finish-adding-video-search',
    ]),
    scraping_num: z.number(),
  }),
  scraped_data: z.object({
    num: z.number(),
  }),
  multilingual_queries: z.array(
    z.object({
      q: z.string(),
      lang: z.string(),
    }),
  ),
});

@Injectable()
export class GoogleSearchService extends BaseToolService {
  private readonly logger = new Logger(GoogleSearchService.name);

  readonly toolName: TOOL_TYPES = TOOL_TYPES.GOOGLE_SEARCH;
  readonly toolOutputSchema = GoogleSearchToolResultSchema;

  readonly toolDefinition: ToolDefinition = {
    max_calls: 8,
    type: 'function',
    function: {
      name: this.toolName,
      description: `Find content on the Internet. This tool can search webpages, videos, scholarly papers, and local events. Queries should be concise (under 30 words) and must not be similar across multiple calls. To construct effective queries:
- Use diverse and specific keywords while sticking to the main topic.
- Use multilingual_queries when it significantly improves search results or when the original query mentions places, people, events, or entities whose primary language context differs from the query language (e.g., adding an English query for "Trump China Policies").
- For queries with timeframes, include absolute dates (e.g., "climate change impact on agriculture in 2025") instead of relative terms like "tomorrow" or "latest".
- Include related queries that may affect answers, such as checking weather for event planning.
- Advanced search operators are supported for non-webpage searches (video, scholar, events), e.g., exact match ("climate change effects"), negation (-politics), or site-specific searches (site:*.edu).`,
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: "Search query, under 30 words, in the same language as the user's input.",
          },
          type: {
            type: 'string',
            enum: ['webpage', 'video', 'events', 'scholar'],
            description: 'Type of content to search: webpage (default), video, scholar, or event.',
          },
          language: {
            type: 'string',
            description:
              "ISO 639-1 code of the query language (e.g., 'zh', 'en'). For Traditional Chinese queries, use 'zh-Hant'.",
          },
          multilingual_queries: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                q: {
                  type: 'string',
                  description: 'Search query in a different language',
                },
                lang: {
                  type: 'string',
                  description:
                    "ISO 639-1 code of the query language (e.g., 'zh', 'en'). For Traditional Chinese queries, use 'zh-Hant'.",
                },
              },
              required: ['q', 'lang'],
            },
            description:
              'Optional array of queries in languages other than the user\'s input language; use when it improves search results or when the original query mentions places, people, events, or entities whose primary language context differs from the query language (e.g., adding an English query for "特朗普最新对华政策")',
          },
          location: {
            type: 'string',
            description:
              "Required for local events. Must be in English (e.g., 'New York', 'Tokyo').",
          },
          event_filter: {
            type: 'string',
            description:
              "Time and type filter for events. Supports date filters: 'date:today' (Today's Events), 'date:tomorrow' (Tomorrow's Events), 'date:week' (This Week's Events), 'date:weekend' (This Weekend's Events), 'date:next_week' (Next Week's Events), 'date:month' (This Month's Events), 'date:next_month' (Next Month's Events). Event type filter: 'event_type:Virtual-Event' (Online Events). You can mix different filters by separating them with a comma, e.g., 'event_type:Virtual-Event,date:today' for Today's Online Events.",
          },
          freshness: {
            type: 'string',
            description:
              "Filters search results by when they were discovered. Supports 'pd' (within 24 hours), 'pw' (within 7 days), 'pm' (within 31 days), 'py' (within 365 days), or date range format 'YYYY-MM-DDtoYYYY-MM-DD' (e.g., '2022-04-01to2022-07-30').",
          },
        },
        required: ['query', 'type', 'language', 'multilingual_queries'],
        additionalProperties: false,
      },
    },
  };

  constructor(
    private readonly youllm: YouLLMService,
    private readonly webSearchDomain: WebSearchDomainService,
  ) {
    super();
  }

  /**
   * 处理多语言查询
   */
  private extractMultilingualQueries(
    queries?: Array<{ q: string; lang: string }> | unknown,
  ): { q: string; lang: string }[] {
    if (!queries || !Array.isArray(queries)) return [];

    try {
      return queries
        .map((item) => ({
          q: item?.q || '',
          lang: item?.lang || 'en',
        }))
        .filter((q) => !!q.q);
    } catch (error) {
      this.logger.error('extractMultilingualQueries error', error);
      return [];
    }
  }

  /**
   * 批量验证和纠正语言代码
   */
  private async validateAndCorrectLanguages(
    textLangPairs: Array<{ text: string; declaredLang: string }>,
    span: LangfuseSpanClient,
  ): Promise<LanguageDetectionResult[]> {
    try {
      // 使用 francToIso6391 批量检测
      const francResults = textLangPairs.map(({ text, declaredLang }) => {
        const francDetected = francToIso6391(text);
        return {
          text,
          declaredLang,
          francDetected,
          needsAiCheck: francDetected !== declaredLang && declaredLang !== 'zh-Hant',
        };
      });

      // 收集需要 AI 检测的文本
      const textsNeedingAiCheck = francResults
        .filter((item) => item.needsAiCheck)
        .map((item) => item.text);

      let aiResults: string[] = [];
      if (textsNeedingAiCheck.length > 0) {
        span.event({
          name: 'ai-language-detection-call',
          input: {
            texts_needing_check: textsNeedingAiCheck.length,
            texts: textsNeedingAiCheck,
          },
        });

        const now = Date.now();
        const result = await this.youllm.detectTextsLanguage(textsNeedingAiCheck, {
          // promptPrefer: "local",
        });
        aiResults = result.results;

        this.logger.log('ai-language-detection-result', {
          ai_detected_languages: aiResults,
          ai_raw_result: result.aiRawResult,
        });

        if (aiResults.some((r) => r === 'unknown')) {
          this.logger.warn('ai detect language unknown', {
            texts: textsNeedingAiCheck,
            ai_detected_languages: aiResults,
            ai_raw_result: result.aiRawResult,
          });

          span.event({
            name: 'ai-language-detection-unknown',
          });
        }

        span.event({
          name: 'ai-language-detection-result',
          output: {
            ai_detected_languages: aiResults,
            time_cost: Date.now() - now,
          },
        });
      }

      // 创建从文本到AI检测结果的映射
      const aiResultsMap = new Map<string, string>();
      textsNeedingAiCheck.forEach((text, index) => {
        aiResultsMap.set(text, aiResults[index]);
      });

      // 将检测结果映射回原始数组
      const detectionResults: LanguageDetectionResult[] = francResults.map((item) => {
        const result: LanguageDetectionResult = {
          text: item.text,
          declaredLang: item.declaredLang,
          francDetected: item.francDetected,
          finalLang:
            item.declaredLang === 'zh-Hant'
              ? item.declaredLang
              : item.francDetected || item.declaredLang,
          corrected: false,
          method: 'declared',
        };

        if (!item.needsAiCheck) {
          result.method = 'declared';
          return result;
        }

        const aiDetected = aiResultsMap.get(item.text);
        result.aiDetected = aiDetected;

        if (aiDetected && aiDetected !== 'unknown') {
          result.finalLang = aiDetected;
          result.corrected = true;
          result.method = 'ai';
          this.logger.log(
            `Language corrected: declared=${item.declaredLang}, franc=${item.francDetected}, ai=${aiDetected}, using=${aiDetected}`,
          );
        } else {
          result.method = 'declared';
          this.logger.log(
            `Language detection uncertain: declared=${item.declaredLang}, franc=${item.francDetected}, ai=${aiDetected}, using=${item.declaredLang}`,
          );
        }

        return result;
      });

      span.event({
        name: 'language-detection-summary',
        output: {
          results: detectionResults,
        },
      });

      return detectionResults;
    } catch (error) {
      span.event({
        name: 'language-detection-error',
        input: {
          error: error?.toString(),
          texts_count: textLangPairs.length,
        },
      });

      this.logger.error('Language validation error:', error);
      return textLangPairs.map((item) => ({
        text: item.text,
        declaredLang: item.declaredLang,
        finalLang: item.declaredLang,
        corrected: false,
        method: 'declared' as const,
      }));
    }
  }

  async *execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
  ): AsyncGenerator<any, ToolCallResult> {
    const { user_id, chat, parsed_params } = parameters;
    const { query, location, event_filter, language, multilingual_queries, freshness } =
      parsed_params;

    let type = (parsed_params.type || 'webpage') as 'webpage' | 'video' | 'events' | 'scholar';

    const typeEnum = ['webpage', 'video', 'events', 'scholar'];
    if (!typeEnum.includes(type)) {
      type = 'webpage';
    }

    // 处理多语言查询
    const parsedMultilingualQueries = this.extractMultilingualQueries(multilingual_queries);

    // 收集所有需要验证的文本和语言对
    const textLangPairs = [
      { text: query, declaredLang: language },
      ...parsedMultilingualQueries.map((item) => ({
        text: item.q,
        declaredLang: item.lang,
      })),
    ];

    // 一次性验证和纠正所有语言代码
    const detectionResults = await this.validateAndCorrectLanguages(textLangPairs, span);

    // 分离出主查询和多语言查询的纠正结果
    const correctedLanguage = detectionResults[0].finalLang;
    const correctedMultilingualQueries = parsedMultilingualQueries.map((item, index) => ({
      q: item.q,
      lang: detectionResults[index + 1].finalLang,
    }));

    this.logger.log('corrected language:', correctedLanguage);
    this.logger.log('corrected multilingual queries:', correctedMultilingualQueries);

    if (!query) {
      this.logger.error('query is not provided', chat.id);
      throw new InvalidArguments('missing query');
    }

    yield {
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: parameters.completion_block.id,
      data: {
        multilingual_queries: correctedMultilingualQueries,
      },
      path: 'tool_result',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>;

    const results = await this.webSearchDomain.search(
      {
        query,
        type,
        language: correctedLanguage,
        multilingual_queries: correctedMultilingualQueries,
        location,
        event_filter,
        freshness,
        chat,
      },
      span,
    );

    if (!results.length) {
      span.event({
        name: 'no-internet-result-found',
        input: {
          query,
          user_id,
        },
      });
      return {
        response: 'No Internet search result found, please try again with a different query',
        result: { results: [] },
      };
    }

    // 初始化空数组
    yield {
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: parameters.completion_block.id,
      data: [],
      path: 'tool_result.results',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>;

    // 然后逐条添加结果
    for (const [index, result] of results.entries()) {
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: parameters.completion_block.id,
        data: result,
        path: `tool_result.results[${index}]`,
      } as CompletionStreamReplaceChunk<StreamChunkUnion>;
    }

    return {
      result: {
        results,
        multilingual_queries: correctedMultilingualQueries,
      },
    };
  }
}
