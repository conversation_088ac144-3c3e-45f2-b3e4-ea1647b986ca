/**
 * Image Generate Service - 图像生成服务
 * 提供图像生成和编辑功能，支持多种尺寸、风格和背景配置
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/image_generate.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { encode } from 'blurhash';
import { LangfuseSpanClient } from 'langfuse-core';
import { ImagesResponse } from 'openai/resources/images';
import { toFile } from 'openai/uploads';
import sharp from 'sharp';
import { z } from 'zod';
import { runInBackground } from '@/common/errors';
import type { CompletionStreamReplaceChunk } from '@/common/types';
// TODO: These dependencies need to be migrated from @/lib
import { MessageImageGenerateResultSchema } from '@/common/types';
// import { createImage } from "@/lib/app/snip/create_snip";
// import { SnipImageVO } from "@/lib/app/snip/types";
import { imageUrlToBytesAndFormat } from '@/common/utils';
import {
  CompletionStreamModeEnum,
  LLMProviders,
  LLMs,
  MessageAtReferenceTypeEnum,
  MessageRoleEnum,
  TOOL_TYPES,
} from '../../../common/types';
import { YLImageEditOptions, YLImageGenerationOptions } from '../../../infra/youllm';
import { LLMRunner } from '../../../infra/youllm/llm_service/runner';
import { FileDomainService } from '../../file';
import { Directory } from '../../file/types';
import { YouLLMService } from '../../llm';
import { SnipDomainService } from '../../snip';
import { SnipTypeEnum } from '../../snip/types';
import { SpaceDomainService } from '../../space';
import { QuotaResourceEnum } from '../../subscription/types';
import { UsageRecordDomainService } from '../../usage-record';
import { StreamChunkUnion } from '../ask_ai/handler';
import type { UserMessage } from '../types';
import { getCurrentBoardIdByChat, isInsideNewBoardWorkflow } from '../util/isNewBoardChat';
import { BaseToolService } from './base.service';
import type { ToolDefinition, ToolFunctionParameters } from './types';

const ImageGenerateToolSchema = z.object({
  style: z.string().optional(),
  quality: z.string().optional(),
  size: z.string().optional(),
});

interface SnipImageVO {
  id: string;
  board_ids?: string[];
  [key: string]: any;
}

interface ErrorWithName {
  name?: string;
  message?: string;
}

const size_map = {
  auto: 'auto',
  square: '1024x1024',
  portrait: '1024x1536',
  landscape: '1536x1024',
};

const tool_definition: ToolDefinition = {
  max_calls: 3,
  execution_timeout: 120000,
  type: 'function',
  function: {
    name: TOOL_TYPES.IMAGE_GENERATE,
    description:
      'use this tool to generate or edit an image given one or more source images in PNG format. Create an image snip if explicitly requested by the user.',
    parameters: {
      type: 'object',
      properties: {
        prompt: {
          type: 'string',
          description: 'The prompt to generate a image for',
        },
        title: {
          type: 'string',
          description: 'The title of the image.',
        },
        source_image_urls: {
          type: 'array',
          description: 'One or more source images',
          items: {
            type: 'string',
            description: 'The source image url',
          },
        },
        size: {
          type: 'string',
          enum: ['auto', 'square', 'portrait', 'landscape'],
          description: 'The size of the generated images. Default to auto.',
        },
        background: {
          type: 'string',
          enum: ['auto', 'transparent', 'opaque'],
          description:
            'Allows to set transparency for the background of the generated image. Default to auto.',
        },
        board_id: {
          type: 'string',
          description:
            'Optional. Board ID to add the Snips to. If provided, all Snips will be added to this board.',
        },
        parent_board_group_id: {
          type: 'string',
          description: 'Optional. Board group ID',
        },
      },
      required: ['prompt', 'title'],
      additionalProperties: false,
    },
  },
};

@Injectable()
export class ImageGenerateService extends BaseToolService {
  readonly toolName = TOOL_TYPES.IMAGE_GENERATE;
  readonly toolDefinition: ToolDefinition = tool_definition;
  readonly toolOutputSchema = MessageImageGenerateResultSchema;

  private readonly logger = new Logger(ImageGenerateService.name);

  constructor(
    private readonly fileDomain: FileDomainService,
    private readonly usageRecordDomain: UsageRecordDomainService,
    private readonly spaceDomain: SpaceDomainService,
    private readonly snipDomain: SnipDomainService,
    private readonly youllm: YouLLMService,
    private readonly eventBus: EventBus,
  ) {
    super();
  }

  async *execute(parameters: ToolFunctionParameters, span: LangfuseSpanClient) {
    const { parsed_params, user_id, chat } = parameters;
    const {
      prompt,
      title = 'Image',
      size = 'landscape',
      background = 'auto',
      parent_board_group_id: common_parent_board_group_id,
    } = parsed_params;
    const common_board_id = getCurrentBoardIdByChat(chat);

    if (!prompt) {
      return {
        response: 'Invalid prompt',
      };
    }

    const createSnip = isInsideNewBoardWorkflow(chat);

    const space = await this.spaceDomain.getByUserId(user_id);
    await this.usageRecordDomain.checkQuota(space, QuotaResourceEnum.IMAGE_GENERATION);
    const lastUserMessage = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.USER,
    ) as UserMessage;

    const style = (lastUserMessage.tools?.image_generate as z.infer<typeof ImageGenerateToolSchema>)
      ?.style;
    const quality = (
      lastUserMessage.tools?.image_generate as z.infer<typeof ImageGenerateToolSchema>
    )?.quality;

    const runner = new LLMRunner(span);
    const provider = runner.getProvider(LLMProviders.OPENAI, LLMs.GPT_IMAGE_1);
    const params: Partial<YLImageGenerationOptions> = {
      size: size_map[
        ((lastUserMessage.tools?.image_generate as z.infer<typeof ImageGenerateToolSchema>)?.size ||
          size) as keyof typeof size_map
      ] as YLImageGenerationOptions['size'],
      background: background as YLImageGenerationOptions['background'],
      n: 1,
      // Image outputs cost approximately $0.01 (low), $0.04 (medium), and $0.17 (high) for square images.
      quality: quality as YLImageGenerationOptions['quality'],
      style: style as YLImageGenerationOptions['style'],
    };

    let source_image_urls = parsed_params.source_image_urls as unknown as string[];
    const references = (lastUserMessage?.at_references || []).filter(
      ({ entity_type }) => entity_type === MessageAtReferenceTypeEnum.SNIP,
    );
    if (references.length) {
      const reference_image_urls = (
        await Promise.all(
          references.map(async ({ entity_id }) => {
            const snip = await this.snipDomain.getById(entity_id);
            if (snip.type === SnipTypeEnum.IMAGE) {
              const url = (await snip.getChatImageUrl())[0];
              return url;
            }
          }),
        )
      ).filter((url) => !!url) as string[];

      if (reference_image_urls.length > 0) {
        source_image_urls = reference_image_urls;
      }
    }

    if (source_image_urls?.length > 0) {
      source_image_urls = await Promise.all(
        source_image_urls.map(async (url) => {
          if (url.includes('/snips/')) {
            const snip_id = url.split('/snips/')[1].split('/')[0];
            if (snip_id) {
              const snip = await this.snipDomain.getById(snip_id);
              if (snip.type === SnipTypeEnum.IMAGE) {
                url = (await snip.getChatImageUrl())[0];
              }
            }
          } else if (url.includes('cdn.gooo.ai')) {
            // remove png, jpg, svg extension
            url = `${url.replace(/@.*$/, '').replace(/\.(png|jpg|jpeg|webp|svg)$/, '')}@chat`;
          }
          return url;
        }),
      );
    }

    let imageResponse: ImagesResponse;
    if (!source_image_urls || source_image_urls.length === 0) {
      const generation = runner.trace.generation({
        name: 'image-generate',
        input: params,
        model: LLMs.GPT_IMAGE_1,
      });
      imageResponse = await provider.generateImage(prompt, params);
      generation.end({
        output: {
          usage: imageResponse?.usage,
          created: imageResponse?.created,
        },
        usage: {
          input: imageResponse?.usage?.input_tokens || 0,
          output: imageResponse?.usage?.output_tokens || 0,
          total: imageResponse?.usage?.total_tokens || 0,
        },
      });
    } else {
      const generation = runner.trace.generation({
        name: 'image-edit',
        input: { ...params, image_urls: source_image_urls },
        model: LLMs.GPT_IMAGE_1,
      });
      const images = await Promise.all(
        source_image_urls.map(async (url) => {
          const { bytes, format } = await imageUrlToBytesAndFormat(url);
          // @see https://cookbook.openai.com/examples/generate_images_with_gpt_image
          return await toFile(bytes, null, {
            type: format,
          });
        }),
      );

      imageResponse = await provider.editImage(prompt, images, params);
      generation.end({
        output: {
          usage: imageResponse?.usage,
          created: imageResponse?.created,
        },
        usage: {
          input: imageResponse?.usage?.input_tokens || 0,
          output: imageResponse?.usage?.output_tokens || 0,
          total: imageResponse?.usage?.total_tokens || 0,
        },
      });
    }

    // @see https://platform.openai.com/docs/guides/image-generation?image-generation-model=gpt-image-1&lang=javascript#cost-and-latency
    // High	4160 tokens	6240 tokens	6208 tokens
    // Use EventBus instead of runInBackground
    this.eventBus.publish(
      // TODO: Create proper event when UsageRecordDomainService is migrated
      {
        type: 'CREATE_IMAGE_GENERATION_USAGE_RECORD',
        space_id: space.id,
        user_id,
        amount: imageResponse.usage?.total_tokens || 0,
      },
    );

    const { hash, image_url, blurhash, width, height } =
      await this.imageResponseToResult(imageResponse);

    const result: z.infer<typeof MessageImageGenerateResultSchema> = {
      image_urls: [image_url],
      blurhash,
      width,
      height,
      quality: quality as YLImageGenerationOptions['quality'],
      history: [
        {
          image_url,
          prompt,
        },
      ],
    };

    if (createSnip) {
      const childSpan = span.span({
        name: 'create-image-snip',
        input: {
          image_url,
          user_id,
          board_id: common_board_id,
          parent_board_group_id: common_parent_board_group_id,
        },
      });

      try {
        yield {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: parameters.completion_block.id,
          data: {
            status: 'processing',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>;

        // TODO: Replace with proper snipDomain.createImage when available
        const snipResult = (await this.createImage({
          file: {
            name: title,
            hash,
            is_public: true,
          },
          title,
          board_id: common_board_id,
          parent_board_group_id: common_parent_board_group_id,
        })) as SnipImageVO;

        if (snipResult) {
          childSpan.event({
            name: 'create-image-snip-success',
            input: { url: image_url, user_id, snip_id: snipResult?.id },
            output: snipResult,
          });

          const successResult = {
            status: 'success',
            board_id:
              common_board_id ||
              (snipResult?.board_ids && snipResult.board_ids.length > 0
                ? snipResult.board_ids[0]
                : null),
            vo: snipResult,
          };

          result.snip = successResult;
          yield {
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: parameters.completion_block.id,
            data: successResult,
            path: 'tool_result.snip',
          } as CompletionStreamReplaceChunk<StreamChunkUnion>;

          childSpan.end();
        }
      } catch (error) {
        childSpan.event({
          name: 'create-image-snip-failed',
          input: {
            image_url,
            user_id,
            error: error instanceof Error ? (error as ErrorWithName)?.name : String(error),
          },
        });
        childSpan.end({
          level: 'ERROR',
          statusMessage: (error as Error).message,
        });
        result.snip = {
          status: 'failed',
        };

        yield {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: parameters.completion_block.id,
          data: {
            status: 'failed',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>;
      }
    }

    return {
      response: `Image finished generating, it can be accessed at ${image_url}. Use this URL only as input for other tool calls. This URL will be presented using customized UI component, don't repeat this URL in your response. Doing so will be penalized.`,
      result,
    };
  }

  // TODO: Implement createImage when migrated from @/lib/app/snip/create_snip
  private async createImage(params: {
    file: { name: string; hash: string; is_public: boolean };
    title: string;
    board_id: string;
    parent_board_group_id?: string;
  }): Promise<SnipImageVO> {
    // Temporary implementation
    this.logger.warn('createImage not fully implemented - using temporary stub');
    return {
      id: 'temp-snip-id',
      board_ids: [params.board_id],
      title: params.title,
    };
  }

  private async imageResponseToResult(imageResponse: ImagesResponse, blurhashEnabled = true) {
    const image_bytes = Buffer.from(imageResponse.data?.[0].b64_json || '', 'base64');
    const hash = await this.fileDomain.uploadStringOrBuffer(image_bytes, {
      visibility: 'public',
      contentType: 'image/png',
      directory: Directory.GEN_IMAGES,
    });
    const image_url = `https://cdn.gooo.ai/${Directory.GEN_IMAGES}/${hash}.png`;

    if (!blurhashEnabled) {
      return {
        hash,
        image_url,
      };
    }

    const image = sharp(image_bytes);
    const metadata = await image.metadata();

    if (!metadata.width || !metadata.height) {
      throw new Error('Invalid image: Could not determine dimensions');
    }

    const { data, info } = await image.ensureAlpha().raw().toBuffer({ resolveWithObject: true });

    const blurhash = encode(new Uint8ClampedArray(data), info.width, info.height, 4, 4);

    return {
      hash,
      image_url,
      blurhash,
      width: metadata.width,
      height: metadata.height,
    };
  }

  async editImage(
    user_id: string,
    url: string,
    prompt: string,
    size: string,
    mask: string,
    quality: 'low' | 'medium' | 'high' | 'auto',
  ) {
    interface EditImageMask {
      x: number;
      y: number;
      width: number;
      height: number;
    }

    const masks = JSON.parse(mask) as EditImageMask[];
    const space = await this.spaceDomain.getByUserId(user_id);

    // 获取原图像的尺寸信息
    const { bytes: imageBytes } = await imageUrlToBytesAndFormat(url);
    const originalImage = sharp(imageBytes);
    const { width: originalWidth, height: originalHeight } = await originalImage.metadata();

    if (!originalWidth || !originalHeight) {
      throw new Error('Could not determine original image dimensions');
    }

    // 获取原图像的 RGBA 原始数据
    const { data: originalImageData } = await originalImage
      .ensureAlpha()
      .raw()
      .toBuffer({ resolveWithObject: true });

    // 创建一个与原图像相同尺寸的 mask，初始时完全复制原图像
    // mask 图像中，完全透明的区域（alpha = 0）表示要编辑的区域
    // Mask is an additional image whose fully transparent areas (e.g. where alpha is zero) indicate where image should be edited.
    const maskBuffer = Buffer.from(originalImageData);

    // 根据 masks 数组将指定区域设为透明（要编辑的区域）
    masks.forEach(({ x, y, width, height }) => {
      for (let row = y; row < Math.min(y + height, originalHeight); row++) {
        for (let col = x; col < Math.min(x + width, originalWidth); col++) {
          const index = (row * originalWidth + col) * 4;
          if (index + 3 < maskBuffer.length) {
            maskBuffer[index + 3] = 0; // 设为完全透明
          }
        }
      }
    });

    const maskImageBuffer = await sharp(maskBuffer, {
      raw: {
        width: originalWidth,
        height: originalHeight,
        channels: 4,
      },
    })
      .png()
      .toBuffer();

    // fs.writeFileSync("mask.png", maskImageBuffer);

    // 将 mask 转换为 Uploadable 格式
    const maskUploadable = await toFile(maskImageBuffer, null, {
      type: 'image/png',
    });

    const runner = new LLMRunner();
    const provider = runner.getProvider(LLMProviders.OPENAI, LLMs.GPT_IMAGE_1);
    const params: Partial<YLImageEditOptions> = {
      size: size as YLImageEditOptions['size'],
      n: 1,
      quality: quality || 'medium',
      mask: maskUploadable,
    };

    const generation = runner.trace.generation({
      name: 'image-edit',
      input: { ...params, image_urls: [url] },
      model: LLMs.GPT_IMAGE_1,
    });

    const { bytes, format } = await imageUrlToBytesAndFormat(url);
    const image = await toFile(bytes, null, {
      type: format,
    });

    const imageResponse = await provider.editImage(prompt, image, params);
    generation.end({
      output: {
        usage: imageResponse?.usage,
        created: imageResponse?.created,
      },
      usage: {
        input: imageResponse?.usage?.input_tokens || 0,
        output: imageResponse?.usage?.output_tokens || 0,
        total: imageResponse?.usage?.total_tokens || 0,
      },
    });

    runInBackground(
      this.usageRecordDomain.createImageGenerationUsageRecord({
        space_id: space.id,
        user_id,
        amount: imageResponse.usage?.total_tokens || 0,
      }),
    );

    const { hash, image_url, blurhash, width, height } =
      await this.imageResponseToResult(imageResponse);

    return {
      hash,
      image_url,
      original_image_url: url,
      blurhash,
      width,
      height,
      quality,
    };
  }
}
