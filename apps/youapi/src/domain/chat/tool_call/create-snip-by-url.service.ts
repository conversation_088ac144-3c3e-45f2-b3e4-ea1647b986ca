/**
 * Create Snip By URL Tool Service - 通过URL创建片段工具服务
 * 从URL创建不同类型的片段（网页、PDF、图片等）
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/create_snip_by_url.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { z } from 'zod';
import { InvalidArguments } from '../../../common/errors';
import {
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  TOOL_TYPES,
} from '../../../common/types';
import { SpanEventLevel } from '../../../infra/youllm';
import { SnipDomainService } from '../../snip';
import { SnipTypeEnum } from '../../snip/types';
import { SpaceDomainService } from '../../space';
import { ThoughtDomainService } from '../../thought';
import { StreamChunkUnion } from '../ask_ai/handler';
import { getCurrentBoardIdByChat } from '../util/isNewBoardChat';
import { BaseToolService } from './base.service';
import type { ToolCallResult, ToolDefinition, ToolFunctionParameters } from './types';

// Temporary schema definition
const MessageCreateSingleUrlResultSchema = z.object({
  url: z.string(),
  success: z.boolean(),
  error: z.string().nullable(),
  board_id: z.string().nullable(),
  snip: z.any().nullable(),
  type: z.string().optional(),
  provided_title: z.string().optional(),
  provided_album_url: z.string().optional(),
});

interface SnipUnknownWebpageVO {
  id: string;
  board_ids?: string[];
  [key: string]: any;
}

interface ErrorWithName {
  name?: string;
  message?: string;
}

@Injectable()
export class CreateSnipByUrlService extends BaseToolService {
  private readonly logger = new Logger(CreateSnipByUrlService.name);

  readonly toolName: TOOL_TYPES = TOOL_TYPES.CREATE_SNIP_BY_URL;
  readonly toolOutputSchema = z.object({
    snipsResults: z.array(MessageCreateSingleUrlResultSchema),
    summary_message: z.string(),
  });

  readonly toolDefinition: ToolDefinition = {
    max_calls: 8,
    type: 'function',
    function: {
      name: this.toolName,
      description: `Save webpage to current board, accept one or more URLs of webpages, PDFs, and images. Tool response includes the result of this action. Should be called only when the user explicitly asks to create or save materials.`,
      parameters: {
        type: 'object',
        properties: {
          urls: {
            type: 'array',
            items: {
              type: 'string',
            },
            description: 'A list of resource URLs to create Snips from',
          },
          titles: {
            type: 'array',
            items: {
              type: 'string',
            },
            description: 'A list of resource titles to create Snips from',
          },
          album_urls: {
            type: 'array',
            items: {
              type: 'string',
            },
            description:
              'A list of album cover URLs for audio/video Snips. This should be used with audio URLs generated from audio_generate tool - use the album_url from audio generation result. Each album URL should correspond to the URL at the same index in the urls array.',
          },
        },
        required: ['urls'],
        additionalProperties: false,
      },
    },
  };

  constructor(
    private readonly snipDomain: SnipDomainService,
    private readonly spaceDomain: SpaceDomainService,
    private readonly thoughtDomain: ThoughtDomainService,
  ) {
    super();
  }

  async *execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
  ): AsyncGenerator<any, ToolCallResult> {
    const { user_id, chat, parsed_params } = parameters;
    this.logger.log('create-snip-by-url>>>>', chat.board_id);
    const { urls, titles: unSafeTitles, album_urls: unSafeAlbumUrls } = parsed_params;
    const common_board_id = getCurrentBoardIdByChat(chat);

    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      this.logger.error('urls are not provided or empty', chat.id);
      throw new InvalidArguments('missing or empty urls array');
    }

    let titles: string[] = unSafeTitles as unknown as string[];

    // Validate titles parameter
    if (titles && Array.isArray(titles) && titles.length !== urls.length) {
      this.logger.error('titles array length does not match urls array length', chat.id);
      titles = [];
    }

    let album_urls: string[] = unSafeAlbumUrls as unknown as string[];

    // Validate album_urls parameter
    if (album_urls && Array.isArray(album_urls) && album_urls.length !== urls.length) {
      this.logger.error('album_urls array length does not match urls array length', chat.id);
      album_urls = [];
    }

    if (!common_board_id) {
      throw new InvalidArguments('missing board_id');
    }

    // Initialize empty array to store results
    yield {
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: parameters.completion_block.id,
      data: [],
      path: 'tool_result.snipsResults',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>;

    const results: z.infer<typeof MessageCreateSingleUrlResultSchema>[] = [];
    let successful_creations = 0;
    let failed_creations = 0;

    for (const [index, url] of urls.entries()) {
      const title = titles && Array.isArray(titles) ? titles[index] : undefined;
      const album_url = album_urls && Array.isArray(album_urls) ? album_urls[index] : undefined;

      if (!url || typeof url !== 'string') {
        // Invalid URL entry
        this.logger.warn('Invalid URL entry in the list:', url, chat.id);
        const invalidResult = {
          url: String(url) || 'invalid_url_entry',
          success: false,
          error: 'Invalid URL entry provided in the list.',
          board_id: null,
          snip: null,
          provided_title: title && title.trim() ? title.trim() : undefined,
          provided_album_url: album_url && album_url.trim() ? album_url.trim() : undefined,
        };

        results.push(invalidResult);

        yield {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: parameters.completion_block.id,
          data: invalidResult,
          path: `tool_result.snipsResults[${index}]`,
        } as CompletionStreamReplaceChunk<StreamChunkUnion>;

        failed_creations++;
        continue;
      }

      // Create child span for each URL
      const childSpan = span.span({
        name: 'create-single-snip-by-url',
        input: {
          url,
          user_id,
          board_id: common_board_id,
        },
      });

      try {
        // Yield current processing status
        yield {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: parameters.completion_block.id,
          data: {
            status: 'processing',
            current_url: url,
            current_index: index,
            total: urls.length,
          },
          path: 'tool_result.progress',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>;

        // Check if it's a YouMind URL
        const youMindUrl = this.checkYouMindUrl(url);
        if (youMindUrl) {
          const itemId = this.extractYouMindItemIdFromUrl(url, youMindUrl.type);
          childSpan.event({
            name: 'youmind-item-url-detected',
            input: { url, user_id, item_id: itemId, type: youMindUrl.type },
          });

          if (!itemId) {
            const invalidSnipResult = {
              url,
              success: false,
              error: 'Invalid YouMind Snip URL: could not extract Snip ID',
              board_id: common_board_id,
              snip: null,
              type: youMindUrl.type,
              provided_title: title && title.trim() ? title.trim() : undefined,
              provided_album_url: album_url && album_url.trim() ? album_url.trim() : undefined,
            };

            results.push(invalidSnipResult);

            yield {
              mode: CompletionStreamModeEnum.REPLACE,
              targetType: 'CompletionBlock',
              targetId: parameters.completion_block.id,
              data: invalidSnipResult,
              path: `tool_result.snipsResults[${index}]`,
            } as CompletionStreamReplaceChunk<StreamChunkUnion>;

            failed_creations++;
            childSpan.end();
            continue;
          }

          // Handle YouMind item URL
          const handleResult = await this.handleYouMindItemUrl(
            itemId,
            common_board_id,
            youMindUrl.type,
            user_id,
          );

          const specialResult = {
            url,
            success: handleResult.success,
            error: handleResult.error || null,
            board_id: common_board_id,
            snip: handleResult.snip || null,
            type: youMindUrl.type,
            provided_title: title && title.trim() ? title.trim() : undefined,
            provided_album_url: album_url && album_url.trim() ? album_url.trim() : undefined,
          };

          results.push(specialResult);

          yield {
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: parameters.completion_block.id,
            data: specialResult,
            path: `tool_result.snipsResults[${index}]`,
          } as CompletionStreamReplaceChunk<StreamChunkUnion>;

          if (handleResult.success) {
            successful_creations++;
            childSpan.event({
              name: 'youmind-item-url-processed-successfully',
              input: { url, user_id, item_id: itemId },
              output: handleResult.snip,
            });
          } else {
            failed_creations++;
            childSpan.event({
              name: 'youmind-item-url-processing-failed',
              input: {
                url,
                user_id,
                item_id: itemId,
                error: handleResult.error,
              },
            });
          }

          childSpan.end();
          continue;
        }

        // TODO: Implement tryCreateSnipByUrl when available
        const result = await this.tryCreateSnipByUrl({
          url,
          board_id: common_board_id,
          title,
          album_url_for_voice_snip: album_url,
        });

        if (!result) {
          childSpan.event({
            name: 'no-snip-created-for-url',
            input: { url, user_id },
          });
          const failResult = {
            url,
            success: false,
            error:
              'Cannot process this URL. Please ensure the URL points to a valid resource (webpage, PDF, image, etc.)',
            board_id: common_board_id,
            snip: null,
            provided_title: title && title.trim() ? title.trim() : undefined,
            provided_album_url: album_url && album_url.trim() ? album_url.trim() : undefined,
          };

          results.push(failResult);

          yield {
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: parameters.completion_block.id,
            data: failResult,
            path: `tool_result.snipsResults[${index}]`,
          } as CompletionStreamReplaceChunk<StreamChunkUnion>;

          failed_creations++;
        } else {
          childSpan.event({
            name: 'snip-created-for-url',
            input: { url, user_id, snip_id: result.id },
            output: result,
          });
          const successResult = {
            url,
            success: true,
            board_id:
              common_board_id ||
              (result.board_ids && result.board_ids.length > 0 ? result.board_ids[0] : null),
            snip: result as SnipUnknownWebpageVO,
            snip_predicted_type: this.predictSnipType(url),
            error: null,
            type: 'snip' as const,
            provided_title: title && title.trim() ? title.trim() : undefined,
            provided_album_url: album_url && album_url.trim() ? album_url.trim() : undefined,
          };

          results.push(successResult);

          yield {
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: parameters.completion_block.id,
            data: successResult,
            path: `tool_result.snipsResults[${index}]`,
          } as CompletionStreamReplaceChunk<StreamChunkUnion>;

          successful_creations++;
        }
        childSpan.end();
      } catch (error) {
        this.logger.error(`Failed to create Snip by URL: ${url}`, error);
        childSpan.event({
          name: 'create-snip-by-url-failed-for-url',
          input: {
            url,
            user_id,
            error: error instanceof Error ? (error as ErrorWithName)?.name : String(error),
          },
        });
        childSpan.end({
          level: SpanEventLevel.ERROR,
          statusMessage: (error as Error).message,
        });
        const errorResult = {
          url,
          success: false,
          error: `Failed to create Snip: ${(error as ErrorWithName)?.name || 'Unknown error'}  ${(error as ErrorWithName).message}`,
          board_id: common_board_id,
          snip: null,
          provided_title: title && title.trim() ? title.trim() : undefined,
          provided_album_url: album_url && album_url.trim() ? album_url.trim() : undefined,
        };

        results.push(errorResult);

        yield {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: parameters.completion_block.id,
          data: errorResult,
          path: `tool_result.snipsResults[${index}]`,
        } as CompletionStreamReplaceChunk<StreamChunkUnion>;

        failed_creations++;
      }

      // Update current processing progress
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: parameters.completion_block.id,
        data: {
          processed: index + 1,
          total: urls.length,
          successful: successful_creations,
          failed: failed_creations,
        },
        path: 'tool_result.stats',
      } as CompletionStreamReplaceChunk<StreamChunkUnion>;
    }

    let summary_message = '';
    if (urls.length === 1) {
      if (successful_creations === 1) {
        summary_message = `Successfully created 1 Snip.`;
      } else {
        summary_message = results[0].error || `Failed to create Snip.`;
      }
    } else {
      summary_message = `Attempted to create ${urls.length} Snips. ${successful_creations} succeeded, ${failed_creations} failed.`;
    }

    // Final update of summary info
    yield {
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: parameters.completion_block.id,
      data: summary_message,
      path: 'tool_result.summary_message',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>;

    return {
      response: summary_message,
      result: {
        snipsResults: results,
        summary_message,
      },
    };
  }

  private predictSnipType(
    url: string,
  ): SnipTypeEnum.ARTICLE | SnipTypeEnum.VOICE | SnipTypeEnum.VIDEO {
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return SnipTypeEnum.VIDEO;
    }
    if (url.includes('xiaoyuzhoufm.com') || url.includes('podcasts.apple.com')) {
      return SnipTypeEnum.VOICE;
    }
    return SnipTypeEnum.ARTICLE;
  }

  // Check if URL is a YouMind URL
  private checkYouMindUrl(url: string): {
    type: 'snip' | 'thought';
  } | null {
    try {
      const urlObj = new URL(url);
      const youmindDomains = ['youmind.ai'];

      const isValidDomain = youmindDomains.some(
        (domain) => urlObj.hostname === domain || urlObj.host === domain,
      );
      if (!isValidDomain) {
        return null;
      }

      const isSnip =
        /^\/snips\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          urlObj.pathname,
        );
      if (isSnip) {
        return {
          type: 'snip',
        };
      }

      const isThought =
        /^\/thoughts\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          urlObj.pathname,
        );
      if (isThought) {
        return {
          type: 'thought',
        };
      }
      return null;
    } catch {
      return null;
    }
  }

  // Extract item ID from YouMind URL
  private extractYouMindItemIdFromUrl(url: string, type: 'snip' | 'thought'): string | null {
    try {
      const urlObj = new URL(url);
      const match =
        type === 'snip'
          ? urlObj.pathname.match(
              /^\/snips\/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$/i,
            )
          : urlObj.pathname.match(
              /^\/thoughts\/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$/i,
            );
      return match ? match[1] : null;
    } catch {
      return null;
    }
  }

  private async handleYouMindItemUrl(
    itemId: string,
    boardId: string,
    type: 'snip' | 'thought',
    userId: string,
  ): Promise<{
    success: boolean;
    error?: string;
    snip?: SnipUnknownWebpageVO;
  }> {
    const space = await this.spaceDomain.getByUserId(userId);

    if (type === 'snip') {
      // TODO: Implement getSnip when available
      const snip = await this.getSnip({ id: itemId });

      if (!snip.id) {
        return {
          success: false,
          error: 'Snip not found',
        };
      }
      const newSnip = await this.snipDomain.clone(snip.id, userId, space.id, boardId);

      return {
        success: true,
        snip: newSnip as unknown as SnipUnknownWebpageVO,
      };
    }

    if (type === 'thought') {
      // TODO: Implement getThought when available
      const thought = await this.getThought({ id: itemId });

      if (!thought.id) {
        return {
          success: false,
          error: 'Thought not found',
        };
      }
      const newThought = await this.thoughtDomain.clone(thought.id, userId, space.id, boardId);

      return {
        success: true,
        snip: newThought as unknown as SnipUnknownWebpageVO,
      };
    }

    return {
      success: false,
      error: 'Invalid YouMind item type',
    };
  }

  // TODO: Implement when available
  private async tryCreateSnipByUrl(params: {
    url: string;
    board_id: string;
    title?: string;
    album_url_for_voice_snip?: string;
  }): Promise<SnipUnknownWebpageVO | null> {
    // Temporary implementation
    this.logger.warn('tryCreateSnipByUrl not fully implemented - using temporary stub');
    return null;
  }

  // TODO: Implement when available
  private async getSnip(params: { id: string }): Promise<any> {
    this.logger.warn('getSnip not fully implemented - using temporary stub');
    return { id: params.id };
  }

  // TODO: Implement when available
  private async getThought(params: { id: string }): Promise<any> {
    this.logger.warn('getThought not fully implemented - using temporary stub');
    return { id: params.id };
  }
}
