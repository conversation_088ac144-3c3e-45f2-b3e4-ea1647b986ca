/**
 * Create Board Tool Service - 创建板块工具服务
 * 创建新的板块
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/create_board.ts
 */

import { Injectable, Logger } from '@nestjs/common';

// Icon names as strings - backend shouldn't import React components
const AVAILABLE_ICONS = [
  'Archive',
  'Atom',
  'Audio',
  'BarChart',
  'Battery',
  'Beach',
  'Bell',
  'B<PERSON>',
  'Birthd',
  'Book',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  'Box',
  'Bug',
  'Calendar',
  'Camera',
  '<PERSON>',
  '<PERSON>',
  'Chat',
  'Cheers',
  '<PERSON>',
  'ChristmasTree',
  '<PERSON>',
  '<PERSON>',
  'Compass',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Database',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Drink',
  'Earth',
  'Education',
  'Family',
  'Film',
  'Fire',
  'Flag',
  'Flight',
  'Folder',
  'Footprint',
  'ForkAndKnife',
  'Game',
  'Ghost',
  'Gift',
  '<PERSON><PERSON>',
  'Handle',
  'Home',
  'Hourglass',
  'Image',
  'Ink',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'LightBulb',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Mail',
  'Money',
  'Monitor',
  'Mountain',
  'News',
  'Nut',
  'Operaglass',
  'Page',
  'Path',
  'Pencil',
  'Pills',
  'Pin',
  'Planet',
  'Playlist',
  'Podcast',
  'Purse',
  'Raining',
  'Sad',
  'Scissors',
  'Setting',
  'Ship',
  'Sun',
  'Tag',
  'Tea',
  'Terminal',
  'Test',
  'Thoughts',
  'ThreeLeaf',
  'Tree',
  'Video',
  'Workout',
  'Worm',
  'Wow',
  'Yoga',
] as const;

import { LangfuseSpanClient } from 'langfuse-core';
import { z } from 'zod';
import { InvalidArguments } from '@/common/errors';
import { YouapiClsService } from '@/common/services/cls.service';
import { ChatOriginTypeEnum, TOOL_TYPES } from '@/common/types';
import { ChatDAO } from '@/dao/chat';
import { BoardDomainService } from '../../board';
import { BaseToolService } from './base.service';
import type { ToolCallResult, ToolDefinition, ToolFunctionParameters } from './types';

// Use icon names directly instead of React components
const ICONS = AVAILABLE_ICONS;
const PALETTE = [
  '--foreground',
  '--function-gray',
  '--function-link',
  '--function-mint',
  '--function-green',
  '--function-indigo',
  '--function-purple',
  '--function-pink',
  '--function-red',
  '--function-orange',
  '--function-yellow',
  '--function-brown',
];

@Injectable()
export class CreateBoardService extends BaseToolService {
  private readonly logger = new Logger(CreateBoardService.name);

  readonly toolName: TOOL_TYPES = TOOL_TYPES.CREATE_BOARD;
  readonly toolOutputSchema = z.object({
    board_id: z.string(),
  });

  readonly toolDefinition: ToolDefinition = {
    max_calls: 1,
    type: 'function',
    function: {
      name: this.toolName,
      description: 'create board',
      parameters: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'should be less than 30 words',
          },
          icon_name: {
            type: 'string',
            enum: ICONS,
            description: 'choose an icon from the list',
          },
          icon_color: {
            type: 'string',
            enum: PALETTE,
            description: 'choose a color for the icon',
          },
          description: {
            type: 'string',
            description: 'should be less than 100 words',
          },
        },
        required: ['name'],
        additionalProperties: false,
      },
    },
  };

  constructor(
    private readonly boardDomain: BoardDomainService,
    private readonly chatDAO: ChatDAO,
    private readonly youbizClsService: YouapiClsService,
  ) {
    super();
  }

  async execute(
    parameters: ToolFunctionParameters,
    _span: LangfuseSpanClient,
  ): Promise<ToolCallResult> {
    const { user_id, chat, parsed_params } = parameters;
    const { name, description, icon_name, icon_color } = parsed_params;

    if (!name || !icon_name || !icon_color) {
      this.logger.error('name or icon_name or icon_color is not provided', {
        chatId: chat.id,
      });
      throw new InvalidArguments('missing name or icon_name or icon_color');
    }

    const spaceId = await this.youbizClsService.getSpaceId();
    const board = await this.boardDomain.create({
      space_id: spaceId || '',
      user_id,
      name,
      description,
      icon: {
        name: icon_name,
        color: icon_color,
      },
    });

    await this.chatDAO.updateOrigin(
      chat.id,
      {
        id: board.id,
        type: ChatOriginTypeEnum.BOARD,
      },
      board.id,
    );

    return {
      response: `Board "${name}" has been created successfully.`,
      result: {
        board_id: board.id,
      },
    };
  }
}
