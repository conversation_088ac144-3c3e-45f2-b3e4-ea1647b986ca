import { LangfuseSpanClient } from 'langfuse-core';
import { ChatCompletionTool } from 'openai/resources/index.mjs';
import { ZodSchema } from 'zod';
import { CompletionStreamChunk, MessageEvent, TOOL_TYPES } from '@/common/types';
import { StreamMessage } from '@/infra/youllm';
import { StreamChunkUnion } from '../ask_ai/handler';
import type { ChatDetail, CompletionBlock } from '../types';

export interface ToolContext {
  chat: ChatDetail;
  user_id: string;
  parsed_params: Record<string, string>;
  tool_call_id: string;
  completion_block: CompletionBlock;
}

export interface ToolFunctionParameters {
  chat: ChatDetail;
  user_id: string;
  parsed_params: Record<string, string>;
  tool_call_id: string;
  completion_block: CompletionBlock;
}
export interface ToolCallResult {
  response?: string; // completion_block 的 tool_response, 给 LLM 的调用结果
  result?: unknown; // completion_block 的 tool_result, 真实调用结果
  event?: MessageEvent; // @deprecated, 需转换为 result
}

export type ToolFunction = (
  param: ToolFunctionParameters,
  span: LangfuseSpanClient,
) =>
  | AsyncGenerator<StreamMessage | CompletionStreamChunk<StreamChunkUnion>, ToolCallResult>
  | Promise<ToolCallResult>;
export type ToolDefinition = ChatCompletionTool & {
  max_calls: number;
  execution_timeout?: number;
};
export type ToolCall = {
  tool_name: TOOL_TYPES;
  tool_function: ToolFunction;
  tool_definition: ToolDefinition;
  tool_output_schema?: ZodSchema;
};
