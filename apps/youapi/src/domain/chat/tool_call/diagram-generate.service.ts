/**
 * Diagram Generate Service - 图表生成服务
 * 提供SVG图表生成功能，支持多种图表类型和尺寸配置
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/diagram_generate.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { z } from 'zod';
import type {
  CompletionStreamAppendStringChunk,
  CompletionStreamReplaceChunk,
} from '@/common/types';
import {
  CompletionStreamModeEnum,
  LLMs,
  MessageDiagramGenerateResultSchema,
  TOOL_TYPES,
} from '@/common/types';
import { LLMRunner } from '../../../infra/youllm/llm_service/runner';
import { FileDomainService } from '../../file';
import { Directory } from '../../file/types';
import { YouLLMService } from '../../llm';
import { StreamChunkUnion } from '../ask_ai/handler';
import { getCurrentBoardIdByChat, isInsideNewBoardWorkflow } from '../util/isNewBoardChat';
import { BaseToolService } from './base.service';
import type { ToolDefinition, ToolFunctionParameters } from './types';
import { mindmapLayout, Node } from './utils/mindmap';

// TODO: These dependencies need to be migrated from @/lib
// import { YLDiagramGenerationOptions } from "@/lib/infra/youllm/types";
// import { createImage } from "@/lib/app/snip/create_snip";
// import { SnipImageVO } from "@/lib/app/snip/types";

type YLDiagramGenerationOptions = {
  size: 'auto' | 'square' | 'portrait' | 'landscape';
};

type SnipImageVO = {
  id: string;
  board_ids?: string[];
  [key: string]: any;
};

interface ErrorWithName {
  name?: string;
  message?: string;
}

const tool_definition: ToolDefinition = {
  max_calls: 3,
  execution_timeout: 240000,
  type: 'function',
  function: {
    name: TOOL_TYPES.DIAGRAM_GENERATE,
    description: `use this tool to generate a diagram or chart in SVG format.
This tool is good at generating more structured diagrams like flowchart, mindmap, sequence diagram, quadrant chart, timeline, PPT or keynote slides(Generate one page in one call) etc.
But if user ask for mermaid syntax, don't use this tool. Create an image snip if explicitly requested by the user.`,
    parameters: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: 'The text to generate a diagram for',
        },
        title: {
          type: 'string',
          description: 'The title of the diagram.',
        },
        type: {
          type: 'string',
          enum: [
            'flowchart',
            'mindmap',
            'sequence diagram',
            'quadrant chart',
            'timeline',
            'PPT or keynote slide',
            'other',
          ],
          description: 'The type of the diagram.',
        },
        size: {
          type: 'string',
          enum: ['auto', 'square', 'portrait', 'landscape'],
          description: 'The size of the generated diagram. Default to auto.',
        },
        board_id: {
          type: 'string',
          description:
            'Optional. Board ID to add the Snips to. If provided, all Snips will be added to this board.',
        },
        parent_board_group_id: {
          type: 'string',
          description: 'Optional. Board group ID',
        },
      },
      required: ['text', 'title', 'type'],
      additionalProperties: false,
    },
  },
};

@Injectable()
export class DiagramGenerateService extends BaseToolService {
  readonly toolName = TOOL_TYPES.DIAGRAM_GENERATE;
  readonly toolDefinition: ToolDefinition = tool_definition;
  readonly toolOutputSchema = MessageDiagramGenerateResultSchema;

  private readonly logger = new Logger(DiagramGenerateService.name);

  constructor(
    private readonly fileDomain: FileDomainService,
    private readonly youllm: YouLLMService,
  ) {
    super();
  }

  async *execute(parameters: ToolFunctionParameters, span: LangfuseSpanClient) {
    const { parsed_params, user_id, chat } = parameters;
    const {
      text,
      title,
      type,
      size = 'auto',
      parent_board_group_id: common_parent_board_group_id,
    } = parsed_params;
    const common_board_id = getCurrentBoardIdByChat(chat);

    if (!text) {
      return {
        response: 'Invalid text',
      };
    }

    const createSnip = isInsideNewBoardWorkflow(chat);

    let svg = '';
    if (type === 'mindmap') {
      // TODO: use mindmap prompt
      // @see https://lab.gooo.ai/project/clyhh5x0i0003eni7gbox5c0q/prompts/generate-mindmap
      // svg = await this.youllm.generateMindmap(text, {
      //   runner,
      //   modelOptions: {
      //     model: LLMs.CLAUDE_4_SONNET,
      //     max_tokens: 8192,
      //   },
      // });

      const root = {
        id: 'Modeling Methods',
        isRoot: true,
        children: [
          {
            id: 'Classification',
            children: [],
          },
        ],
      };

      const rootNode = mindmapLayout(root, {
        direction: 'H',
        getHeight(d) {
          if (d.isRoot) {
            return 60;
          }
          return 30;
        },
        getWidth(d) {
          const padding = d.isRoot ? 40 : 30;
          const fontSize = d.isRoot ? 24 : 16;
          // return (
          //   measureText(d.id, fontSize) + padding
          // );
          return 100;
        },
        getVGap: () => 6,
        getHGap: () => 60,
        getSubTreeSep(d) {
          if (!d.children || !d.children.length) {
            return 0;
          }
          return 20;
        },
      });

      svg =
        '<svg width="100%" height="100%" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">\n';
      const drawLink = (n: Node, c: Node) => {
        let beginNode = n;
        let endNode = c;
        let side = 'right';
        if (n.x > c.x) {
          side = 'left';
          beginNode = c;
          endNode = n;
        }
        let beginX = Math.round(beginNode.x + beginNode.width - beginNode.hgap);
        let beginY = Math.round(beginNode.y + beginNode.height / 2);
        let endX = Math.round(endNode.x + endNode.hgap);
        let endY = Math.round(endNode.y + endNode.height / 2);
        if (beginNode.isRoot()) {
          beginX = Math.round(beginNode.x + beginNode.width / 2);
          beginY = Math.round(beginNode.y + beginNode.height / 2);
        }
        if (endNode.isRoot()) {
          endX = Math.round(endNode.x + endNode.width / 2);
          endY = Math.round(endNode.y + endNode.height / 2);
        }
        svg += `<path d="${
          (
            side === 'right'
              ? `M${beginX},${beginY} `
              : `M${beginNode.x + beginNode.hgap},${beginY} L${beginX},${beginY} `
          ) +
          `C${Math.round(beginX + (beginNode.hgap + endNode.hgap) / 2)},${beginY} ${Math.round(endX - (beginNode.hgap + endNode.hgap) / 2)},${endY} ${endX},${endY}` +
          (side === 'right' ? `L${endX + endNode.width - endNode.hgap * 2},${endY}` : '')
        }" stroke="black" stroke-width="4" fill="none" />`;
      };

      const drawNode = (node: Node) => {
        const origin = node.data;
        // const color = randomColor();
        const x = Math.round(node.x + node.hgap);
        const y = Math.round(node.y + node.vgap);
        const width = Math.round(node.width - node.hgap * 2);
        const height = Math.round(node.height - node.vgap * 2);
        const rect = `<rect x="${x}" y="${origin.isRoot ? y : y - 18}" width="${width}" height="${height}" fill="red" />`;
        svg += rect;
      };

      rootNode.eachNode((node: Node) => {
        node.children.forEach((child: Node) => {
          drawLink(node, child);
        });
        drawNode(node);
      });
      svg += '</svg>';
    } else {
      const runner = new LLMRunner(span);
      const generate_params = {
        text,
        type,
        size: size as YLDiagramGenerationOptions['size'],
      };
      const generateStream = await runner.runSpan({
        name: 'generate-svg-diagram',
        input: generate_params,
        fn: async () => {
          const message = await this.youllm.generateSVGDiagram(generate_params, {
            runner,
            modelOptions: {
              model: LLMs.CLAUDE_4_SONNET,
              max_tokens: 8192,
            },
          });
          return message;
        },
      });

      let generatedContent = '';
      for await (const chunk of generateStream) {
        const chunkContent = chunk.choices[0]?.delta?.content;
        if (chunkContent) {
          generatedContent += chunkContent;
        }
      }
      const generatedSVG = this.formatSVG(generatedContent);
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: parameters.completion_block.id,
        data: generatedSVG,
        path: 'tool_result.svg',
      } as CompletionStreamReplaceChunk<StreamChunkUnion>;

      const optimize_params = {
        svg: generatedSVG,
        type,
      };
      const stream = await runner.runSpan({
        name: 'optimize-svg-diagram',
        input: optimize_params,
        fn: async () => {
          const message = await this.youllm.optimizeSVGDiagram(optimize_params, {
            runner,
            modelOptions: {
              model: LLMs.GEMINI_25_PRO,
              max_tokens: 8192 * 3,
            },
          });
          return message;
        },
      });

      let accumulatedContent = '';
      let received = false;
      for await (const chunk of stream) {
        if (!received) {
          received = true;
          yield {
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: parameters.completion_block.id,
            data: '',
            path: 'tool_result.svg',
          } as CompletionStreamReplaceChunk<StreamChunkUnion>;
        }
        const chunkContent = chunk.choices[0]?.delta?.content;
        if (chunkContent) {
          accumulatedContent += chunkContent;
        }
        yield {
          mode: CompletionStreamModeEnum.APPEND_STRING,
          targetType: 'CompletionBlock',
          targetId: parameters.completion_block.id,
          data: chunkContent,
          path: 'tool_result.svg',
        } as CompletionStreamAppendStringChunk;
      }

      // escape & to &amp; to avoid `xmlParseEntityRef: no name` error
      svg = this.formatSVG(accumulatedContent || '');
    }

    // Upload the SVG code to AWS S3
    const hash = await this.fileDomain.uploadStringOrBuffer(Buffer.from(svg), {
      visibility: 'public',
      contentType: 'image/svg+xml',
      directory: Directory.GEN_IMAGES,
    });

    const image_url = `https://cdn.gooo.ai/${Directory.GEN_IMAGES}/${hash}.svg`;

    const result: z.infer<typeof MessageDiagramGenerateResultSchema> = {
      svg,
      image_url,
    };

    if (createSnip) {
      const childSpan = span.span({
        name: 'create-image-snip',
        input: {
          image_url,
          user_id,
          board_id: common_board_id,
          parent_board_group_id: common_parent_board_group_id,
        },
      });

      try {
        yield {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: parameters.completion_block.id,
          data: {
            status: 'processing',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>;

        // TODO: Replace with proper snipDomain.createImage when available
        const snipResult = (await this.createImage({
          file: {
            name: title,
            hash,
            is_public: true,
          },
          title,
          board_id: common_board_id,
          parent_board_group_id: common_parent_board_group_id,
        })) as SnipImageVO;

        if (snipResult) {
          childSpan.event({
            name: 'create-image-snip-success',
            input: { url: image_url, user_id, snip_id: snipResult?.id },
            output: snipResult,
          });

          const successResult = {
            status: 'success',
            board_id:
              common_board_id ||
              (snipResult?.board_ids && snipResult.board_ids.length > 0
                ? snipResult.board_ids[0]
                : null),
            vo: snipResult,
          };

          result.snip = successResult;
          yield {
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: parameters.completion_block.id,
            data: successResult,
            path: 'tool_result.snip',
          } as CompletionStreamReplaceChunk<StreamChunkUnion>;

          childSpan.end();
        }
      } catch (error) {
        childSpan.event({
          name: 'create-image-snip-failed',
          input: {
            image_url,
            user_id,
            error: error instanceof Error ? (error as ErrorWithName)?.name : String(error),
          },
        });
        childSpan.end({
          level: 'ERROR',
          statusMessage: (error as Error).message,
        });
        result.snip = {
          status: 'failed',
        };

        yield {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: parameters.completion_block.id,
          data: {
            status: 'failed',
          },
          path: 'tool_result.snip',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>;
      }
    }

    return {
      response: `Diagram finished generating, it can be accessed at ${image_url}. Use this URL only as input for other tool calls. This URL will be presented using customized UI component, don't repeat this URL in your response. Doing so will be penalized.`,
      result,
    };
  }

  // TODO: Implement createImage when migrated from @/lib/app/snip/create_snip
  private async createImage(params: {
    file: { name: string; hash: string; is_public: boolean };
    title: string;
    board_id: string;
    parent_board_group_id?: string;
  }): Promise<SnipImageVO> {
    // Temporary implementation
    this.logger.warn('createImage not fully implemented - using temporary stub');
    return {
      id: 'temp-diagram-snip-id',
      board_ids: [params.board_id],
      title: params.title,
    };
  }

  private formatSVG(svg: string) {
    // escape & to &amp; to avoid `xmlParseEntityRef: no name` error
    return (svg || '')
      .replace(/&/g, '&amp;')
      .replace('```xml', '') // remove ```xml and ```
      .replace('```svg', '') // remove ```svg and ```
      .replace('```other', '') // remove ```other and ```
      .replace('```', '');
  }
}
