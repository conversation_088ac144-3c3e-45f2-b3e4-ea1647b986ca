/**
 * Edit Thought Tool Service - 编辑思想工具服务
 * 创建或编辑思想内容
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/edit_thought.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { z } from 'zod';
import { YouapiClsService } from '@/common/services/cls.service';
import { UserDomainService } from '@/domain/user';
import { TOOL_TYPES } from '../../../common/types';
import { truncate } from '../../../common/utils';
import { LLMRunner } from '../../../infra/youllm/llm_service/runner';
import { BoardItemDomainService } from '../../board-item';
import { BoardItemTypeEnum } from '../../board-item/types';
import { YouLLMService } from '../../llm';
import { SpaceDomainService } from '../../space';
import { ThoughtDomainService } from '../../thought';
import { ThoughtVersionTypeEnum } from '../../thought/types';
import { getCurrentBoardIdByChat, isInsideNewBoardWorkflow } from '../util/isNewBoardChat';
import { BaseToolService } from './base.service';
import type { ToolCallResult, ToolDefinition, ToolFunctionParameters } from './types';

@Injectable()
export class EditThoughtService extends BaseToolService {
  private readonly logger = new Logger(EditThoughtService.name);

  readonly toolName: TOOL_TYPES = TOOL_TYPES.EDIT_THOUGHT;
  readonly toolOutputSchema = z.any();

  readonly toolDefinition: ToolDefinition = {
    max_calls: 3,
    type: 'function',
    function: {
      name: this.toolName,
      description: `Use this tool to propose an edit to a thought.
This will be read by a less intelligent model, which will quickly apply the edit. You should make it clear what the edit is, while also minimizing the unchanged text you write.
When writing the edit, you should specify each edit in sequence, with the special notation [...] to represent unchanged text in between edited lines.
For example:
[...]
FIRST_EDIT
[...]
SECOND_EDIT
[...]
THIRD_EDIT
[...]
You should bias towards repeating as few lines of the original thought as possible to convey the change.
But, each edit should contain sufficient context of unchanged lines around the text you are editing to resolve ambiguity.
DO NOT omit spans of pre-existing text without using the [...] notation to indicate its absence.
Make sure it is clear what the edit should be.

If you are creating a new thought, you should specify the following arguments before the others: [title_edit]`,
      parameters: {
        type: 'object',
        properties: {
          instructions: {
            type: 'string',
            description:
              'A single sentence instruction describing what you are going to do for the sketched edit. This is used to assist the less intelligent model in applying the edit. Dont repeat what you have said previously in normal messages. And use it to disambiguate uncertainty in the edit.',
          },
          thought_id: {
            type: 'string',
            description:
              'The UUID of the thought to edit. If not provided, a new thought will be created.',
          },
          title_edit: {
            type: 'string',
            description:
              'The new title of the thought. If not provided, the title will not be changed. When creating a new thought, you must always provide the title_edit. Keep the title concise and not too long (recommended under 50 characters).',
          },
          text_edit: {
            type: 'string',
            description:
              "Specify ONLY the precise lines of markdown text that you wish to edit. **NEVER specify the thought's title (H1 heading) in text_edit, use title_edit parameter instead**. **NEVER specify or write out unchanged text**. Instead, represent all unchanged text using the notation - example: `[...]`. **DO NOT** wrap your edits in code blocks using triple backticks (```). Provide the raw text edit directly.",
          },
        },
        required: ['instructions'],
        additionalProperties: false,
      },
    },
  };

  constructor(
    private readonly boardItemDomain: BoardItemDomainService,
    private readonly youllm: YouLLMService,
    private readonly spaceDomain: SpaceDomainService,
    private readonly thoughtDomain: ThoughtDomainService,
    private readonly youbizClsService: YouapiClsService,
    private readonly userDomainService: UserDomainService,
  ) {
    super();
  }

  async execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
  ): Promise<ToolCallResult> {
    const { parsed_params, user_id, chat } = parameters;
    const { thought_id, title_edit, text_edit, instructions } = parsed_params;
    const lastUserMessage = chat.messages.findLast((it) => it.role === 'user');
    const isCommand = !!lastUserMessage?.command;
    const versionTitle = lastUserMessage?.command
      ? truncate((lastUserMessage?.command as string) ?? '', 255)
      : truncate((lastUserMessage?.content as string) ?? '', 255);

    const boardId = getCurrentBoardIdByChat(chat);
    let thought = thought_id ? await this.thoughtDomain.getById(thought_id) : undefined;
    const finalThoughtTitle = title_edit || thought?.title;
    let finalTextEdit = text_edit;

    if (text_edit && text_edit.trim().startsWith('# ')) {
      // Remove H1 title from text edit
      finalTextEdit = text_edit.replace(/^\s*# .*?\n/, '');
      if (finalThoughtTitle && finalTextEdit.includes(finalThoughtTitle)) {
        const headingRegex = new RegExp(`^\\s*(#{1,6})\\s+${finalThoughtTitle}\\s*\\n`, 'i');
        finalTextEdit = finalTextEdit.replace(headingRegex, '');
      }
    }

    // Handle special case where only title is being edited
    if (finalTextEdit && finalTextEdit.trim() === '[...]') {
      finalTextEdit = '';
    }

    if (!thought_id) {
      // Create new thought
      const space = await this.spaceDomain.getByUserId(user_id);
      thought = await this.thoughtDomain.create({
        title: title_edit,
        content: {
          raw: this.markdownToYjsBase64(finalTextEdit || ''),
          plain: finalTextEdit || '',
        },
        user_id,
        space_id: space.id,
      });

      await this.boardItemDomain.putEntityToBoard({
        board_id: boardId,
        entity_type: BoardItemTypeEnum.THOUGHT,
        entity_id: thought.id,
      });

      // Create version record
      await this.thoughtDomain.createThoughtVersion({
        thought_id: thought.id,
        type: ThoughtVersionTypeEnum.AI,
        title: versionTitle,
        description: truncate(instructions, 2048),
        thought_title: thought.title,
        content: thought.content,
      });

      return {
        response: 'The edit_thought tool call was successful. The thought has been created.',
        result: thought,
      };
    } else {
      // Edit existing thought
      const runner = new LLMRunner(span);
      thought = await this.thoughtDomain.getById(thought_id);
      const userId = await this.youbizClsService.getUserId();
      const user = await this.userDomainService.selectOneById(userId);
      let newFulltext: string | undefined;

      if (finalTextEdit) {
        const params = {
          fulltext: thought.content.plain || '',
          textEdit: finalTextEdit,
          instructions,
        };
        const modelOptions = {
          prediction: [thought?.content.plain || '', finalTextEdit],
        };
        const result = await runner.runSpan({
          name: 'instant-apply',
          input: params,
          fn: async () => {
            const start = Date.now();
            const result = await this.youllm.instantApply(params, false, {
              modelOptions,
              runner,
            });
            const end = Date.now();
            this.logger.log('instantApply cost', end - start);
            return result;
          },
        });
        newFulltext = result.choices[0].message?.content ?? undefined;
      }

      // Backup previous version
      let latestThoughtVersion = await this.thoughtDomain.tryGetLatestThoughtVersion(thought_id);
      if (
        !latestThoughtVersion ||
        latestThoughtVersion.content.plain !== thought.content.plain ||
        latestThoughtVersion.title !== thought.title
      ) {
        latestThoughtVersion = await this.thoughtDomain.createThoughtVersion({
          thought_id: thought.id,
          type: ThoughtVersionTypeEnum.BACKUP,
          title: `${user.name} modified`,
          thought_title: thought.title,
          content: thought.content,
        });
      }

      const newThought = await this.thoughtDomain.patch({
        id: thought_id,
        title: title_edit,
        content: newFulltext
          ? {
              raw:
                isInsideNewBoardWorkflow(chat) || isCommand
                  ? this.markdownToYjsBase64(newFulltext)
                  : (await runner.runSpan({
                      name: 'apply-markdown-to-yjs-base64',
                      input: {
                        oldContent: {
                          ...thought,
                          content: {
                            raw: thought.content.raw,
                          },
                        },
                        newMarkdown: newFulltext,
                      },
                      fn: async () => {
                        return this.applyMarkdownToYjsBase64(thought!.content.raw, newFulltext);
                      },
                    })) || '',
              plain: newFulltext,
            }
          : undefined,
      });

      // Create new version record
      const currentVersion = await this.thoughtDomain.createThoughtVersion({
        thought_id: newThought.id,
        type: ThoughtVersionTypeEnum.AI,
        title: versionTitle,
        description: truncate(instructions, 2048),
        thought_title: newThought.title,
        content: newThought.content,
      });

      return {
        response: 'The edit_thought tool call was successful. The thought has been updated.',
        result: {
          ...newThought,
          previous_version_id: latestThoughtVersion?.id,
          current_version_id: currentVersion.id,
        },
      };
    }
  }

  /**
   * Convert Markdown content directly to Yjs serialized base64 format
   * 将Markdown内容直接转换为Yjs序列化的base64格式
   */
  private markdownToYjsBase64(markdown: string): string {
    this.logger.log(`markdownToYjsBase64\nnewMarkdown: ${markdown}`);
    // TODO: Implement when MarkdownParseBackend, SchemaExtension, and convertJSONToBase64 are migrated
    // Temporary implementation - return base64 encoded markdown
    return Buffer.from(markdown).toString('base64');
  }

  /**
   * Apply markdown to a possibly diff-containing yjs base64 content
   * 将 markdown 应用到一个可能带有 diff 数据的 yjs base64 内容上
   */
  private applyMarkdownToYjsBase64(base64Content: string, markdown: string): string | undefined {
    this.logger.log(
      `applyMarkdownToYjsBase64\noldBase64Content: ${base64Content}\nnewMarkdown: ${markdown}`,
    );
    // TODO: Implement when diffTransformUtils, diffControllerBackend, and editorContentUtilsBackend are migrated
    // Temporary implementation - return new markdown as base64
    return Buffer.from(markdown).toString('base64');
  }
}
