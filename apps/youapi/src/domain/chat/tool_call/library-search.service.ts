/**
 * Library Search Tool Service - 库搜索工具服务
 * 在YouMind中搜索用户处理的所有材料
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/library_search.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { z } from 'zod';
import {
  ChatModeEnum,
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  TOOL_TYPES,
} from '../../../common/types';
import { BoardDomainService } from '../../board';
import { StreamChunkUnion } from '../ask_ai/handler';
import { ContextBuilder } from '../context';
import type { AtReferenceForContextBuilding, MessageAnalyzedContextType } from '../context/types';
import { convertToContextString, convertToSearchResults } from '../util/convertToSearchResult';
import { BaseToolService } from './base.service';
import type { ToolCallResult, ToolDefinition, ToolFunctionParameters } from './types';

@Injectable()
export class LibrarySearchService extends BaseToolService {
  private readonly logger = new Logger(LibrarySearchService.name);

  readonly toolName: TOOL_TYPES = TOOL_TYPES.LIBRARY_SEARCH;
  readonly toolOutputSchema = z.object({
    query: z.string(),
  });

  readonly toolDefinition: ToolDefinition = {
    max_calls: 3,
    type: 'function',
    function: {
      name: 'library_search',
      description: `Search all materials processed by current user in YouMind.
Use this tool only if the user ask to search all the content in YouMind, with clear instructions like "search all boards", "search my library", "search everything", etc,
or stated that use "library search" tool.
If users' query includes "@youmind", you should prioritize using this tool.`,
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description:
              'A high quality search query to be used to semantically search in YouMind, convert relative date to concrete date if applicable',
          },
        },
        required: ['query'],
        additionalProperties: false,
      },
    },
  };

  constructor(
    private readonly boardDomain: BoardDomainService,
    private readonly contextBuilder: ContextBuilder,
  ) {
    super();
  }

  async *execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
  ): AsyncGenerator<any, ToolCallResult> {
    const { user_id, chat, parsed_params, completion_block } = parameters;
    const { query } = parsed_params;

    const finalAtReferences: AtReferenceForContextBuilding[] = [
      {
        entity_type: 'library',
      },
    ];

    const isNewBoardChat = chat.mode === ChatModeEnum.NEW_BOARD;
    const availableTokens = 40000;

    const analysis = {
      needsContext: true,
      contextType: 'relevant' as MessageAnalyzedContextType,
      searchKeywords: query,
    };

    yield {
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completion_block.id,
      data: `Searching in YouMind`,
      path: 'tool_result.statusMessage',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>;

    const contextResult = await this.contextBuilder.buildContextFromReferences({
      messages: chat.messages || [],
      at_references: finalAtReferences,
      availableTokens,
      userId: user_id,
      trace: span,
      analysis,
    });

    const searchResults = convertToSearchResults(contextResult);

    // 提取 snip 和 thought IDs
    const snipIds: string[] = [];
    const thoughtIds: string[] = [];

    searchResults.forEach((result) => {
      if (result.entity_type === 'snip') {
        snipIds.push(result.entity_id);
      } else if (result.entity_type === 'thought') {
        thoughtIds.push(result.entity_id);
      }
    });

    // 获取 board 信息
    const boardBriefs = await this.boardDomain.listSimpleBoardsByEntityIds(snipIds, thoughtIds);

    // 创建实体ID到board名称的映射
    const entityToBoardsMap = new Map<string, string[]>();

    boardBriefs.forEach((board) => {
      // 为每个 snip 添加 board 名称
      board.snip_ids?.filter(Boolean).forEach((snipId) => {
        if (!entityToBoardsMap.has(snipId)) {
          entityToBoardsMap.set(snipId, []);
        }
        entityToBoardsMap.get(snipId)!.push(board.name);
      });

      // 为每个 thought 添加 board 名称
      board.thought_ids?.filter(Boolean).forEach((thoughtId) => {
        if (!entityToBoardsMap.has(thoughtId)) {
          entityToBoardsMap.set(thoughtId, []);
        }
        entityToBoardsMap.get(thoughtId)!.push(board.name);
      });
    });

    // 为每个搜索结果添加 board 名称
    const enrichedSearchResults = searchResults.map((result) => ({
      ...result,
      board_name: entityToBoardsMap.get(result.entity_id)?.join(', ') || 'No board',
    }));

    // biome-ignore lint/suspicious/noImplicitAnyLet: migration
    let result, response;
    if (enrichedSearchResults.length > 0) {
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: completion_block.id,
        data: enrichedSearchResults,
        path: 'tool_result.results',
      } as CompletionStreamReplaceChunk<StreamChunkUnion>;

      result = {
        statusMessage: `Searched in YouMind`,
      };
      response = `Following chunks are retrieved from YouMind${
        isNewBoardChat
          ? `, choose wisely to save to board by calling ${TOOL_TYPES.CREATE_SNIP_BY_URL} tool`
          : ''
      }: \n\n ${convertToContextString(contextResult)}`;
    } else {
      result = {
        statusMessage: `No related content found in YouMind`,
      };
      response = `No related content found in YouMind`;
    }

    return {
      result: {
        statusMessage: result.statusMessage,
        results: enrichedSearchResults,
      },
      response,
    };
  }
}
