import { uuidv7 } from 'uuidv7';
import {
  type InternetSearchResult,
  type MessageEvent,
  MessageEventTypeEnum,
  SearchResultTypeEnum,
  TOOL_TYPES,
} from '@/common/types';
import type { ToolCompletionBlock } from '../../types';

/**
 * Convert tool call result to message event (for backward compatibility)
 * 将工具调用结果转换为消息事件（向后兼容）
 */
export function convertToolCallResultToMessageEvent(block: ToolCompletionBlock): MessageEvent {
  switch (block.tool_name) {
    case TOOL_TYPES.GOOGLE_SEARCH:
      return {
        type: MessageEventTypeEnum.SEARCH,
        query: block.tool_arguments?.query || '',
        results:
          block.tool_result?.results?.map((r: InternetSearchResult) => ({
            entity_type: SearchResultTypeEnum.INTERNET_SEARCH_RESULT,
            entity_vo: r,
          })) || [],
        time_of_action: block.updated_at.toISOString(),
      };

    case TOOL_TYPES.BOARD_SEARCH:
    case TOOL_TYPES.LIBRARY_SEARCH:
    case 'resolve_library':
      return {
        type: MessageEventTypeEnum.RESOLVE_LIBRARY,
        query: block.tool_arguments?.query || '',
        results: block.tool_result?.results || [],
        time_of_action: block.updated_at.toISOString(),
      };

    case TOOL_TYPES.IMAGE_GENERATE:
    case TOOL_TYPES.DIAGRAM_GENERATE:
    case TOOL_TYPES.AUDIO_GENERATE:
    case TOOL_TYPES.CREATE_SNIP_BY_URL:
      // These tools don't produce search results, so return empty results
      return { results: [] } as unknown as MessageEvent;

    default:
      return { results: [] } as unknown as MessageEvent;
  }
}

/**
 * Convert message event to tool call result (for backward compatibility)
 * 将消息事件转换为工具调用结果（向后兼容）
 */
export function convertMessageEventToToolCallResult(event: MessageEvent): {
  tool_id: string;
  tool_name: TOOL_TYPES;
  tool_arguments: Record<string, unknown>;
  tool_response: string;
  tool_result: unknown;
} {
  if (event.type === MessageEventTypeEnum.SEARCH) {
    const is_web_search = event.results?.some(
      (r) => r.entity_type === SearchResultTypeEnum.INTERNET_SEARCH_RESULT,
    );

    if (is_web_search) {
      const actual_results = event.results?.map((r) => r.entity_vo);
      return {
        tool_id: uuidv7(),
        tool_name: TOOL_TYPES.GOOGLE_SEARCH,
        tool_arguments: {
          query: event.query,
          type: 'webpage',
        },
        tool_response: JSON.stringify({ results: actual_results }),
        tool_result: {
          results: actual_results || [],
        },
      };
    } else {
      const actual_results = event.results?.map((r) => ({
        entity_id: r.entity_id,
        entity_type: r.entity_type,
        related_chunk: r.related_chunk,
      }));
      return {
        tool_id: uuidv7(),
        tool_name: TOOL_TYPES.LIBRARY_SEARCH,
        tool_arguments: {
          query: event.query,
        },
        tool_response: JSON.stringify({ results: actual_results }),
        tool_result: { results: actual_results || [] },
      };
    }
  }

  if (event.type === MessageEventTypeEnum.RESOLVE_LIBRARY) {
    const actual_results = event.results?.map((r) => ({
      entity_id: r.entity_id,
      entity_type: r.entity_type,
      related_chunk: r.related_chunk,
    }));
    return {
      tool_id: uuidv7(),
      tool_name: TOOL_TYPES.LIBRARY_SEARCH,
      tool_arguments: {
        query: event.query,
      },
      tool_response: JSON.stringify({ results: actual_results }),
      tool_result: { results: actual_results || [] },
    };
  }

  // TODO: 添加其他事件类型的转换逻辑

  throw new Error(`Unsupported tool call: ${(event as MessageEvent).type}`);
}
