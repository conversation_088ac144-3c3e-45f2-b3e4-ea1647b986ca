/**
 * Audio Generate Service - 音频生成服务
 * 提供文本转语音功能，支持多种语音模型和参数配置
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/audio_generate.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { LangfuseSpanClient } from 'langfuse-core';
import { LLMProviders, LLMs, MessageAudioGenerateResultSchema, TOOL_TYPES } from '@/common/types';
import { LLMRunner } from '../../../infra/youllm/llm_service/runner';
import { FileDomainService } from '../../file';
import { Directory } from '../../file/types';
import { SpaceDomainService } from '../../space';
import { UsageRecordDomainService } from '../../usage-record';
import { getCurrentBoardIdByChat, isInsideNewBoardWorkflow } from '../util/isNewBoardChat';
import { BaseToolService } from './base.service';
import type { ToolCallResult, ToolDefinition, ToolFunctionParameters } from './types';

// TODO: These dependencies need to be migrated from @/lib
// import { createVoice, patchVoiceTranscript } from '@/lib/app/snip/create_snip';
// import type { SnipVoiceVO } from '@/lib/app/snip/types';
// import type { MinimaxEmotion, MinimaxVoice } from '@/lib/common/tts';
// import type { MinimaxLLMProvider } from '@/lib/infra/youllm/provider/minimax';
// import type { YLOpenAILLMProvider } from '@/lib/infra/youllm/provider/openai';

type MinimaxVoice = string;
type MinimaxEmotion = string;
type MinimaxLLMProvider = any;
type YLOpenAILLMProvider = any;
type SnipVoiceVO = {
  id: string;
  board_ids?: string[];
  [key: string]: any;
};

interface ErrorWithName {
  name?: string;
  message?: string;
}

const tool_definition: ToolDefinition = {
  max_calls: 3,
  type: 'function',
  function: {
    name: TOOL_TYPES.AUDIO_GENERATE,
    description:
      "use this tool to generate an audio from text with a given voice. This tool may incur costs. Use only when explicitly requested by the user. Create an audio snip if explicitly requested by the user. The tool returns an album_url (cover image) that can be used with create_snip_by_url tool's album_urls parameter.",
    parameters: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description:
            'Text to be synthesized. Character limit < 5000 chars. Paragraph markers will be replaced by line breaks. (To manually add a pause, insert the phrase <#x#> where x refers to the number of seconds to pause. Supports 0.01-99.99, with an accuracy of at most 2 decimal places)',
        },
        title: {
          type: 'string',
          description: 'The title of the audio.',
        },
        voice: {
          type: 'string',
          description:
            'Desired voice. Both system voice ids and cloned voice ids are supported. System voice ids are listed below: Wise_Woman, Friendly_Person, Inspirational_girl, Deep_Voice_Man, Calm_Woman, Casual_Guy, Lively_Girl, Patient_Man, Young_Knight, Determined_Man, Lovely_Girl, Decent_Boy, Imposing_Manner, Elegant_Man, Abbess, Sweet_Girl_2, Exuberant_Girl. Default to Wise_Woman.',
        },
        speed: {
          type: 'number',
          description:
            'The speed of the audio from 0.5 to 2.0. Default to 1. The speed of the generated speech. Larger values indicate faster speech.',
        },
        vol: {
          type: 'number',
          description:
            'The volume of the audio from 0.1 to 10.0. Default to 1. Larger values indicate larger volumes.',
        },
        pitch: {
          type: 'number',
          description:
            'The pitch of the audio from -12 to 12. Default to 0. The pitch of the generated speech. A value of 0 corresponds to default voice output.',
        },
        emotion: {
          type: 'string',
          description:
            'The emotion of the audio from happy, sad, angry, fearful, disgusted, surprised, neutral. Default to happy.',
        },
        board_id: {
          type: 'string',
          description:
            'Optional. Board ID to add the Snips to. If provided, all Snips will be added to this board.',
        },
        parent_board_group_id: {
          type: 'string',
          description: 'Optional. Board group ID',
        },
      },
      required: ['title', 'text', 'voice'],
      additionalProperties: false,
    },
  },
};

@Injectable()
export class AudioGenerateService extends BaseToolService {
  readonly toolName = TOOL_TYPES.AUDIO_GENERATE;
  readonly toolDefinition: ToolDefinition = tool_definition;
  readonly toolOutputSchema = MessageAudioGenerateResultSchema;

  private readonly logger = new Logger(AudioGenerateService.name);

  constructor(
    private readonly fileDomain: FileDomainService,
    private readonly usageRecordDomain: UsageRecordDomainService,
    private readonly spaceDomain: SpaceDomainService,
    private readonly eventBus: EventBus,
  ) {
    super();
  }

  async execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
  ): Promise<ToolCallResult> {
    const { parsed_params, user_id, chat } = parameters;
    const { text } = parsed_params;
    const {
      title,
      voice,
      speed,
      vol,
      pitch,
      emotion,
      parent_board_group_id: common_parent_board_group_id,
    } = parsed_params;
    const common_board_id = getCurrentBoardIdByChat(chat);

    if (!text) {
      return {
        response: 'Invalid text',
      };
    }

    const createSnip = isInsideNewBoardWorkflow(chat);

    const runner = new LLMRunner(span);
    const { hash, audio_url, subtitleFile, extraInfo, image_url } = await this.generate(runner, {
      user_id,
      text,
      title,
      voice,
      speed,
      vol,
      pitch,
      emotion,
    });

    if (createSnip) {
      try {
        const snip = await this.createVoice({
          file: { name: title, hash, is_public: true },
          title,
          board_id: common_board_id,
          parent_board_group_id: common_parent_board_group_id,
          hero_image_url: image_url,
        });
        if (subtitleFile) {
          await this.patchVoiceTranscript({
            snipId: snip.id,
            subtitleFileUrl: subtitleFile,
          });
        }
        return {
          response: `Audio generated successfully. Audio URL: ${audio_url}. Album URL: ${image_url}.`,
          result: {
            title,
            audio_url,
            subtitle_file: subtitleFile,
            extra_info: extraInfo,
            album_url: image_url,
            snip,
          },
        };
      } catch (error) {
        this.logger.error('Failed to create voice snip', error);
        return {
          response: `Audio generated successfully but failed to create snip. Audio URL: ${audio_url}. Album URL: ${image_url}.`,
          result: {
            title,
            audio_url,
            subtitle_file: subtitleFile,
            extra_info: extraInfo,
            album_url: image_url,
          },
        };
      }
    }

    return {
      response: `Audio generated successfully. Audio URL: ${audio_url}. Album URL: ${image_url}.`,
      result: {
        title,
        audio_url,
        subtitle_file: subtitleFile,
        extra_info: extraInfo,
        album_url: image_url,
      },
    };
  }

  private async createVoice(params: {
    file: { name: string; hash: string; is_public: boolean };
    title: string;
    board_id: string;
    parent_board_group_id?: string;
    hero_image_url?: string;
  }): Promise<SnipVoiceVO> {
    // TODO: Implement createVoice when snip domain is migrated
    this.logger.warn('createVoice not implemented - using temporary stub');
    return {
      id: 'temp-snip-id',
      board_ids: [params.board_id],
    };
  }

  private async patchVoiceTranscript(_params: {
    snipId: string;
    subtitleFileUrl: string;
  }): Promise<void> {
    // TODO: Implement patchVoiceTranscript when snip domain is migrated
    this.logger.warn('patchVoiceTranscript not implemented - using temporary stub');
  }

  private isMiniMaxModel(model: string) {
    return model.startsWith('speech-');
  }

  private isOpenAIModel(model: string) {
    return model.startsWith('tts-');
  }

  async generate(
    runner: LLMRunner,
    {
      user_id,
      text,
      title,
      voice,
      speed,
      vol,
      pitch,
      emotion,
      model = LLMs.SPEECH_02_HD,
    }: {
      user_id: string;
      text: string;
      title: string;
      voice: string;
      speed: string;
      vol: string;
      pitch: string;
      emotion: string;
      model?: string;
    },
  ) {
    const space = await this.spaceDomain.getByUserId(user_id);
    if (!space) {
      throw new Error('Space not found');
    }

    let provider: MinimaxLLMProvider | YLOpenAILLMProvider;
    if (this.isMiniMaxModel(model)) {
      provider = runner.getProvider(LLMProviders.MINIMAX, model as LLMs) as MinimaxLLMProvider;
    } else if (this.isOpenAIModel(model)) {
      provider = runner.getProvider(LLMProviders.OPENAI, model as LLMs) as YLOpenAILLMProvider;
    } else {
      throw new Error(`Unsupported model: ${model}`);
    }

    const audioParams = {
      voice: voice as MinimaxVoice,
      model,
      speed: Number(speed),
      vol: Number(vol),
      pitch: Number(pitch),
      emotion: emotion as MinimaxEmotion,
      subtitleEnable: false,
    };

    const audioGeneration = runner.trace.generation({
      name: 'audio-generate',
      input: audioParams,
      model,
    });
    const { audioBuffer, subtitleFile, extraInfo } = await provider.generateSpeech(
      text,
      audioParams,
    );
    audioGeneration.end({
      output: {
        subtitleFile,
        extraInfo,
      },
      usage: {
        input: 0,
        output: extraInfo?.usage_characters || 0,
        total: extraInfo?.usage_characters || 0,
      },
    });

    // Use EventBus instead of runInBackground
    this.eventBus.publish({
      type: 'CREATE_TTS_USAGE_RECORD',
      space_id: space.id,
      user_id,
      amount: extraInfo?.usage_characters || text.length,
      voice,
    });

    const hash = await this.fileDomain.uploadStringOrBuffer(audioBuffer, {
      visibility: 'public',
      contentType: 'audio/mpeg',
      directory: Directory.GEN_AUDIO,
    });

    const audio_url = `https://cdn.gooo.ai/${Directory.GEN_AUDIO}/${hash}.mp3`;

    // Generate album cover
    const imageProvider = runner.getProvider(LLMProviders.OPENAI, LLMs.GPT_IMAGE_1);
    const imageParams = {
      size: '1024x1024',
      n: 1,
      quality: 'low',
    } as const;
    const imageGeneration = runner.trace.generation({
      name: 'image-generate',
      input: imageParams,
    });
    const imageResponse = await imageProvider.generateImage(
      `Please generate an album cover for the following audio. The audio's title is: ${title}. The audio's text is: ${text}. There should be no text on the cover.`,
      imageParams,
    );
    imageGeneration.end({
      output: imageResponse,
      usage: {
        input: imageResponse?.usage?.input_tokens || 0,
        output: imageResponse?.usage?.output_tokens || 0,
        total: imageResponse?.usage?.total_tokens || 0,
      },
    });

    // Use EventBus instead of runInBackground
    this.eventBus.publish({
      type: 'CREATE_IMAGE_GENERATION_USAGE_RECORD',
      space_id: space.id,
      user_id,
      amount: imageResponse.usage?.total_tokens || 0,
    });

    // TODO: Replace with proper imageResponseToResult when available
    const { image_url } = await this.imageResponseToResult(imageResponse, false);

    return {
      hash,
      audio_url,
      subtitleFile,
      extraInfo,
      image_url,
    };
  }

  // TODO: Implement imageResponseToResult when image_generate service is available
  private async imageResponseToResult(
    _imageResponse: any,
    _blurhashEnabled: boolean = true,
  ): Promise<{ image_url: string }> {
    // Temporary implementation
    this.logger.warn('imageResponseToResult not fully implemented - using temporary stub');
    return {
      image_url: 'https://cdn.gooo.ai/temp-image.png',
    };
  }
}
