/**
 * Tool Call Service - 工具调用服务管理器
 * 管理所有工具服务的注册和调用
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/index.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { ToolCallTimeoutError } from '@/common/errors';
import { type CompletionStreamChunk, TOOL_TYPES } from '../../../common/types';
import { StreamChunkUnion } from '../ask_ai/handler';
import { AudioGenerateService } from './audio-generate.service';
import { BaseToolService } from './base.service';
import { BoardSearchService } from './board-search.service';
import { CreateBoardService } from './create-board.service';
import { CreateSnipByUrlService } from './create-snip-by-url.service';
import { DiagramGenerateService } from './diagram-generate.service';
import { EditThoughtService } from './edit-thought.service';
import { GoogleSearchService } from './google-search.service';
import { ImageGenerateService } from './image-generate.service';
import { LibrarySearchService } from './library-search.service';
import { OrganizeDirectoryStructureService } from './organize-directory-structure.service';
import type { ToolCall, ToolCallResult, ToolContext } from './types';

@Injectable()
export class ToolCallService {
  private readonly logger = new Logger(ToolCallService.name);
  private readonly toolServices = new Map<TOOL_TYPES, BaseToolService>();

  constructor(
    // All tool services are now properly converted to NestJS providers
    private readonly librarySearchService: LibrarySearchService,
    private readonly googleSearchService: GoogleSearchService,
    private readonly createBoardService: CreateBoardService,
    private readonly boardSearchService: BoardSearchService,
    private readonly organizeDirectoryStructureService: OrganizeDirectoryStructureService,
    private readonly editThoughtService: EditThoughtService,
    private readonly createSnipByUrlService: CreateSnipByUrlService,
    private readonly audioGenerateService: AudioGenerateService,
    private readonly imageGenerateService: ImageGenerateService,
    private readonly diagramGenerateService: DiagramGenerateService,
  ) {
    this.registerToolServices();
  }

  /**
   * Register all tool services
   * 注册所有工具服务
   */
  private registerToolServices(): void {
    this.toolServices.set(TOOL_TYPES.LIBRARY_SEARCH, this.librarySearchService);
    this.toolServices.set(TOOL_TYPES.GOOGLE_SEARCH, this.googleSearchService);
    this.toolServices.set(TOOL_TYPES.CREATE_BOARD, this.createBoardService);
    this.toolServices.set(TOOL_TYPES.BOARD_SEARCH, this.boardSearchService);
    this.toolServices.set(
      TOOL_TYPES.ORGANIZE_DIRECTORY_STRUCTURE,
      this.organizeDirectoryStructureService,
    );
    this.toolServices.set(TOOL_TYPES.EDIT_THOUGHT, this.editThoughtService);
    this.toolServices.set(TOOL_TYPES.CREATE_SNIP_BY_URL, this.createSnipByUrlService);
    this.toolServices.set(TOOL_TYPES.AUDIO_GENERATE, this.audioGenerateService);
    this.toolServices.set(TOOL_TYPES.IMAGE_GENERATE, this.imageGenerateService);
    this.toolServices.set(TOOL_TYPES.DIAGRAM_GENERATE, this.diagramGenerateService);

    this.logger.log(`Registered ${this.toolServices.size} tool services`);
  }

  /**
   * Get tool call object by tool type
   * 根据工具类型获取工具调用对象
   */
  async *callTool(toolName: TOOL_TYPES, toolContext: ToolContext, span: LangfuseSpanClient) {
    const service = this.toolServices.get(toolName);
    if (!service) {
      throw new Error(`Tool service ${toolName} not found`);
    }
    const fn = service.execute;
    const TOOL_EXECUTION_TIMEOUT = service.toolDefinition.execution_timeout * 1000 || 60000;
    const isGenerator = Object.getPrototypeOf(fn).constructor.name === 'AsyncGeneratorFunction';
    let result: ToolCallResult;

    // Execute the tool function and store results directly in the block
    if (isGenerator) {
      // Execute generator function and allow yielding intermediate results
      const generator = fn(toolContext, span) as AsyncGenerator<
        CompletionStreamChunk<StreamChunkUnion>,
        ToolCallResult,
        unknown
      >;

      // Forward any intermediate yields from the tool function
      let yielded: IteratorResult<CompletionStreamChunk<StreamChunkUnion>, ToolCallResult>;
      while (
        !(yielded = await Promise.race([
          generator.next(),
          new Promise<never>((_, reject) =>
            setTimeout(
              () => reject(new ToolCallTimeoutError({ tool_name: toolName })),
              TOOL_EXECUTION_TIMEOUT,
            ),
          ),
        ])).done
      ) {
        yield yielded.value as CompletionStreamChunk<StreamChunkUnion>;
      }

      // Process tool result
      result = yielded.value;
    } else {
      // Execute regular async function with timeout
      result = (await fn(toolContext, span)) as ToolCallResult;
    }

    return result;
  }

  /**
   * Get tool call object by tool type
   * 根据工具类型获取工具调用对象
   */
  getToolCall(toolType: TOOL_TYPES): ToolCall | undefined {
    const service = this.toolServices.get(toolType);
    return service?.getToolCall();
  }

  /**
   * Get all available tool calls
   * 获取所有可用的工具调用
   */
  getAllToolCalls(): Record<TOOL_TYPES, ToolCall> {
    const toolCalls = {} as Record<TOOL_TYPES, ToolCall>;

    for (const [toolType, service] of this.toolServices) {
      toolCalls[toolType] = service.getToolCall();
    }

    return toolCalls;
  }

  /**
   * Get tool map (for backward compatibility)
   * 获取工具映射（向后兼容）
   */
  getToolMap(): Partial<Record<TOOL_TYPES, ToolCall>> {
    const toolMap: Partial<Record<TOOL_TYPES, ToolCall>> = {};

    for (const [toolType, service] of this.toolServices) {
      toolMap[toolType] = service.getToolCall();
    }

    return toolMap;
  }

  /**
   * Check if a tool is available
   * 检查工具是否可用
   */
  isToolAvailable(toolType: TOOL_TYPES): boolean {
    return this.toolServices.has(toolType);
  }

  /**
   * Get available tool types
   * 获取可用的工具类型
   */
  getAvailableToolTypes(): TOOL_TYPES[] {
    return Array.from(this.toolServices.keys());
  }
}
