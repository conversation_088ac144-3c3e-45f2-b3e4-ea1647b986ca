/**
 * Board Search Service - 板块搜索服务
 * 处理板块搜索相关的工具调用
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/board_search.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { z } from 'zod';
import {
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  MessageAtReferenceTypeEnum,
  MessageRoleEnum,
  TOOL_TYPES,
} from '@/common/types';
import type { MessageAnalyzedContextType } from '@/domain/chat/context/types';
import {
  convertToContextString,
  convertToSearchResults,
} from '@/domain/chat/util/convertToSearchResult';
import { StreamChunkUnion } from '../ask_ai/handler';
import { ContextBuilder } from '../context';
import { getCurrentBoardIdByChat } from '../util/isNewBoardChat';
import { BaseToolService } from './base.service';
import type { ToolDefinition, ToolFunctionParameters } from './types';

export const tool_name: TOOL_TYPES = TOOL_TYPES.BOARD_SEARCH;
export const tool_output_schema = z.object({
  query: z.string(),
});

export const tool_definition = {
  max_calls: 3,
  type: 'function',
  function: {
    name: 'board_search',
    description: `
Use this tool only if the user's request can be answered with content already present in the current board in by board directory structure,
or users' query explicitly mentions the board (e.g. "@[@BOARD_NAME](id:BOARD_ID;type:board)") or board group (e.g. "@[@BOARD_GROUP_NAME](id:BOARD_GROUP_ID;type:board_group)")
and you need extra information from those boards or board groups to fulfill the user's request.

1. Check the board directory first.
- If none of its \`title\` or \`entity_type\` values are topically related to the user's prompt, do not call the tool.
- If at least one item is relevant, build the \`parameters\` object as described below.

2. \`contextType\` rules
- "relevant" – The user asks for a specific fact, explanation, or narrow slice of information.
- "full" – The user wants a broad survey or summary of an entire board item.

3. \`query\` construction
- For "relevant": distill the user prompt into 3-8 core keywords/phrases; resolve pronouns/back-references; replace relative dates ("yesterday") with concrete dates, match the date time format of the board item if applicable.
- For "full": leave \`query\` empty ("")`,
    parameters: {
      type: 'object',
      properties: {
        contextType: {
          type: 'string',
          enum: ['full', 'relevant'],
          description:
            "Choose 'full' for a broad overview of a board item or the entire board; choose 'relevant' when only a focused excerpt is needed.",
        },
        query: {
          type: 'string',
          description:
            "Semantic search string. Required for 'relevant'; leave empty ('') when contextType is 'full'.",
        },
      },
      required: ['contextType', 'query'],
      additionalProperties: false,
    },
  },
} as ToolDefinition;

@Injectable()
export class BoardSearchService extends BaseToolService {
  private readonly logger = new Logger(BoardSearchService.name);

  readonly toolName: TOOL_TYPES = tool_name;
  readonly toolOutputSchema = tool_output_schema;
  readonly toolDefinition: ToolDefinition = tool_definition;

  constructor(private readonly contextBuilder: ContextBuilder) {
    super();
  }

  /**
   * Board search tool function
   * 板块搜索工具函数
   */
  async *execute(parameters: ToolFunctionParameters, span: LangfuseSpanClient) {
    const { user_id, chat, parsed_params, completion_block } = parameters;
    const { query, contextType } = parsed_params;

    const lastUserMessage = chat.messages.findLast((m) => m.role === MessageRoleEnum.USER);
    const atReferences = lastUserMessage?.at_references;
    if (!atReferences) {
      return { response: 'No context needed' };
    }

    let targetBoardIds: string[] = atReferences
      .filter((atReference) => atReference.entity_type === MessageAtReferenceTypeEnum.BOARD)
      .map((atReference) => atReference.entity_id);

    const targetBoardGroupIds: string[] = atReferences
      .filter((atReference) => atReference.entity_type === MessageAtReferenceTypeEnum.BOARD_GROUP)
      .map((atReference) => atReference.entity_id);

    if (targetBoardIds.length === 0) {
      let boardId = chat.board_id;
      if (!boardId) {
        boardId = await getCurrentBoardIdByChat(chat);
      }

      // 如果仍然没有找到 board_id，返回错误信息
      if (!boardId) {
        return {
          response: 'No board context available - this chat is not associated with any board',
        };
      }

      targetBoardIds = [boardId];
    }

    const hasOtherBoards = targetBoardIds.filter((boardId) => boardId !== chat.board_id).length > 0;
    const hasOtherBoardGroups = targetBoardGroupIds.length > 0;
    const searchCopySuffix = hasOtherBoards || hasOtherBoardGroups ? 'in YouMind' : 'this board';

    const finalAtReferences: Array<{
      at_name: string;
      entity_type: MessageAtReferenceTypeEnum;
      entity_id: string;
    }> = targetBoardIds.map((boardId) => ({
      at_name: 'board',
      entity_type: MessageAtReferenceTypeEnum.BOARD,
      entity_id: boardId,
    }));

    if (targetBoardGroupIds.length > 0) {
      finalAtReferences.push(
        ...targetBoardGroupIds.map((boardGroupId) => ({
          at_name: 'board_group',
          entity_type: MessageAtReferenceTypeEnum.BOARD_GROUP,
          entity_id: boardGroupId,
        })),
      );
    }

    const availableTokens = 40000;

    const analysis = {
      needsContext: true,
      contextType: contextType as MessageAnalyzedContextType,
      searchKeywords: query,
    };

    yield {
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completion_block.id,
      data: `Searching ${searchCopySuffix}`,
      path: 'tool_result.statusMessage',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>;

    const contextResult = await this.contextBuilder.buildContextFromReferences({
      messages: chat.messages || [],
      at_references: finalAtReferences,
      availableTokens,
      userId: user_id,
      trace: span,
      analysis,
    });

    const searchResults = convertToSearchResults(contextResult);

    let result: { statusMessage: string }, response: string;
    if (searchResults.length > 0) {
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: completion_block.id,
        data: searchResults,
        path: 'tool_result.results',
      } as CompletionStreamReplaceChunk<StreamChunkUnion>;

      result = {
        statusMessage: `Searched ${searchCopySuffix}`,
      };
      response = `Following chunks are retrieved from ${searchCopySuffix}: \n\n ${convertToContextString(contextResult)}`;
    } else {
      result = {
        statusMessage: `No related content found ${searchCopySuffix}`,
      };
      response = `No related content found ${searchCopySuffix}`;
    }

    return {
      result: {
        statusMessage: result.statusMessage,
        results: searchResults,
      },
      response,
    };
  }
}
