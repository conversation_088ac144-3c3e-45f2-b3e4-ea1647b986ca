import { Injectable } from '@nestjs/common';
import {
  Chat<PERSON>ode<PERSON>num,
  type Chat<PERSON><PERSON><PERSON>,
  type Chat<PERSON>riginType<PERSON>num,
  type ChatWebpage<PERSON>rigin,
  CompletionBlockTypeEnum,
  DEFAULT_AI_CHAT_MODEL,
  type ListHistoryForChatAssistantParam,
  MessageContextEnum,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
} from '../../common/types';
import { SafeParse } from '../../common/utils';
import { replaceAtReference } from '../../common/utils/extractAtReference';
import { ChatDAO, MessageDAO } from '../../dao/chat';
import { ChatDO, CompletionBlockDO, MessageDO } from '../../dao/chat/types';
import { convertToolCallResultToMessageEvent } from './tool_call/utils/compatibility';
import {
  AssistantMessage,
  Chat,
  ChatDetail,
  CompletionBlock,
  CompletionContentBlock,
  CompletionReasoningBlock,
  CompletionToolBlock,
  ListByUserParam,
  Message,
  QueryByOriginParam,
  ToolCompletionBlock,
  UserMessage,
} from './types';

@Injectable()
export class ChatQueryDomainService {
  constructor(
    private readonly chatDAO: ChatDAO,
    private readonly messageDAO: MessageDAO,
  ) {}
  public toChat(data: ChatDO | null): Chat | null {
    if (!data) return null;

    return {
      id: data.id,
      creator_id: data.creatorId,
      created_at: data.createdAt,
      updated_at: data.updatedAt,
      title: replaceAtReference(data.title || '', (name) => `@"${name}"`),
      origin: {
        type: data.originType as ChatOriginTypeEnum,
        id: data.originId || '',
        url: data.originUrl || '',
      } as unknown as ChatOrigin,
      board_id: data.boardId || '',
      mode: data.mode as ChatModeEnum,
      show_new_board_suggestion: data.context
        ? SafeParse(data.context, true, { show_new_board_suggestion: false })
            .show_new_board_suggestion
        : false,
    };
  }

  public toMessage(data: MessageDO | null, chat: Chat): Message | null {
    if (!data) return null;

    const origin = data.context?.find((c) => c.type === MessageContextEnum.ORIGIN)?.origin;
    if (origin) {
      delete (origin as ChatWebpageOrigin).content;
    }

    const reasoning_block = data.blocks?.find((b) => b.type === CompletionBlockTypeEnum.REASONING);
    // legacy data
    const reasoning_metadata = data.context?.find(
      (c) => c.type === MessageContextEnum.REASONING_METADATA,
    );

    const tool_blocks = data.blocks?.filter((b) => b.type === CompletionBlockTypeEnum.TOOL) || [];
    // legacy data
    const events = (data.context || [])
      ?.filter((c) => c.type === MessageContextEnum.EVENT)
      .map((c) => c.event);

    // Create base message properties
    const baseMessage = {
      id: data.id,
      chat_id: data.chatId,
      created_at: data.createdAt,
      updated_at: data.updatedAt,
      status: data.status as MessageStatusEnum,
    };

    if (data.role === MessageRoleEnum.USER) {
      return {
        ...baseMessage,
        role: MessageRoleEnum.USER,
        origin: origin || chat.origin,
        content: data.content || '',
        selection: data.context?.find((c) => c.type === MessageContextEnum.SELECTION)?.text || '',
        at_references:
          data.context?.find((c) => c.type === MessageContextEnum.AT)?.at_references || [],
        board_id: data.context?.find((c) => c.type === MessageContextEnum.BOARD_ID)?.board_id || '',
        tools: data.tools || {},
        mode: data.mode ?? MessageModeEnum.ASK,
        command: data.command ?? undefined,
        shortcut: data.shortcut ?? undefined,
      } as UserMessage;
    } else {
      const content =
        data.content ||
        data.blocks
          ?.map((b) => {
            if (b.type === CompletionBlockTypeEnum.CONTENT) {
              return b.data;
            } else if (
              b.type === CompletionBlockTypeEnum.TOOL &&
              (b.extra as { error: string })?.error
            ) {
              return b.toolResponse;
            }
            return '';
          })
          .join('\n') ||
        '';
      return {
        ...baseMessage,
        role: MessageRoleEnum.ASSISTANT,
        model:
          data.model ||
          data.context?.find((c) => c.type === MessageContextEnum.MODEL)?.model ||
          DEFAULT_AI_CHAT_MODEL,
        error: (data.error as object) || null,
        trace_id: data.traceId || '',
        blocks: (data.blocks?.map((b) => this.toCompletionBlock(b)) || []) as CompletionBlock[],
        events: tool_blocks.length
          ? tool_blocks.map((b) => convertToolCallResultToMessageEvent(b as ToolCompletionBlock))
          : events || [],
        content,
        reasoning: data.reasoning || reasoning_block?.data || '',
        ...(reasoning_block
          ? {
              reasoning_begin_at: reasoning_block.createdAt
                ? reasoning_block.createdAt.toISOString()
                : '',
              reasoning_end_at: reasoning_block.updatedAt
                ? reasoning_block.updatedAt.toISOString()
                : '',
            }
          : reasoning_metadata
            ? {
                reasoning_begin_at: reasoning_metadata.begin_at,
                reasoning_end_at: reasoning_metadata.end_at,
              }
            : {}),
      } as AssistantMessage;
    }
  }

  public toCompletionBlock(data: CompletionBlockDO | null): CompletionBlock | null {
    if (!data) return null;
    const baseBlock = {
      id: data.id,
      created_at: data.createdAt,
      updated_at: data.updatedAt,
      type: data.type,
      status: data.status,
      message_id: data.messageId,
      extra: data.extra || {},
    };

    switch (data.type) {
      case CompletionBlockTypeEnum.CONTENT:
        return {
          ...baseBlock,
          data: data.data || '',
        } as CompletionContentBlock;
      case CompletionBlockTypeEnum.REASONING:
        return {
          ...baseBlock,
          data: data.data || '',
        } as CompletionReasoningBlock;
      case CompletionBlockTypeEnum.TOOL:
        return {
          ...baseBlock,
          tool_id: data.toolId || '',
          tool_name: data.toolName || '',
          tool_arguments: data.toolArguments || {},
          tool_result: data.toolResult || {},
          tool_response: data.toolResponse || '',
        } as CompletionToolBlock;
      default:
        return baseBlock as CompletionBlock;
    }
  }

  public toChatDetail(data: ChatDO | null): ChatDetail | null {
    const chat = this.toChat(data);
    if (!chat) return null;

    // ChatDO doesn't have messages property, so we return empty array for now
    // In a real implementation, you would need to fetch messages separately
    const messages: Message[] = [];
    return {
      ...chat,
      messages,
    };
  }

  public toMessageContent(m?: Message) {
    if (!m) return '';
    return (m as UserMessage).selection
      ? `${m.content} \n> ${(m as UserMessage).selection}`
      : m.content;
  }

  async getById(id: string) {
    const chat = await this.chatDAO.selectOneById(id);
    return this.toChat(chat);
  }

  async getDetailById(id: string) {
    const chat = await this.chatDAO.selectDetailById(id);
    return this.toChatDetail(chat);
  }

  async getMessageById(id: string) {
    const message = await this.messageDAO.selectOneById(id);
    if (!message) return null;
    const chat = await this.chatDAO.selectOneById(message.chatId);
    return this.toMessage(message, this.toChat(chat) as Chat);
  }

  async listValidMessagesById(param: {
    chat_id: string;
    roles?: MessageRoleEnum[];
    order?: 'asc' | 'desc';
    max_messages?: number;
    statuses?: MessageStatusEnum[];
  }): Promise<Message[]> {
    const {
      chat_id: id,
      roles = [MessageRoleEnum.USER, MessageRoleEnum.ASSISTANT],
      order = 'desc',
      statuses = [MessageStatusEnum.DONE, MessageStatusEnum.QUEUED, MessageStatusEnum.ING],
    } = param || {};

    const chat = await this.getById(id);
    if (!chat) return [];

    const messages = await this.chatDAO.selectValidMessagesById(id, {
      roles,
      order,
      statuses,
    });
    if (!messages?.length) return [];

    if (param?.max_messages) {
      return messages.slice(0, param.max_messages).map((m) => this.toMessage(m, chat)) as Message[];
    }

    return messages.map((m) => this.toMessage(m, chat)) as Message[];
  }

  async listByUser(param: ListByUserParam) {
    const { query, user_id } = param;
    // Convert PagingParam to the expected format
    const convertedQuery = {
      limit: query.pageSize,
      offset: query.current ? (query.current - 1) * (query.pageSize || 20) : 0,
      order: 'desc' as const,
    };
    const result = await this.chatDAO.listByUserId(user_id, convertedQuery);
    return {
      ...result,
      data: result.data.map((r) => this.toChat(r)),
    };
  }

  async listByUserUnsorted(param: ListByUserParam) {
    const { query, user_id } = param;
    // Convert PagingParam to the expected format
    const convertedQuery = {
      limit: query.pageSize,
      offset: query.current ? (query.current - 1) * (query.pageSize || 20) : 0,
    };
    const result = await this.chatDAO.listByUserIdAndUnsorted(user_id, convertedQuery);
    return {
      ...result,
      data: result.data.map((r) => this.toChatDetail(r)),
    };
  }

  async listForChatAssistant(param: ListHistoryForChatAssistantParam) {
    const { query, user_id, board_id, origin } = param;
    const result = await this.chatDAO.listForChatAssistant(user_id, param, board_id, origin, query);
    return {
      ...result,
      data: result.data.map((r) => this.toChat(r)),
    };
  }

  async queryByIds(ids: string[]) {
    const chatDOs = await this.chatDAO.selectDetailByIds(ids);
    return chatDOs.map((r) => this.toChatDetail(r));
  }

  async queryDetailsByOrigin(param: QueryByOriginParam) {
    const { origin, user_id } = param;
    const chats = await this.chatDAO.selectDetailsByUserAndOrigin(user_id, origin, [
      ChatModeEnum.CHAT,
      ChatModeEnum.NEW_BOARD,
    ]);
    return chats.map((r) => this.toChatDetail(r));
  }

  async getDetailByBoardId(param: { user_id: string; board_id: string }) {
    const { user_id, board_id } = param;
    const chats = await this.chatDAO.selectDetailsForChatAssistant({
      user_id,
      chat_id: board_id, // Using board_id as chat_id for this query
    });
    if (chats.length) {
      return this.toChatDetail(chats[0]);
    }
    return null;
  }
}
