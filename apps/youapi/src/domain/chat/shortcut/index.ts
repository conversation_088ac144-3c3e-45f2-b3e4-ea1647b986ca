import { Injectable } from '@nestjs/common';
import { YouapiClsService } from '@/common/services/cls.service';
import type { PagingResponse } from '@/common/types';
import { AssistantTypeEnum, AssistantVisibilityEnum } from '@/common/types';
import { rankBetween } from '@/common/utils';
import { Assistant<PERSON>O } from '@/dao/assistant';
import { AssistantDO, InsertAssistantDOParam, UpdateAssistantDOParam } from '@/dao/assistant/types';
import { SpaceAssistantConfigDAO } from '@/dao/space-assistant-config';
import { SpaceAssistantConfigDO } from '@/dao/space-assistant-config/types';

// import type { Shortcut } from '@/lib/app/shortcut/types';
type Shortcut = unknown;

/**
 * 快捷指令领域服务
 */
@Injectable()
class ShortcutDomainService {
  constructor(
    private readonly youbizClsService: YouapiClsService,
    private readonly assistantDAO: AssistantDAO,
    private readonly spaceAssistantConfigDAO: SpaceAssistantConfigDAO,
  ) {}
  /**
   * 列出空间的所有的快捷指令
   */
  async listShortcuts(creator_id: string): Promise<PagingResponse<Shortcut>> {
    const shortcutDOs = await this.assistantDAO.selectByCreatorId(creator_id);
    const shortcuts = shortcutDOs.map((it) => this.assistantDO2entity(it));
    return {
      data: shortcuts,
      paging: {
        total: shortcuts.length,
        current: 0,
        pageSize: shortcuts.length,
      },
    };
  }

  async createShortcut(param: {
    name: string;
    prompt: string;
    space_id: string;
    creator_id: string;
  }): Promise<Shortcut> {
    const { space_id } = param;
    // 创建快捷指令
    const assistantDO = await this.assistantDAO.insert({
      ...param,
      type: AssistantTypeEnum.PROMPT_EXECUTER,
      visibility: AssistantVisibilityEnum.PUBLIC,
      description: '',
      instructions: param.prompt,
    } as InsertAssistantDOParam);

    // 创建空间配置
    const first = await this.spaceAssistantConfigDAO.trySelectFirst(space_id);
    const rank = rankBetween(undefined, first?.rank);
    await this.spaceAssistantConfigDAO.insert({
      assistant_id: assistantDO.id,
      space_id,
      enabled: true,
      rank,
    } as SpaceAssistantConfigDO);

    return this.assistantDO2entity({
      ...assistantDO,
      rank,
    });
  }

  async getById(id: string): Promise<Shortcut> {
    const assistantDO = await this.assistantDAO.selectById(id);
    const spaceId = await this.youbizClsService.getSpaceId();
    const spaceAssistantConfigDO = await this.spaceAssistantConfigDAO.select(spaceId, id);
    return this.assistantDO2entity({
      ...assistantDO,
      rank: spaceAssistantConfigDO?.rank || null,
    });
  }

  /**
   * 更新 Prompt 与名称
   */
  async patchShortcut(param: { id: string; name: string; prompt: string }): Promise<Shortcut> {
    // 更新快捷指令信息
    const assistantDO = await this.assistantDAO.update(param.id, {
      name: param.name,
      instructions: param.prompt,
    } as UpdateAssistantDOParam);
    const spaceId = await this.youbizClsService.getSpaceId();
    const spaceAssistantConfigDO = await this.spaceAssistantConfigDAO.select(spaceId, param.id);
    return this.assistantDO2entity({
      ...assistantDO,
      rank: spaceAssistantConfigDO?.rank || null,
    });
  }

  /**
   * 删除快捷指令
   */
  async deleteShortcut(shortcut_id: string) {
    // 在所有空间卸载快捷指令
    await this.spaceAssistantConfigDAO.deleteByAssistantId(shortcut_id);
    // 删除快捷指令本身
    await this.assistantDAO.delete(shortcut_id);
  }

  private async getRankRange(
    space_id: string,
    rank_after: string | 'first' | 'last',
  ): Promise<[string | undefined, string | undefined]> {
    if (rank_after === 'first') {
      const first = await this.spaceAssistantConfigDAO.trySelectFirst(space_id);
      return [undefined, first?.rank];
    } else if (rank_after === 'last') {
      const last = await this.spaceAssistantConfigDAO.trySelectLast(space_id);
      return [last?.rank, undefined];
    } else {
      const target = await this.spaceAssistantConfigDAO.select(space_id, rank_after);
      if (!target) {
        throw new Error('Assistant config not found');
      }
      const next = await this.spaceAssistantConfigDAO.trySelectNextRank(space_id, target.rank);
      return [target.rank, next?.rank];
    }
  }

  /**
   * 调整快捷指令的排序
   */
  async move(param: {
    user_id: string;
    space_id: string;
    shortcut_id: string;
    rank_after_id: string;
  }): Promise<void> {
    const { space_id, shortcut_id, rank_after_id } = param;

    // 获取rank范围
    const [beforeRank, afterRank] = await this.getRankRange(space_id, rank_after_id || 'first');

    // 生成新的rank值
    const newRank = rankBetween(beforeRank, afterRank);

    // 更新快捷指令配置的rank
    await this.spaceAssistantConfigDAO.update(space_id, shortcut_id, {
      rank: newRank,
    });
  }

  private assistantDO2entity(assistantDO: AssistantDO & { rank: string | null }): Shortcut {
    return {
      id: assistantDO.id,
      created_at: assistantDO.created_at,
      updated_at: assistantDO.updated_at,
      creator_id: assistantDO.creator_id,
      name: assistantDO.name,
      prompt: assistantDO.instructions || '',
      rank: assistantDO.rank || '',
    };
  }
}

export { ShortcutDomainService };
