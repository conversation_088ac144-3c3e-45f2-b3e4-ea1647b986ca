import { Injectable, Logger } from '@nestjs/common';
import { ChatPromptClient } from 'langfuse-core';
import type { CompletionStreamChunk } from '@/common/types';
import {
  type AssistantSourceTypeEnum,
  type ChatBoardOrigin,
  ChatOriginTypeEnum,
  type ChatSnipOrigin,
  type ChatThoughtOrigin,
  type ChatWebpageOrigin,
  DEFAULT_AI_CHAT_MODEL,
  DEFAULT_TEMPERATURE,
  type LLMs,
  MessageRoleEnum,
  type TOOL_TYPES,
} from '@/common/types';
import { AssistantDO } from '@/dao/assistant/types';
import { CompletionBlockDAO, MessageDAO } from '@/dao/chat';
import {
  PromptName,
  type YLChatRequestAssistantMessage,
  type YLChatRequestToolMessage,
  type YLChatStreamCompletion,
} from '../../../infra/youllm';
import { ChatLLMRunner } from '../../../infra/youllm/llm_service/runner';
import { YouLLMService } from '../../llm';
import { ContextManager } from '../../llm/context';
import { type StreamChunkUnion, ToolCallChatStreamHandler } from '../ask_ai/handler';
import { ChatQueryDomainService } from '../query';
import { ToolCallService } from '../tool_call';
import type { ToolDefinition } from '../tool_call/types';
import type { AssistantMessage, ChatDetail, CompletionBlock } from '../types';
import { blocksToLLMMessages } from '../util/blocksToLLMMessages';

@Injectable()
export class AssistantChatDomainService {
  private readonly logger = new Logger(AssistantChatDomainService.name);

  constructor(
    private readonly youllmService: YouLLMService,
    private readonly contextManager: ContextManager,
    private readonly chatQueryDomain: ChatQueryDomainService,
    private readonly toolCallService: ToolCallService,
    private readonly messageDAO: MessageDAO,
    private readonly completionBlockDAO: CompletionBlockDAO,
  ) {}

  /**
   * Get tool definitions based on assistant configuration
   * 根据助手配置获取工具定义
   */
  protected getToolDefinitions(assistant: Partial<AssistantDO>): ToolDefinition[] {
    const enabledTools = Object.entries(assistant.tools || {})
      .filter(
        ([key, enabled]) => enabled && this.toolCallService.isToolAvailable(key as TOOL_TYPES),
      )
      .map(([key]) => key as TOOL_TYPES);

    return enabledTools
      .map((toolType) => this.toolCallService.getToolCall(toolType)?.tool_definition)
      .filter(Boolean) as ToolDefinition[];
  }

  protected getTraceArgs(chat: ChatDetail, assistant: Partial<AssistantDO>) {
    return {
      sessionId: chat.id,
      metadata: {
        chatId: chat.id,
        assistantId: assistant.id,
        assistantName: assistant.name,
        ...(chat.origin?.type === ChatOriginTypeEnum.BOARD && {
          boardId: (chat.origin as ChatBoardOrigin)?.id,
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.SNIP && {
          snipId: (chat.origin as ChatSnipOrigin)?.id,
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.THOUGHT && {
          thoughtId: (chat.origin as ChatThoughtOrigin)?.id,
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.WEBPAGE && {
          url: (chat.origin as ChatWebpageOrigin)?.url,
        }),
      },
    };
  }

  async *generate(args: {
    chat: ChatDetail;
    runner: ChatLLMRunner;
    assistant: Partial<AssistantDO>;
  }): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    const chat = args.chat;
    const { runner, assistant } = args;
    const {
      model = DEFAULT_AI_CHAT_MODEL,
      temperature = DEFAULT_TEMPERATURE,
      instructions = '',
    } = assistant;

    try {
      // Get tool definitions with proper error handling
      const tools = this.getToolDefinitions(assistant);

      // Create handler with proper parameters following new_board.ts pattern
      const handler = new ToolCallChatStreamHandler(
        chat,
        runner,
        this.messageDAO,
        this.completionBlockDAO,
        this.chatQueryDomain,
        this.toolCallService,
        5, // max_call_count
        tools,
      );

      // Build assistant context
      const { context_string } = await runner.runSpan({
        name: 'build-assistant-context',
        fn: (span) => {
          return this.contextManager.buildAssistantContext(
            {
              entity_id: (chat.origin as ChatBoardOrigin).id,
              entity_type: chat.origin.type as unknown as AssistantSourceTypeEnum,
            },
            {
              span,
              model: model as LLMs,
              instructions: instructions || '',
            },
          );
        },
      });

      // Fetch prompt
      const prompt = (await runner.runSpan({
        name: 'fetch-prompt',
        fn: async (span) =>
          runner.prompt_manager.fetchPrompt(
            {
              name: PromptName.CustomAssistantPrompt2,
              prefer: 'local',
            },
            span,
            true,
          ),
      })) as ChatPromptClient;

      // Find assistant message index
      const index = chat.messages.findLastIndex((m) => m.role === MessageRoleEnum.ASSISTANT);

      if (index === -1) {
        throw new Error('No assistant message found in chat');
      }

      yield* handler.handleStream(async (blocks: CompletionBlock[]) => {
        // Update chat messages with blocks
        (chat.messages[index] as AssistantMessage).blocks = blocks;

        // Build messages array
        const messages = [
          ...(prompt.compile({
            instructions: assistant.instructions!,
            context: context_string,
          }) as Array<YLChatRequestAssistantMessage | YLChatRequestToolMessage>),
          ...(blocksToLLMMessages(blocks, runner.model_name, true) as Array<
            YLChatRequestAssistantMessage | YLChatRequestToolMessage
          >),
        ];

        return this.youllmService.streamChatCompletionWithMessages(prompt, {
          runner,
          traceArgs: this.getTraceArgs(chat, assistant),
          useCache: false,
          messages,
          modelOptions: {
            model: model as LLMs,
            temperature: temperature || DEFAULT_TEMPERATURE,
            tool_choice: 'auto',
            tools,
          },
        }) as unknown as AsyncIterable<YLChatStreamCompletion>;
      });

      // Cleanup with proper error handling
      await runner.cleanup();
    } catch (error) {
      this.logger.error('Error in assistant chat generation:', error);
      throw error;
    }
  }
}
