/**
 * Chat V2 Domain Service - 聊天V2领域服务
 * 处理聊天相关的业务逻辑
 *
 * Migrated from:
 * - youapp/src/domain/chat/v2.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { omit } from 'lodash';
import { RestError } from '@/common/errors';
import {
  type CompletionStreamChunk,
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  MessageRoleEnum,
  MessageStatusEnum,
} from '@/common/types';
import { AssistantDO } from '@/dao/assistant/types';
import { MessageDAO } from '@/dao/chat';
import { RedisService } from '@/infra/redis';
import { ChatLLMRunner } from '@/infra/youllm/llm_service/runner';
import { ChatTitleGenerationEvent } from '../events/chat.events';
import { NewBoardWorkflowService } from './agent/new_board';
import { AskAIService } from './ask_ai';
import { StreamChunkUnion } from './ask_ai/handler';
import { AssistantChatDomainService } from './assistant';
import type { AssistantMessage, ChatDetail, UserMessage } from './types';

@Injectable()
export class ChatV2DomainService {
  private readonly logger = new Logger(ChatV2DomainService.name);

  constructor(
    private readonly eventBus: EventBus,
    private readonly messageDAO: MessageDAO,
    private readonly askAIService: AskAIService,
    private readonly newBoardWorkflowService: NewBoardWorkflowService,
    private readonly assistantChatDomainService: AssistantChatDomainService,
    private readonly redisService: RedisService,
  ) {}

  private validateChat(chat: ChatDetail) {
    const assistantMessage = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.ASSISTANT,
    ) as AssistantMessage;
    if (!assistantMessage) throw new Error('Invalid chat, missing assistant message');

    const userMessage = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.USER,
    ) as UserMessage;
    if (!userMessage || (!userMessage.content && !userMessage.command && !userMessage.shortcut))
      throw new Error('Invalid chat, missing user message');
  }

  private async *patchTraceId(param: { chat: ChatDetail; runner: ChatLLMRunner }) {
    const { chat, runner } = param;
    const last_assistant_message = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.ASSISTANT,
    ) as AssistantMessage;

    if (last_assistant_message) {
      last_assistant_message.trace_id = runner.trace.id;
      await this.messageDAO.updateTraceId(
        last_assistant_message.id,
        last_assistant_message.trace_id,
      );
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'Message',
        targetId: last_assistant_message.id,
        path: 'trace_id',
        data: last_assistant_message.trace_id,
      } as CompletionStreamReplaceChunk<string>;
    }
  }

  async *chat(param: {
    chat: ChatDetail;
    signal: AbortSignal;
    regenerate?: boolean;
  }): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    const { chat, signal, regenerate = false } = param;

    this.validateChat(chat);
    const assistantMessage = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.ASSISTANT,
    ) as AssistantMessage;
    const userMessage = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.USER,
    ) as UserMessage;

    // update title in background using event bus
    this.eventBus.publish(new ChatTitleGenerationEvent({ chat }));

    // receive streaming messages from assistant
    try {
      const response = await this.askAIService.respond({
        chat,
        regenerate,
        mode: userMessage.mode,
      });
      for await (const message of response) {
        if (signal.aborted) {
          this.logger.log('aborted by signal');
          assistantMessage.status = MessageStatusEnum.ABORT;
          await this.messageDAO.updateStatus(assistantMessage.id, MessageStatusEnum.ABORT);
          return;
        }
        yield message;
      }
    } catch (error) {
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'Message',
        targetId: assistantMessage.id,
        path: 'error',
        data:
          error instanceof RestError
            ? error.json('')
            : {
                code: (error as Error).name,
                status: 400,
                message: (error as Error).message,
              },
      };
      this.logger.error('AskAI Error', {
        chat: omit(chat, ['messages']),
        message: error,
      });
      await this.messageDAO.updateStatus(assistantMessage.id, MessageStatusEnum.ERROR);
    }
  }

  async *runNewBoardWorkflow(param: {
    chat: ChatDetail;
    signal: AbortSignal;
  }): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    const { chat } = param;

    this.validateChat(chat);
    const assistantMessage = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.ASSISTANT,
    ) as AssistantMessage;
    const runner = new ChatLLMRunner({
      name: 'custom-assistant',
    });

    try {
      yield* this.patchTraceId({ chat, runner });
      yield* this.newBoardWorkflowService.generate({
        chat,
        runner,
      });
    } catch (error) {
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'Message',
        targetId: assistantMessage.id,
        path: 'error',
        data:
          error instanceof RestError
            ? error.json('')
            : {
                code: (error as Error).name,
                status: 400,
                message: (error as Error).message,
              },
      };
      this.logger.error('New Board Workflow Error', {
        chat: omit(chat, ['messages']),
        message: error,
      });
      await this.messageDAO.updateStatus(assistantMessage.id, MessageStatusEnum.ERROR);
    }
  }

  async *runAssistant(param: {
    chat: ChatDetail;
    assistant: Partial<AssistantDO>;
  }): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    const { chat, assistant } = param;

    this.validateChat(chat);
    const assistantMessage = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.ASSISTANT,
    ) as AssistantMessage;
    const runner = new ChatLLMRunner({
      name: 'custom-assistant',
    });

    try {
      yield* this.patchTraceId({ chat, runner });
      yield* this.assistantChatDomainService.generate({
        chat,
        runner,
        assistant,
      });
    } catch (error) {
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'Message',
        targetId: assistantMessage.id,
        path: 'error',
        data:
          error instanceof RestError
            ? error.json('')
            : {
                code: (error as Error).name,
                status: 400,
                message: (error as Error).message,
              },
      };
      this.logger.error('Run Assistant Error', {
        chat: omit(chat, ['messages']),
        message: error,
      });
      await this.messageDAO.updateStatus(assistantMessage.id, MessageStatusEnum.ERROR);
    }
  }
}
