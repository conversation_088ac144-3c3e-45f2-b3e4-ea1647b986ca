/**
 * Chat Event Handler - 聊天事件处理器
 * 处理聊天相关事件的异步处理逻辑
 *
 * Migrated from:
 * - youapp/src/lib/domain/chat/index.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventsHandler, type IEventHandler } from '@nestjs/cqrs';
import { ChatDAO } from '../../../dao/chat';
import { ChatEssenceGenerationEvent, ChatTitleGenerationEvent } from '../../events/chat.events';
import { AskAIService } from '../ask_ai';

@EventsHandler(ChatTitleGenerationEvent)
@Injectable()
export class ChatTitleGenerationEventHandler implements IEventHandler<ChatTitleGenerationEvent> {
  private readonly logger = new Logger(ChatTitleGenerationEventHandler.name);

  constructor(
    private readonly askAIService: AskAIService,
    private readonly chatDAO: ChatDAO,
  ) {}

  async handle(event: ChatTitleGenerationEvent) {
    try {
      const { chat } = event.payload;

      // Check if title generation is needed
      const userMessages = chat.messages.filter((m) => m.role === 'user');
      const generate_by_first_message =
        chat.origin?.type !== 'webpage' && userMessages.length === 1;
      const generate_by_interval =
        userMessages.length % 5 === 0 && chat.created_at.getTime() === chat.updated_at.getTime();

      if (generate_by_first_message || generate_by_interval) {
        const title = await this.askAIService.generateTitle({ chat });
        await this.chatDAO.updateTitle(chat.id, title);

        this.logger.debug(`Successfully generated title for chat: ${chat.id} -> ${title}`);
      }
    } catch (error) {
      this.logger.error(`Failed to generate title for chat: ${event.payload.chat.id}`, error);
    }
  }
}

@EventsHandler(ChatEssenceGenerationEvent)
@Injectable()
export class ChatEssenceGenerationEventHandler
  implements IEventHandler<ChatEssenceGenerationEvent>
{
  private readonly logger = new Logger(ChatEssenceGenerationEventHandler.name);

  constructor(
    private readonly askAIService: AskAIService,
    private readonly chatDAO: ChatDAO,
  ) {}

  async handle(event: ChatEssenceGenerationEvent) {
    try {
      const { chatId, regenerate = false } = event.payload;

      // Generate essence using the AI service
      const essenceStream = await this.askAIService.generateEssence({
        chat_id: chatId,
        regenerate,
      });

      // Process the essence stream and extract the final essence
      let essence = '';
      for await (const message of essenceStream) {
        const content = message?.choices?.[0]?.delta?.content || '';
        if (content) {
          essence += content;
        }
      }

      // Update the chat with the generated essence
      await this.chatDAO.updateEssence(chatId, essence, new Date(), 'generated-trace-id');

      this.logger.debug(`Successfully generated essence for chat: ${chatId}`);
    } catch (error) {
      this.logger.error(`Failed to generate essence for chat: ${event.payload.chatId}`, error);
    }
  }
}
