/**
 * Chat Ask Service - 聊天问答服务
 * 处理基础聊天AI对话功能
 *
 * Migrated from:
 * - youapp/src/domain/chat/ask_ai/ask.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { ChatPromptClient } from 'langfuse-core';
import {
  type ChatBoardOrigin,
  ChatOriginTypeEnum,
  type ChatSnipOrigin,
  type ChatThoughtOrigin,
  type ChatWebpageOrigin,
  CompletionBlockTypeEnum,
  type LLMs,
  MessageRoleEnum,
  TOOL_TYPES,
} from '../../../common/types';
import { CompletionBlockDAO, MessageDAO } from '../../../dao/chat';
import { PromptName } from '../../../infra/youllm/llm_service/langfuse.service';
import { LLMRunner } from '../../../infra/youllm/llm_service/runner';
import { WriterPromptService } from '../../../infra/youllm/prompt/writer-prompt.service';

import { BoardDomainService } from '../../board';
import { BoardItemTypeEnum } from '../../board-item/types';
import { YouLLMService } from '../../llm';
import { WebpageContextBuilder } from '../../llm/context/webpage';
import { UserDomainService } from '../../user';
import { ChatQueryDomainService } from '../query';
import { ToolCallService } from '../tool_call';
import type { ToolDefinition } from '../tool_call/types';
import type { AssistantMessage, ChatDetail, CompletionBlock, Message, UserMessage } from '../types';
import { getCurrentBoardIdByChat } from '../util/isNewBoardChat';
import { ToolCallChatStreamHandler } from './handler';

@Injectable()
export class ChatAskService {
  private readonly logger = new Logger(ChatAskService.name);

  constructor(
    private readonly youllm: YouLLMService,
    private readonly boardDomain: BoardDomainService,
    private readonly webpageContextBuilder: WebpageContextBuilder,
    private readonly userDomain: UserDomainService,
    private readonly chatQueryDomain: ChatQueryDomainService,
    private readonly toolCallService: ToolCallService,
    private readonly messageDAO: MessageDAO,
    private readonly completionBlockDAO: CompletionBlockDAO,
  ) {}
  protected MAX_HISTORY_LENGTH = 10;

  protected getTools(): ToolDefinition[] {
    const librarySearchTool = this.toolCallService.getToolCall(TOOL_TYPES.LIBRARY_SEARCH);
    const boardSearchTool = this.toolCallService.getToolCall(TOOL_TYPES.BOARD_SEARCH);
    const tools: ToolDefinition[] = [];

    if (librarySearchTool) {
      tools.push(librarySearchTool.tool_definition);
    }
    if (boardSearchTool) {
      tools.push(boardSearchTool.tool_definition);
    }

    return tools;
  }

  protected getTraceArgs(chat: ChatDetail) {
    const last_message = (chat.messages as Message[]).findLast(
      (m: Message) => m.role === MessageRoleEnum.ASSISTANT,
    ) as AssistantMessage;
    return {
      sessionId: chat.id as string,
      metadata: {
        chatId: chat.id as string,
        messageId: last_message?.id as string,
        ...(chat.origin?.type === ChatOriginTypeEnum.BOARD && {
          boardId: (chat.origin as ChatBoardOrigin)?.id || '',
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.SNIP && {
          snipId: (chat.origin as ChatSnipOrigin)?.id || '',
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.THOUGHT && {
          thoughtId: (chat.origin as ChatThoughtOrigin)?.id || '',
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.WEBPAGE && {
          url: (chat.origin as ChatWebpageOrigin)?.url || '',
        }),
      },
    };
  }

  async *generateResponse(args: {
    chat: ChatDetail;
    runner: LLMRunner;
    regenerate: boolean;
  }): AsyncGenerator<any, void, unknown> {
    // TODO: Fix CompletionStreamChunk type
    const { chat, runner } = args;
    const max_call_count = 5;
    // TODO: 需要从请求上下文或参数中获取用户信息
    // const user = await this.userDomain.getCurrentUser();
    const user = { id: 'temp-user-id', name: 'temp-user-name' }; // 临时处理
    const all_history = await this.chatQueryDomain.listValidMessagesById({
      chat_id: chat.id as string,
      order: 'asc',
    });
    const chat_history = all_history.slice(-1 * this.MAX_HISTORY_LENGTH - 1);
    const user_message = (chat_history as Message[]).findLast(
      (m: Message) => m.role === MessageRoleEnum.USER,
    ) as UserMessage;
    const assistant_message = (chat_history as Message[]).findLast(
      (m: Message) => m.role === MessageRoleEnum.ASSISTANT,
    );
    if (!assistant_message) {
      throw new Error('No assistant message found in chat history');
    }
    const prompt = (await runner.runSpan({
      name: 'fetch-prompt',
      fn: async (span) =>
        runner.prompt_manager.fetchPrompt(
          {
            name: PromptName.AiAskChatPrompt,
            prefer: 'local',
          },
          span,
          true,
        ),
    })) as ChatPromptClient;
    const language = await this.userDomain.getPrimaryResponseLanguage({
      user_id: user.id,
    });

    const tools = [...this.getTools()];
    if (user_message.tools?.google_search?.use_tool === 'auto') {
      const googleSearchTool = this.toolCallService.getToolCall(TOOL_TYPES.GOOGLE_SEARCH);
      if (googleSearchTool) {
        tools.push(googleSearchTool.tool_definition);
      }
    }
    const handler = new ToolCallChatStreamHandler(
      chat,
      runner,
      this.messageDAO,
      this.completionBlockDAO,
      this.chatQueryDomain,
      this.toolCallService,
      max_call_count,
      tools,
    );
    yield* handler.handleStream(async (blocks: CompletionBlock[]) => {
      // update blocks so tools can access
      if (blocks.length) {
        (assistant_message as AssistantMessage).blocks = blocks;
        handler.setChat(chat);
      }
      // force resolution of aliases when present
      const tool_options = handler.getToolChoice();

      const promptVariables: Record<string, string> = {
        model: assistant_message.model,
        userName: user.name,
        language,
        currentTime: new Date().toLocaleString(),
      };

      // 查询 board 的目录结构
      const board_id = getCurrentBoardIdByChat(chat);
      let prebuiltContext = '';
      if (chat.origin?.type === ChatOriginTypeEnum.WEBPAGE) {
        tool_options.tools = tool_options.tools.filter(
          (t) =>
            ![
              TOOL_TYPES.LIBRARY_SEARCH,
              TOOL_TYPES.BOARD_SEARCH,
              TOOL_TYPES.CREATE_SNIP_BY_URL,
            ].includes(t.function.name as TOOL_TYPES),
        );
        const context = await this.webpageContextBuilder.getChatContext(chat.id as string);
        prebuiltContext = (context.fields as any).content || '';
      } else {
        let disableLibrarySearch = false;

        if (board_id) {
          const board = await this.boardDomain.getById(board_id);
          // TODO: 需要将getBoardDirectoryStructure迁移为注入的服务方法
          // const boardDirectoryStructure = await getBoardDirectoryStructure(
          //   getCurrentBoardIdByChat(chat)
          // );
          const boardDirectoryStructure: any[] = []; // 临时处理
          promptVariables.boardName = board.name;
          promptVariables.boardDirectoryStructure = JSON.stringify(
            boardDirectoryStructure,
            null,
            2,
          );

          // 如果 board 里没有可搜索的 snip 或 thought，则不挂 board_search 工具
          const hasSearchableContent = (
            items: Array<{
              entity_type: BoardItemTypeEnum;
              children?: Array<{ entity_type: BoardItemTypeEnum }>;
            }>,
          ): boolean => {
            return items.some((item) => {
              if ([BoardItemTypeEnum.THOUGHT, BoardItemTypeEnum.SNIP].includes(item.entity_type)) {
                return true;
              }
              if (item.entity_type === BoardItemTypeEnum.BOARD_GROUP && item.children) {
                return hasSearchableContent(item.children);
              }
              return false;
            });
          };
          const isEmptyBoard = !hasSearchableContent(boardDirectoryStructure);
          if (isEmptyBoard) {
            disableLibrarySearch = true;
          }
        } else {
          this.logger.warn('board_id not found, should look into this', {
            chatId: chat.id,
          });
          // 如果没有 board，则不挂 board_search 工具
          disableLibrarySearch = true;
        }

        if (disableLibrarySearch) {
          tool_options.tools = tool_options.tools.filter(
            (t) => t.function.name !== TOOL_TYPES.BOARD_SEARCH,
          );
        }
      }

      const tool_blocks = blocks.filter((b) => b.type === CompletionBlockTypeEnum.TOOL);
      if (tool_blocks.length === 0 && user_message.tools && tool_options.tool_choice !== 'none') {
        const tool_to_call = Object.keys(user_message.tools).filter((tool_name) => {
          return (
            user_message.tools?.[tool_name as keyof typeof user_message.tools]?.use_tool ===
            'required'
          );
        })[0];
        const is_available = !!tool_options.tools.find((t) => t.function.name === tool_to_call);
        if (tool_to_call && is_available) {
          tool_options.tool_choice = {
            type: 'function',
            function: {
              name: tool_to_call,
            },
          };
        }
      }

      const messages = await (runner.prompt_manager as WriterPromptService).buildFromChatMessages({
        prompt,
        model: assistant_message.model as LLMs,
        messages: chat_history as Message[],
        variables: promptVariables,
        prebuiltContext,
      });

      return this.youllm.streamChatCompletionWithMessages(prompt, {
        runner,
        messages,
        traceArgs: this.getTraceArgs(chat),
        modelOptions: {
          ...tool_options,
          ...(assistant_message?.model ? { model: assistant_message.model } : {}),
        },
      });
    });
  }
}
