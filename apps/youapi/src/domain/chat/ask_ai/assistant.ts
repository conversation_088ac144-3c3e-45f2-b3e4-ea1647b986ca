import { Injectable } from '@nestjs/common';
import { ChatPromptClient } from 'langfuse-core';
import { YouapiClsService } from '@/common/services/cls.service';
import {
  type ChatBoardOrigin,
  ChatOriginTypeEnum,
  type ChatSnipOrigin,
  type ChatThoughtOrigin,
  type ChatWebpageOrigin,
  CompletionBlockTypeEnum,
  type CompletionStreamChunk,
  EditCommandTypeEnum,
  type LLMs,
  MessageRoleEnum,
  TOOL_TYPES,
} from '@/common/types';
import { CompletionBlockDAO, MessageDAO } from '@/dao/chat';
import { PromptName } from '@/infra/youllm';
import { LLMRunner } from '@/infra/youllm/llm_service/runner';
import { WriterPromptService } from '@/infra/youllm/prompt';
// TODO: 等完成迁移后引入
// import { getBoardDirectoryStructure } from '@/lib/app/board';
import { BoardDomainService } from '../../board';
import { BoardItemTypeEnum } from '../../board-item/types';
import { YouLLMService } from '../../llm';
import { WebpageContextBuilder } from '../../llm/context/webpage';
import { UserDomainService } from '../../user';
import { ChatQueryDomainService } from '../query';
import { ToolCallService } from '../tool_call';
import { AssistantMessage, ChatDetail, CompletionBlock, Message, UserMessage } from '../types';
import { getCurrentBoardIdByChat } from '../util/isNewBoardChat';
import { type StreamChunkUnion, ToolCallChatStreamHandler } from './handler';

function getBoardDirectoryStructure(board_id: string) {
  return [];
}

@Injectable()
export class ChatAssistantService {
  protected MAX_HISTORY_LENGTH = 10;

  constructor(
    private readonly youllm: YouLLMService,
    private readonly boardDomain: BoardDomainService,
    private readonly webpageContextBuilder: WebpageContextBuilder,
    private readonly userDomain: UserDomainService,
    private readonly chatQueryDomain: ChatQueryDomainService,
    private readonly toolCallService: ToolCallService,
    private readonly youbizClsService: YouapiClsService,
    private readonly messageDAO: MessageDAO,
    private readonly completionBlockDAO: CompletionBlockDAO,
  ) {}

  protected getTraceArgs(chat: ChatDetail) {
    const last_message = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.ASSISTANT,
    ) as AssistantMessage;
    return {
      sessionId: chat.id,
      metadata: {
        chatId: chat.id,
        messageId: last_message?.id,
        ...(chat.origin?.type === ChatOriginTypeEnum.BOARD && {
          boardId: (chat.origin as ChatBoardOrigin)?.id,
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.SNIP && {
          snipId: (chat.origin as ChatSnipOrigin)?.id,
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.THOUGHT && {
          thoughtId: (chat.origin as ChatThoughtOrigin)?.id,
        }),
        ...(chat.origin?.type === ChatOriginTypeEnum.WEBPAGE && {
          url: (chat.origin as ChatWebpageOrigin)?.url,
        }),
      },
    };
  }

  async *generateResponse(args: {
    chat: ChatDetail;
    runner: LLMRunner;
    regenerate: boolean;
  }): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    const { chat, runner } = args;
    const max_call_count = 10;
    const userId = this.youbizClsService.getUserId();
    const user = await this.userDomain.selectOneById(userId);
    const all_history = await this.chatQueryDomain.listValidMessagesById({
      chat_id: chat.id,
      order: 'asc',
    });
    const chat_history = all_history.slice(-1 * this.MAX_HISTORY_LENGTH - 1);
    const user_message = chat_history.findLast(
      (m) => m.role === MessageRoleEnum.USER,
    ) as UserMessage;
    const assistant_message = chat_history.findLast(
      (m) => m.role === MessageRoleEnum.ASSISTANT,
    ) as AssistantMessage;
    const prompt = (await runner.runSpan({
      name: 'fetch-prompt',
      fn: async (span) =>
        runner.prompt_manager.fetchPrompt(
          {
            name: PromptName.WriterSystemPrompt,
            prefer: 'local',
          },
          span,
          true,
        ),
    })) as ChatPromptClient;
    const language = await this.userDomain.getPrimaryResponseLanguage({
      user_id: user.id,
    });

    let tools = [
      this.toolCallService.getToolCall(TOOL_TYPES.GOOGLE_SEARCH)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.LIBRARY_SEARCH)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.BOARD_SEARCH)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.DIAGRAM_GENERATE)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.CREATE_SNIP_BY_URL)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.IMAGE_GENERATE)?.tool_definition,
      this.toolCallService.getToolCall(TOOL_TYPES.EDIT_THOUGHT)?.tool_definition,
    ];
    if (user_message.tools?.google_search?.use_tool === 'none') {
      tools = tools.filter((t) => t.function.name !== TOOL_TYPES.GOOGLE_SEARCH);
    }
    const handler = new ToolCallChatStreamHandler(
      chat,
      runner,
      this.messageDAO,
      this.completionBlockDAO,
      this.chatQueryDomain,
      this.toolCallService,
      max_call_count,
      tools,
    );
    yield* handler.handleStream(async (blocks: CompletionBlock[]) => {
      // update blocks so tools can access
      if (blocks.length) {
        (assistant_message as AssistantMessage).blocks = blocks;
        handler.setChat(chat);
      }
      // force resolution of aliases when present
      const tool_options = handler.getToolChoice();

      const promptVariables: Record<string, string> = {
        model: assistant_message.model,
        userName: user.name,
        language,
        currentTime: new Date().toLocaleString(),
      };

      // 查询 board 的目录结构
      const board_id = getCurrentBoardIdByChat(chat);
      let prebuiltContext = '';
      if (chat.origin?.type === ChatOriginTypeEnum.WEBPAGE) {
        tool_options.tools = tool_options.tools.filter(
          (t) =>
            ![
              TOOL_TYPES.LIBRARY_SEARCH,
              TOOL_TYPES.BOARD_SEARCH,
              TOOL_TYPES.CREATE_SNIP_BY_URL,
            ].includes(t.function.name as TOOL_TYPES),
        );
        const context = await this.webpageContextBuilder.getChatContext(chat.id);
        prebuiltContext = context.fields.content;
      } else {
        let disableLibrarySearch = false;

        if (board_id) {
          const board = await this.boardDomain.getById(board_id);
          const boardDirectoryStructure = await getBoardDirectoryStructure(
            getCurrentBoardIdByChat(chat),
          );
          promptVariables.boardName = board.name;
          promptVariables.boardDirectoryStructure = JSON.stringify(
            boardDirectoryStructure,
            null,
            2,
          );

          // 如果 board 里没有可搜索的 snip 或 thought，则不挂 board_search 工具
          const hasSearchableContent = (
            items: Array<{
              entity_type: BoardItemTypeEnum;
              children?: Array<{ entity_type: BoardItemTypeEnum }>;
            }>,
          ): boolean => {
            return items.some((item) => {
              if ([BoardItemTypeEnum.THOUGHT, BoardItemTypeEnum.SNIP].includes(item.entity_type)) {
                return true;
              }
              if (item.entity_type === BoardItemTypeEnum.BOARD_GROUP && item.children) {
                return hasSearchableContent(item.children);
              }
              return false;
            });
          };
          const isEmptyBoard = !hasSearchableContent(boardDirectoryStructure);
          if (isEmptyBoard) {
            disableLibrarySearch = true;
          }
        } else {
          console.warn('board_id not found, should look into this');
          // 如果没有 board，则不挂 board_search 工具
          disableLibrarySearch = true;
        }

        if (disableLibrarySearch) {
          tool_options.tools = tool_options.tools.filter(
            (t) => t.function.name !== TOOL_TYPES.BOARD_SEARCH,
          );
        }
      }

      const tool_blocks = blocks.filter((b) => b.type === CompletionBlockTypeEnum.TOOL);
      if (tool_blocks.length === 0 && user_message.tools && tool_options.tool_choice !== 'none') {
        const tool_to_call = Object.keys(user_message.tools).filter((tool_name) => {
          return (
            user_message.tools?.[tool_name as keyof typeof user_message.tools]?.use_tool ===
            'required'
          );
        })[0];
        const is_available = !!tool_options.tools.find((t) => t.function.name === tool_to_call);
        if (tool_to_call && is_available) {
          tool_options.tool_choice = {
            type: 'function',
            function: {
              name: tool_to_call,
            },
          };
        }
      }
      if (
        tool_blocks.length === 0 &&
        [
          EditCommandTypeEnum.SUGGEST_SEARCH,
          EditCommandTypeEnum.SUGGEST_GENERATE_TEXT,
          EditCommandTypeEnum.SUGGEST_GENERATE_AUDIO,
          EditCommandTypeEnum.SUGGEST_GENERATE_IMAGE,
        ].includes(user_message.command?.type as EditCommandTypeEnum) &&
        tool_options.tool_choice !== 'none'
      ) {
        tool_options.tool_choice = {
          type: 'function',
          function: {
            name: '',
          },
        };
        switch (user_message.command?.type) {
          case EditCommandTypeEnum.SUGGEST_SEARCH:
            tool_options.tool_choice.function.name = TOOL_TYPES.GOOGLE_SEARCH;
            break;
          case EditCommandTypeEnum.SUGGEST_GENERATE_TEXT:
            tool_options.tool_choice.function.name = TOOL_TYPES.EDIT_THOUGHT;
            break;
          case EditCommandTypeEnum.SUGGEST_GENERATE_AUDIO:
            tool_options.tool_choice.function.name = TOOL_TYPES.AUDIO_GENERATE;
            break;
          case EditCommandTypeEnum.SUGGEST_GENERATE_IMAGE:
            tool_options.tool_choice.function.name = TOOL_TYPES.IMAGE_GENERATE;
            break;
          default:
            break;
        }
      }

      const messages = await (runner.prompt_manager as WriterPromptService).buildFromChatMessages({
        prompt,
        model: assistant_message.model as LLMs,
        messages: chat_history as Message[],
        variables: promptVariables,
        prebuiltContext,
      });

      return this.youllm.streamChatCompletionWithMessages(prompt, {
        runner,
        messages,
        traceArgs: this.getTraceArgs(chat),
        modelOptions: {
          ...tool_options,
          ...(assistant_message?.model ? { model: assistant_message.model } : {}),
        },
      });
    });
  }
}
