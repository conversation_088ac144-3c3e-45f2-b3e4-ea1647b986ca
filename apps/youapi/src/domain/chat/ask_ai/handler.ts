import { LangfuseSpanClient } from 'langfuse-core';
import { cloneDeep, debounce, isEqual, pick } from 'lodash';
import { ChatCompletionToolChoiceOption } from 'openai/resources/index.mjs';
import { LLMError, MaxToolCallsError, RestError, type RestErrorInfo } from '@/common/errors';
import {
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  type CompletionStreamAppendJsonChunk,
  type CompletionStreamAppendStringChunk,
  type CompletionStreamChunk,
  type CompletionStreamInsertChunk,
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  type LLMs,
  MessageRoleEnum,
  MessageStatusEnum,
  MODEL_DEFINITION,
  type ModelDefinition,
  TOOL_TYPES,
} from '@/common/types';
import { jsonToXml, runConcurrently, SafeParse } from '@/common/utils';
import { CompletionBlockDAO, MessageDAO } from '@/dao/chat';
import { CompletionBlockDO } from '@/dao/chat/types';
import { SpanEventLevel, type YLChatStreamCompletion } from '@/infra/youllm';
import { LLMRunner } from '@/infra/youllm/llm_service/runner';
import { DEFAULT_LLM_RESPONSE } from '../fallback';
import { ChatQueryDomainService } from '../query';
import { ToolCallService } from '../tool_call';
import type { ToolCallResult, ToolDefinition } from '../tool_call/types';
import {
  AssistantMessage,
  ChatDetail,
  CompletionBlock,
  CompletionContentBlock,
  CompletionReasoningBlock,
  CompletionToolBlock,
  PlainCompletionBlock,
} from '../types';

type StreamGeneratorFn = () => Promise<AsyncIterable<YLChatStreamCompletion>>;
type StreamGeneratorFnSupportingTool = (
  blocks: CompletionBlock[],
  is_about_to_exceed_tool_call_limit: boolean,
) => Promise<AsyncIterable<YLChatStreamCompletion>>;
export type StreamChunkUnion =
  | string
  | RestErrorInfo
  | PlainCompletionBlock
  | MessageStatusEnum
  | CompletionBlockStatusEnum
  | object;

class ChatStreamHandler {
  protected model_definition!: ModelDefinition;
  protected assistant_message!: AssistantMessage;
  protected user_message_content = '';
  protected stream!: AsyncIterable<YLChatStreamCompletion>;
  protected blocks: CompletionBlock[] = [];
  protected messageDAO: MessageDAO;
  protected completionBlockDAO: CompletionBlockDAO;
  protected chatQueryDomain: ChatQueryDomainService;
  protected toolCallService: ToolCallService;

  protected updateBlocks: (blocks: CompletionBlock[]) => Promise<void>;
  protected updateAssistantMessage: () => Promise<void>;
  protected updateFn: (blocks: CompletionBlock[]) => Promise<void>;
  protected throttleUpdate: (blocks: CompletionBlock[]) => void;
  protected flushUpdate: (blocks: CompletionBlock[]) => Promise<void>;

  constructor(
    protected chat: ChatDetail,
    protected runner: LLMRunner,
    messageDAO: MessageDAO,
    completionBlockDAO: CompletionBlockDAO,
    chatQueryDomain: ChatQueryDomainService,
    toolCallService: ToolCallService,
  ) {
    this.messageDAO = messageDAO;
    this.completionBlockDAO = completionBlockDAO;
    this.chatQueryDomain = chatQueryDomain;
    this.toolCallService = toolCallService;
    this.user_message_content =
      chat.messages.findLast((m) => m.role === MessageRoleEnum.USER)?.content || '';
    this.assistant_message = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.ASSISTANT,
    ) as AssistantMessage;

    this.blocks = this.assistant_message.blocks || [];
    this.model_definition = MODEL_DEFINITION.get(
      this.assistant_message.model as LLMs,
    ) as ModelDefinition;

    // 更新 blocks 以及控制更新频次
    let previous_blocks: CompletionBlock[] = cloneDeep(this.blocks);
    this.updateBlocks = async (blocks: CompletionBlock[]) => {
      if (!blocks.length) return;

      let blocks_copy = [...blocks];
      // update on a need-basis
      const start_index = blocks_copy.findIndex((block, index) => {
        if (!previous_blocks[index]) return true;
        return !isEqual(block, previous_blocks[index]);
      });

      if (start_index === -1) {
        console.log('No block changes detected');
        return;
      }
      console.log('Updating blocks from index:', start_index);

      // Ensure all blocks have an updated_at timestamp and update it for changed blocks
      blocks_copy = blocks_copy.map((b, index) => ({
        ...b,
        // Update timestamp if block has changed (comparing with previous block)
        ...(index >= start_index && b.updated_at === previous_blocks[index]?.updated_at
          ? { updated_at: new Date() }
          : {}),
      }));
      const updated_blocks = blocks_copy.slice(start_index);

      await runConcurrently(
        updated_blocks.map((block) =>
          this.completionBlockDAO.updateOne.bind(this.completionBlockDAO, block.id, block),
        ),
      );

      previous_blocks = cloneDeep(blocks_copy);
    };
    // 更新 message 以及控制更新频次
    let previous_message: AssistantMessage = { ...this.assistant_message };
    this.updateAssistantMessage = async () => {
      // update on a need-basis
      if (isEqual(previous_message, this.assistant_message)) return;

      await this.messageDAO.updateOne(this.assistant_message.id, {
        error: this.assistant_message.error || null,
        status: this.assistant_message.status,
        updated_at: new Date(),
      });
      previous_message = { ...this.assistant_message };
    };

    this.updateFn = async (blocks: CompletionBlock[]) => {
      await this.updateBlocks(blocks);
      await this.updateAssistantMessage();
    };

    const throttledFn = debounce(this.updateFn, 1000, {
      leading: false,
      trailing: true,
    });

    this.throttleUpdate = throttledFn;
    this.flushUpdate = async (blocks: CompletionBlock[]) => {
      // Cancel any pending updates to ensure we use latest state
      throttledFn.cancel();
      await this.updateFn(blocks);
    };
  }

  /**
   * Finalizes all blocks that are in ING state by changing them to DONE
   * @returns Generator yielding status change messages
   */
  protected async *finalizeBlockStatuses() {
    for (const block of this.blocks) {
      if (
        block.status === CompletionBlockStatusEnum.ING &&
        block.type !== CompletionBlockTypeEnum.TOOL
      ) {
        console.log('update block status to done', block.type, block.data);
        yield* this.updateBlockStatus(block, CompletionBlockStatusEnum.DONE);
      }
    }
  }

  protected async *fakeReasoning() {
    if (!this.model_definition.extra?.fake_reasoning) return;
    const reasoning_block = this.blocks.find(
      (b) => b.type === CompletionBlockTypeEnum.REASONING,
    ) as CompletionReasoningBlock;

    if (!reasoning_block) {
      yield* this.insertBlock({
        type: CompletionBlockTypeEnum.REASONING,
      });
    }
  }

  /**
   * Updates a block's status and yields a status change message
   * @param block The block to update
   * @param status The new status to set
   */
  protected async *updateBlockStatus(block: CompletionBlock, status: CompletionBlockStatusEnum) {
    if (
      status === CompletionBlockStatusEnum.EXECUTING &&
      block.status !== CompletionBlockStatusEnum.ING
    )
      return;
    const index = this.blocks.findIndex((b) => b.id === block.id);
    if (index === -1) {
      console.warn('block not found', block.id);
      return;
    }

    this.blocks[index].status = status;
    yield {
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: block.id,
      path: 'status',
      data: status,
    } as CompletionStreamReplaceChunk<CompletionBlockStatusEnum>;

    if (
      block.type === CompletionBlockTypeEnum.REASONING &&
      status === CompletionBlockStatusEnum.DONE
    ) {
      this.blocks[index].updated_at = new Date();
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: block.id,
        path: 'updated_at',
        data: this.blocks[index].updated_at.toISOString(),
      } as CompletionStreamReplaceChunk<string>;
    }

    // For tool blocks, ensure we wait for the update to complete
    if (status === CompletionBlockStatusEnum.DONE) {
      await this.flushUpdate(this.blocks);
    }
  }

  /**
   * Updates the assistant message status and yields a status change message
   * @param status The new status to set
   */
  protected async *updateMessageStatus(status: MessageStatusEnum, error?: Error) {
    this.assistant_message.status = status;
    yield {
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'Message',
      targetId: this.assistant_message.id,
      path: 'status',
      data: status,
    } as CompletionStreamReplaceChunk<MessageStatusEnum>;

    if (error) {
      const code = (error as LLMError)?.code || 'unknown_error';
      const error_info =
        error instanceof RestError
          ? error.json('') // TODO: 目前没有 i18n 异常消息
          : {
              code,
              status: 400,
              message:
                code in DEFAULT_LLM_RESPONSE
                  ? DEFAULT_LLM_RESPONSE[code as keyof typeof DEFAULT_LLM_RESPONSE]
                  : DEFAULT_LLM_RESPONSE.Error,
              trace_id: this.assistant_message.trace_id,
            };
      this.assistant_message.error = error_info;
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'Message',
        targetId: this.assistant_message.id,
        path: 'error',
        data: error_info,
      } as CompletionStreamReplaceChunk<RestErrorInfo>;
    }

    await this.updateAssistantMessage();
  }

  protected async *insertBlock(
    completionBlock:
      | {
          type: CompletionBlockTypeEnum.CONTENT;
        }
      | {
          type: CompletionBlockTypeEnum.REASONING;
        }
      | {
          type: CompletionBlockTypeEnum.TOOL;
          tool_id: string;
          tool_name: string;
          tool_arguments: string;
        },
  ) {
    // 假设只有一个进行中的 CompletionBlock，修改前置 Block 状态
    const previous_block = this.blocks.findLast((b) => {
      return b.status === CompletionBlockStatusEnum.ING && b.type !== CompletionBlockTypeEnum.TOOL;
    });
    if (previous_block) {
      yield* this.updateBlockStatus(previous_block, CompletionBlockStatusEnum.DONE);
    }

    let blockDO: CompletionBlockDO;
    if (completionBlock.type === CompletionBlockTypeEnum.CONTENT) {
      blockDO = await this.completionBlockDAO.insertContentBlock({
        message_id: this.assistant_message.id,
      });
    } else if (completionBlock.type === CompletionBlockTypeEnum.REASONING) {
      blockDO = await this.completionBlockDAO.insertReasoningBlock({
        message_id: this.assistant_message.id,
      });
    } else if (completionBlock.type === CompletionBlockTypeEnum.TOOL) {
      blockDO = await this.completionBlockDAO.insertToolBlock({
        message_id: this.assistant_message.id,
        tool_id: completionBlock.tool_id,
        tool_name: completionBlock.tool_name,
        tool_arguments: SafeParse(completionBlock.tool_arguments, false, {}),
      });
    }

    const block = this.chatQueryDomain.toCompletionBlock(blockDO!) as CompletionBlock;
    this.blocks.push(block);
    yield {
      mode: CompletionStreamModeEnum.INSERT,
      data: {
        ...block,
        created_at: block.created_at.toISOString(),
        updated_at: block.updated_at.toISOString(),
      },
      dataType: 'CompletionBlock',
    } as CompletionStreamInsertChunk<PlainCompletionBlock>;
    return block;
  }

  protected async *setReasoning(param: {
    reasoning?: string;
    signature?: string;
    redacted_thinking?: string;
  }) {
    let block_index = this.blocks.findIndex((b) => b.type === CompletionBlockTypeEnum.REASONING);
    if (block_index === -1) {
      yield* this.insertBlock({
        type: CompletionBlockTypeEnum.REASONING,
      });
      block_index = this.blocks.length - 1;
    }
    if (param.reasoning) {
      yield {
        mode: CompletionStreamModeEnum.APPEND_STRING,
        targetType: 'CompletionBlock',
        targetId: this.blocks[block_index].id,
        path: 'data',
        data: param.reasoning,
      } as CompletionStreamAppendStringChunk;
      (this.blocks[block_index] as CompletionReasoningBlock).data += param.reasoning;
    }
    if (param.signature) {
      this.blocks[block_index].extra.signature = param.signature;
    }
    if (param.redacted_thinking) {
      if (!this.blocks[block_index].extra.redacted_thinking) {
        this.blocks[block_index].extra.redacted_thinking = '';
      }
      this.blocks[block_index].extra.redacted_thinking += param.redacted_thinking;
    }

    this.throttleUpdate(this.blocks);
  }

  protected async *setContent(param: { content: string }) {
    let block_index = this.blocks.length - 1;
    if (this.blocks[block_index]?.type !== CompletionBlockTypeEnum.CONTENT) {
      yield* this.insertBlock({
        type: CompletionBlockTypeEnum.CONTENT,
      });
      block_index = this.blocks.length - 1;
    }
    if (param.content) {
      (this.blocks[block_index] as CompletionContentBlock).data += param.content;
      yield {
        mode: CompletionStreamModeEnum.APPEND_STRING,
        targetType: 'CompletionBlock',
        targetId: this.blocks[block_index].id,
        path: 'data',
        data: param.content,
      } as CompletionStreamAppendStringChunk;
    }

    this.throttleUpdate(this.blocks);
  }

  async *onMessage(
    message: YLChatStreamCompletion,
  ): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    // reasoning
    if (message.choices[0]?.delta?.thinking?.signature) {
      await this.setReasoning({
        signature: message.choices[0].delta.thinking.signature,
      });
      return;
    }
    if (
      message.choices[0]?.delta?.thinking?.type === 'redacted_thinking' &&
      message.choices[0]?.delta?.thinking?.data
    ) {
      await this.setReasoning({
        redacted_thinking: message.choices[0].delta.thinking.data,
      });
      return;
    }
    if (
      message.choices[0]?.delta?.thinking?.type === 'thinking' &&
      message.choices[0]?.delta?.thinking?.data
    ) {
      yield* this.setReasoning({
        reasoning: message.choices[0].delta.thinking.data,
      });
      return;
    }

    // content
    if (message.choices[0]?.delta?.content) {
      yield* this.setContent({
        content: message.choices[0].delta.content,
      });
      return;
    }

    // 消息结束，处理 finish_reason
    if (message.choices[0]?.finish_reason) {
      switch (message.choices[0]?.finish_reason) {
        case 'stop': // 正常返回
          yield* this.finalizeBlockStatuses();
          break;
        case 'tool_calls': // 调用 tool_call
          this.assistant_message.status = MessageStatusEnum.ING;
          break;
        case 'length': // 返回内容超过 max_tokens
          this.runner.trace.event({
            name: 'output-token-limit-exceeded',
            level: SpanEventLevel.WARNING,
          });
          break;
        default: // 其他错误
          throw new LLMError(message.choices[0]?.finish_reason, {
            message,
          });
      }
    }
  }

  async *onError(error: Error) {
    const err: Error = error;
    err.stack = error.stack;
    if (error instanceof RestError) {
      if (error.code === 'content_filter') {
        this.runner.trace.event({
          name: 'content-filtered',
          level: SpanEventLevel.WARNING,
        });
      }
      if (error.code === 'context_length_exceeded') {
        this.runner.trace.event({
          name: 'context-length-exceeded',
          level: SpanEventLevel.ERROR,
        });
      }
      if (error.code === 'invalid_image_format') {
        this.runner.trace.event({
          name: 'image-format-not-supported',
          level: SpanEventLevel.ERROR,
        });
      }
      if (error.code === MaxToolCallsError.name) {
        this.runner.trace.event({
          name: 'max-tool-calls-exceeded',
          level: SpanEventLevel.ERROR,
        });
      }
    } else {
      console.error(err);
      this.runner.trace.event({
        name: 'unknown-error',
        level: SpanEventLevel.ERROR,
        input: err,
      });
    }

    yield* this.updateMessageStatus(MessageStatusEnum.ERROR, err);
  }

  async *handleStream(
    generateStream: StreamGeneratorFn,
  ): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    try {
      yield* this.updateMessageStatus(MessageStatusEnum.ING);
      yield* this.fakeReasoning();

      this.stream = await generateStream();
      for await (const message of this.stream) {
        yield* this.onMessage(message);
      }

      yield* this.updateMessageStatus(MessageStatusEnum.DONE);
    } catch (error) {
      yield* this.onError(error as Error);
    } finally {
      await this.flushUpdate(this.blocks);
    }
  }
}

class ToolCallChatStreamHandler extends ChatStreamHandler {
  protected call_count: Record<string, number>;
  protected tool_call_map: Record<
    string,
    {
      id: string;
      name: TOOL_TYPES;
      arguments: string;
    }
  >;
  protected model!: LLMs;
  protected generateStream!: StreamGeneratorFnSupportingTool;
  // 极简方案：只记录当前 block 对象和开始时间
  private currentGeneratingToolBlock?: CompletionToolBlock;
  private currentGeneratingToolCallStart?: number;

  constructor(
    chat: ChatDetail,
    runner: LLMRunner,
    messageDAO: MessageDAO,
    completionBlockDAO: CompletionBlockDAO,
    chatQueryDomain: ChatQueryDomainService,
    toolCallService: ToolCallService,
    protected max_call_count: number = 5,
    protected tools: ToolDefinition[],
  ) {
    super(chat, runner, messageDAO, completionBlockDAO, chatQueryDomain, toolCallService);
    this.call_count = {};
    this.tool_call_map = {};
  }

  get max_calls() {
    return this.max_call_count;
  }

  setChat(chat: ChatDetail) {
    this.chat = chat;
  }

  getCompletedToolCalls() {
    return this.blocks.filter(
      (b) => b.type === CompletionBlockTypeEnum.TOOL && b.status !== CompletionBlockStatusEnum.ING,
    ).length;
  }

  getToolChoice(default_choice: ChatCompletionToolChoiceOption = 'auto') {
    const completed = this.getCompletedToolCalls();
    const available_tools =
      this.tools.filter((t) => {
        if (!this.call_count[t.function.name]) return true;
        const call_count = this.call_count[t.function.name];
        return call_count < t.max_calls;
      }) || [];
    if (completed < this.max_calls) {
      return {
        tool_choice: default_choice,
        tools: available_tools,
        tool_description: jsonToXml({
          tools: available_tools.map((t) => ({
            type: 'function',
            function: pick(t.function, ['name', 'description']),
          })),
        }),
      };
    } else {
      return {
        tool_choice: 'none' as ChatCompletionToolChoiceOption,
        // fix bedrock: The toolConfig field must be defined when using toolUse and toolResult content blocks
        tools: this.tools,
      };
    }
  }

  async *callOneTool(
    key: string,
    parent_span: LangfuseSpanClient,
  ): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    // 1. Initialize tracking and get tool information
    const tool_call = this.tool_call_map[key];

    // Find the corresponding block
    const block_index = this.blocks.findIndex(
      (b) => b.type === CompletionBlockTypeEnum.TOOL && b.tool_id === tool_call.id,
    );
    const block = this.blocks[block_index] as CompletionToolBlock;

    // Set up tracing
    const span = parent_span.span({
      name: `tool-call-${tool_call?.name || 'unknown'}`,
      input: {
        tool_name: tool_call?.name,
        arguments: tool_call?.arguments,
        tool_call_id: tool_call?.id,
      },
    });

    try {
      // 2. Validate inputs and state
      if (!block) {
        throw new LLMError(
          'tool-block-not-found',
          {
            context: { tool_call_id: tool_call.id },
          },
          `Block not found: ${tool_call?.id || key}`,
        );
      }

      if (!tool_call) {
        throw new LLMError(
          'tool-call-not-found',
          {
            context: {
              tool_call_id: key,
              all_calls: Object.keys(this.tool_call_map),
            },
          },
          `Tool not found: ${block.tool_name}`,
        );
      }

      const tool = this.toolCallService.getToolCall(tool_call.name);
      if (!tool) {
        throw new LLMError(
          'tool-implementation-not-found',
          {
            context: {
              tool_name: tool_call.name,
              all_tools: [],
            },
          },
          `Tool not implemented: ${block.tool_name}`,
        );
      }

      // 3. Check call limits
      // Track call count at tool level, not at handler level
      const currentCallCount = (this.call_count[tool_call.name] =
        (this.call_count[tool_call.name] || 0) + 1);
      if (currentCallCount > tool.tool_definition.max_calls) {
        throw new MaxToolCallsError({
          tool_name: tool_call.name,
          call_count: currentCallCount,
        });
      }

      // 4. Parse arguments
      const parsedParams = SafeParse(tool_call.arguments, false, {});
      block.tool_arguments = parsedParams;

      // 5. Execute tool with timeout protection
      const toolContext = {
        chat: this.chat,
        user_id: this.chat.creator_id,
        parsed_params: parsedParams,
        tool_call_id: tool_call.id,
        completion_block: block,
      };

      // 通知前端 tool call 正在执行
      yield* this.updateBlockStatus(block, CompletionBlockStatusEnum.EXECUTING);

      // 记录执行耗时
      const executeStart = Date.now();
      const toolStream = await this.toolCallService.callTool(tool_call.name, toolContext, span);
      // Forward any intermediate yields from the tool function
      let yielded: IteratorResult<CompletionStreamChunk<StreamChunkUnion>, ToolCallResult>;
      while (!(yielded = await toolStream.next()).done) {
        yield yielded.value as CompletionStreamChunk<StreamChunkUnion>;
      }

      // Process tool result
      const result = yielded.value;

      const executeEnd = Date.now();
      block.tool_execute_elapsed_ms = executeEnd - executeStart;

      const response = result?.response || '';
      const toolResult = result?.result;

      // If we have a result but no response, convert result to string
      let finalResponse = response;
      if (!finalResponse && toolResult) {
        finalResponse = typeof toolResult === 'string' ? toolResult : JSON.stringify(toolResult);
      }

      // Update block with results
      block.tool_result = toolResult || {};
      block.tool_response = finalResponse;
      if (block.tool_name !== TOOL_TYPES.GOOGLE_SEARCH) {
        yield {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: block.id,
          path: 'tool_result',
          data: block.tool_result,
        } as CompletionStreamReplaceChunk<StreamChunkUnion>;
      }
      yield* this.updateBlockStatus(block, CompletionBlockStatusEnum.DONE);

      // Mark block as done and end the span
      span.update({
        level: SpanEventLevel.DEFAULT,
        output: {
          status: block.status,
          tool_result: block.tool_result,
          tool_response: block.tool_response,
        },
      });
    } catch (error) {
      // 7. Centralized error handling
      console.error(error);

      const error_info =
        error instanceof RestError
          ? error.json('')
          : {
              name: error instanceof Error ? error.name : 'UnknownError',
              code: (error as LLMError)?.code || 'unknown_error',
              status: 400,
              message: (error as LLMError)?.message || DEFAULT_LLM_RESPONSE.Error,
            };
      span.update({
        level: SpanEventLevel.ERROR,
        output: {
          error: error_info,
        },
      });

      // Update error to block and message accordingly
      if (block) {
        block.extra = {
          error: error_info,
        };
        block.tool_response =
          error instanceof MaxToolCallsError
            ? `You have reached the maximum number of calls for this tool. Stop calling this tool and continue with existing tool results.`
            : (error as LLMError).message ||
              'tool call failed, please try again with different parameters';
        yield {
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: block.id,
          path: 'extra.error',
          data: error_info,
        } as CompletionStreamReplaceChunk<RestErrorInfo>;
        yield* this.updateBlockStatus(block, CompletionBlockStatusEnum.ERROR);
      }
    } finally {
      span.end({});
    }
  }

  async *onToolCalls(): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    const tool_ids_to_call = Object.keys(this.tool_call_map);
    if (!Object.keys(this.tool_call_map).length) {
      console.warn('no tools to call');
      return;
    }

    const span = this.runner.trace.span({
      name: 'calling-tools',
      input: this.tool_call_map,
    });

    for await (const key of tool_ids_to_call) {
      yield* this.callOneTool(key, span);
    }

    span.end({
      output: tool_ids_to_call.reduce(
        (accu, tool_id) => {
          const block = this.blocks.find(
            (b) => b.type === CompletionBlockTypeEnum.TOOL && b.tool_id === tool_id,
          ) as CompletionToolBlock;
          if (block) {
            accu[tool_id] = block.tool_result;
          }
          return accu;
        },
        {} as Record<string, object>,
      ),
    });
    await this.flushUpdate(this.blocks);
    yield* this.handleStream();
  }

  async *onMessage(
    message: YLChatStreamCompletion,
  ): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    yield* super.onMessage(message);

    if (message.choices[0]?.delta?.tool_calls) {
      let block: CompletionToolBlock | undefined;
      for (const tool_call of message.choices[0].delta.tool_calls) {
        if (!this.tool_call_map[tool_call.index]) {
          this.tool_call_map[tool_call.index] = {
            id: tool_call.id as string,
            name: tool_call.function?.name as TOOL_TYPES,
            arguments: '',
          };
          // 处理上一个 block 的 generate_elapsed_ms
          this.onGeneratingToolCallEnd();
          const new_block = yield* this.insertBlock({
            type: CompletionBlockTypeEnum.TOOL,
            tool_id: this.tool_call_map[tool_call.index].id,
            tool_name: this.tool_call_map[tool_call.index].name,
            tool_arguments: this.tool_call_map[tool_call.index].arguments,
          });
          block = new_block as CompletionToolBlock;
          // 记录当前 block 和开始时间
          this.currentGeneratingToolBlock = block;
          this.currentGeneratingToolCallStart = Date.now();
        } else {
          block = this.blocks.find(
            (b) =>
              b.type === CompletionBlockTypeEnum.TOOL &&
              b.tool_id === this.tool_call_map[tool_call.index].id,
          ) as CompletionToolBlock;
        }

        if (!this.tool_call_map[tool_call.index].id && tool_call.id) {
          this.tool_call_map[tool_call.index].id = tool_call.id;
        }
        if (!this.tool_call_map[tool_call.index].name && tool_call.function?.name) {
          this.tool_call_map[tool_call.index].name = tool_call.function.name as TOOL_TYPES;
        }
        if (tool_call.function?.arguments) {
          yield {
            mode: CompletionStreamModeEnum.APPEND_JSON,
            targetType: 'CompletionBlock',
            targetId: block.id,
            path: 'tool_arguments',
            data: tool_call.function?.arguments,
          } as CompletionStreamAppendJsonChunk;
          this.tool_call_map[tool_call.index].arguments += tool_call.function?.arguments || '';
        }
      }
    }

    // 当 tool call 数据（入参）收集完毕后，调用 tool call
    if (
      message.choices[0]?.finish_reason === 'tool_calls' &&
      Object.keys(this.tool_call_map).length
    ) {
      // 处理最后一个未写入 generate_elapsed_ms 的 block
      this.onGeneratingToolCallEnd();
      yield* this.onToolCalls();
    }
    // Gemini 调用 tool 或正常结束时 finishReason 为 STOP
    // OpenAI Bug, see https://community.openai.com/t/function-call-with-finish-reason-of-stop/437226/29
    if (message.choices[0]?.finish_reason === 'stop') {
      if (Object.keys(this.tool_call_map).length) {
        // 处理最后一个未写入 generate_elapsed_ms 的 block
        this.onGeneratingToolCallEnd();
        this.assistant_message.status = MessageStatusEnum.ING;
        yield* this.onToolCalls();
      } else {
        yield* this.updateMessageStatus(MessageStatusEnum.DONE);
      }
    }
  }

  protected isAboutToExceedToolCallLimit() {
    const single_tool_exceed_limit = Object.keys(this.call_count).some((tool_name) => {
      if (!this.tools.find((t) => t.function.name === tool_name)) {
        return false;
      }
      return (
        this.call_count[tool_name] + 1 >
        (this.toolCallService.getToolCall(tool_name as TOOL_TYPES)?.tool_definition.max_calls || 0)
      );
    });
    const total_tool_call_exceed_limit =
      Object.values(this.call_count).reduce((accu, count) => accu + count + 1, 0) >
      this.max_call_count;
    return single_tool_exceed_limit || total_tool_call_exceed_limit;
  }

  async *handleStream(
    generateStream?: StreamGeneratorFnSupportingTool,
  ): AsyncGenerator<CompletionStreamChunk<StreamChunkUnion>, void, unknown> {
    this.tool_call_map = {};
    // 首次调用，将 generateStream 闭包并重置 toolcall 调用上限
    // 防止函数复用导致 call_count 累加
    if (generateStream) {
      this.call_count = {};
      this.generateStream = generateStream;
      yield* this.updateMessageStatus(MessageStatusEnum.ING);
      yield* this.fakeReasoning();
    }

    try {
      const will_exceed_limit = this.isAboutToExceedToolCallLimit();
      this.stream = await this.generateStream(this.blocks, will_exceed_limit);
      for await (const message of this.stream) {
        yield* this.onMessage(message);
      }
    } catch (error) {
      yield* this.onError(error as Error);
    } finally {
      await this.flushUpdate(this.blocks);
    }
  }

  private async onGeneratingToolCallEnd() {
    if (this.currentGeneratingToolBlock && this.currentGeneratingToolCallStart) {
      if (!this.currentGeneratingToolBlock.tool_generate_elapsed_ms) {
        this.currentGeneratingToolBlock.tool_generate_elapsed_ms =
          Date.now() - this.currentGeneratingToolCallStart;
      }
    }
  }
}

export { ChatStreamHandler, ToolCallChatStreamHandler };
