import { Injectable, Logger } from '@nestjs/common';
import { ContentHandler } from '@repo/common';
import {
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  type MessageModeEnum,
  MessageRoleEnum,
} from '../../../common/types';
import { MessageDAO } from '../../../dao/chat';
import { type LLMRunner } from '../../../infra/youllm/llm_service/runner';
import type { StreamMessage } from '../../../infra/youllm/types';
import { YouLLMService } from '../../llm';
import { ChatContextBuilder } from '../../llm/context/chat';
import { UserDomainService } from '../../user';
import type { ChatDetail } from '../types';
// import { patchTraceId } from '../util/patchTraceId'; // 已移至方法内
import { ChatAskService } from './ask';
// TODO: 需要迁移ChatAssistantService

@Injectable()
export class AskAIService {
  private readonly logger = new Logger(AskAIService.name);

  constructor(
    private readonly youllm: YouLLMService,
    private readonly userDomain: UserDomainService,
    // TODO: 需要迁移ChatAssistantService
    // private readonly chatAssistantService: ChatAssistantService,
    private readonly chatAskService: ChatAskService,
    private readonly messageDao: MessageDAO,
    private readonly chatContextBuilder: ChatContextBuilder,
  ) {}

  private async *patchTraceId(param: { chat: ChatDetail; runner: LLMRunner }) {
    const { chat, runner } = param;
    // update trace id
    const last_assistant_message = chat.messages.findLast(
      (m) => m.role === MessageRoleEnum.ASSISTANT,
    );
    if (last_assistant_message) {
      last_assistant_message.trace_id = runner.trace.id;
      // TODO: 需要在MessageDAO中实现updateTraceId方法
      await this.messageDao.updateTraceId(
        last_assistant_message.id,
        last_assistant_message.trace_id,
      );
      yield {
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'Message',
        targetId: last_assistant_message.id,
        path: 'trace_id',
        data: last_assistant_message.trace_id,
      } as CompletionStreamReplaceChunk<string>;
    }
  }

  async generateTitle(param: { chat: ChatDetail }) {
    const { chat } = param;
    const user_messages = chat.messages.filter((m) => m.role === MessageRoleEnum.USER);
    const user_message = user_messages.slice(-1)[0];
    if (!user_message) throw new Error('Invalid chat, missing user message');

    const result = await this.youllm.generateTitle(
      {
        questions: JSON.stringify(user_messages.map((m) => m.content)),
      },
      false,
      {
        traceArgs: {
          userId: chat.creator_id,
          metadata: {
            chatId: chat.id,
          },
        },
      },
    );

    return result?.choices?.[0]?.message?.content || '';
  }

  async generateSuggestion(param: { userId: string; content: string; suggestionCount: number }) {
    const { userId, content, suggestionCount } = param;
    if (!content) return [];

    const aiLanguage = await this.userDomain.getPrimaryResponseLanguage({
      user_id: userId,
      content: ContentHandler.fromRaw(content),
    });

    const result = await this.youllm.suggestion(
      {
        aiLanguage,
        content,
        suggestionCount: suggestionCount.toString(),
      },
      false,
    );

    const output = result?.choices?.[0]?.message?.content || '';
    return output
      .split('\n')
      .filter((s) => !!s)
      .map((s) => (s.startsWith('- ') ? s.slice(2).trim() : s.trim()));
  }

  async generateEssence(param: { chat_id: string; regenerate: boolean }) {
    const { chat_id, regenerate } = param;
    const chat_history = await this.chatContextBuilder.getContent(chat_id);
    return (await this.youllm.generateChatEssence(
      {
        chat_history,
      },
      true,
      {
        regenerate,
      },
    )) as AsyncGenerator<StreamMessage>;
  }

  async *respond(param: { chat: ChatDetail; mode: MessageModeEnum; regenerate: boolean }) {
    // TODO: 需要正确初始化WriterLLMRunner，需要注入RedisService
    // TODO: 需要迁移ChatAssistantService
    // 这个方法需要重构以支持NestJS依赖注入模式
    throw new Error(
      'respond method requires WriterLLMRunner and ChatAssistantService to be properly migrated to NestJS',
    );
  }
}
