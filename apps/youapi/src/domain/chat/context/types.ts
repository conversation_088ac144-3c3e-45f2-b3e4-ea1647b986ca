import { LangfuseSpanClient } from 'langfuse-core';
import type { AtReference } from '@/common/types';
import type { ChatDetail, Message } from '@/domain/chat/types';

import { ChunkResult } from '../../search/result';

export type ContextBuildingChunk = Pick<ChunkResult, 'id' | 'type' | 'chunk' | 'distance'> & {
  tokenUsed: number;
  title?: string;
};

export type AtReferenceForContextBuilding =
  | AtReference
  | {
      entity_type: 'library';
    };

/**
 * 构建上下文的请求
 *
 * @example 使用原有方式（向后兼容）:
 * ```typescript
 * const result = await contextBuilder.buildContext({
 *   chat: chatDetail,
 *   availableTokens: 4000,
 *   userId: "user123"
 * });
 * ```
 *
 * @example 直接传入 at_references 和 messages（新方式）:
 * ```typescript
 * const result = await contextBuilder.buildContext({
 *   messages: chatMessages,
 *   at_references: atReferences,
 *   availableTokens: 4000,
 *   userId: "user123"
 * });
 * ```
 */
export interface ContextBuildingRequest {
  /**
   * The user's message content
   * Optional when at_references and messages are provided directly
   */
  chat?: ChatDetail;

  /**
   * Chat messages (alternative to chat.messages)
   */
  messages?: Message[];

  /**
   * At references to resolve (alternative to extracting from chat)
   */
  at_references?: AtReference[];

  /**
   * Available token budget for context
   */
  availableTokens: number;

  /**
   * User ID for permission checks
   */
  userId: string;

  /**
   * Langfuse trace or span
   */
  trace?: LangfuseSpanClient;
}

/**
 * Request for building context
 */
export interface ContextBuildingFromReferencesRequest {
  /**
   * Chat messages
   */
  messages: Message[];

  /**
   * At references to resolve
   */
  at_references: AtReferenceForContextBuilding[];

  /**
   * Available token budget for context
   */
  availableTokens: number;

  /**
   * User ID for permission checks
   */
  userId: string;

  /**
   * Langfuse trace or span
   */
  trace?: LangfuseSpanClient;

  /**
   * Pre-computed message analysis
   * If provided, the analysis step will be skipped
   */
  analysis?: MessageAnalysis;

  /**
   * currently used for web search
   */
  quickMode?: boolean;
}

export enum ContextBuildingStrategy {
  NO_CONTEXT = 'no_context',
  FULL_CONTENT = 'full_content',
  FULL_CONTENT_SAMPLING = 'full_content_sampling',
  SEMANTIC_SEARCH = 'semantic_search',
}

/**
 * Result of context building
 */
export interface ContextBuildingResult {
  atReferencesRetrieved: Array<{
    atReferenceId: string;
    atReferenceType: string;
    chunks: Array<ContextBuildingChunk>;
  }>;
  searchKeywords?: string;
  totalTokensUsed: number;
  strategy: ContextBuildingStrategy;
}

export type MessageAnalyzedContextType = 'full' | 'relevant' | null;

export interface MessageAnalysis {
  needsContext: boolean;
  searchKeywords: string;
  contextType: MessageAnalyzedContextType;
}

export interface RetrievedContent {
  atReferenceId: string;
  atReferenceType: string;
  content: string;
  tokenCount: number;
  chunks: ContextBuildingChunk[];
  searchKeyword?: string;
}

export interface ReferenceAssessment {
  isComplex: boolean;
  estimatedTokens: number;
  title?: string;
}
