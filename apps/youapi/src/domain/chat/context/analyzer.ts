/**
 * Message Analyzer - 消息分析器
 * 分析用户消息以确定是否需要上下文和搜索关键词
 *
 * Migrated from:
 * - youapp/src/domain/chat/context/analyzer.ts
 */

import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { type MessageAtReferenceTypeEnum, MessageRoleEnum } from '../../../common/types';
import { replaceAtReference } from '../../../common/utils/extractAtReference';
import { YouLLMService } from '../../llm';
import type { Message } from '../types';
import type { MessageAnalysis, MessageAnalyzedContextType } from './types';

/**
 * Container content item with type and title
 */
export interface ContainerContentItem {
  type: MessageAtReferenceTypeEnum;
  title: string;
}

export interface MessageAnalyzer {
  analyze(
    messages: Message[],
    containerContents?: Record<string, ContainerContentItem[]>,
    trace?: LangfuseSpanClient,
  ): Promise<MessageAnalysis>;
}

@Injectable()
export class LLMMessageAnalyzer implements MessageAnalyzer {
  private readonly logger = new Logger(LLMMessageAnalyzer.name);

  constructor(
    @Inject(forwardRef(() => YouLLMService))
    private readonly youllmService: YouLLMService,
  ) {}

  async analyze(
    messages: Message[],
    containerContents?: Record<string, ContainerContentItem[]>,
    trace?: LangfuseSpanClient,
  ): Promise<MessageAnalysis> {
    if (messages.length === 0) {
      throw new Error('Messages array cannot be empty');
    }

    const currentUserMessageIndex = messages.findLastIndex((m) => m.role === MessageRoleEnum.USER);
    const currentUserMessage = messages[currentUserMessageIndex];
    if (!currentUserMessage) {
      throw new Error('No user message found');
    }

    const userMessage = replaceAtReference(currentUserMessage.content, (name) => `@${name}`);

    try {
      const relevantMessages = messages.slice(0, currentUserMessageIndex).slice(-5);
      const formattedMessages = relevantMessages
        .map((m) => {
          let content = m.content;

          if (m.role === MessageRoleEnum.ASSISTANT) {
            if (content.length > 200) {
              content = `${content.slice(0, 100)}...${content.slice(-100)}`;
            }
          }

          return `  <${m.role}>${content}</${m.role}>`;
        })
        .join('\n');

      let articleCandidates = '';
      if (containerContents && Object.keys(containerContents).length > 0) {
        articleCandidates = Object.entries(containerContents)
          .flatMap(([, items]) => items)
          .map((item) => `<title>${item.title}</title>`)
          .join('\n');
      } else if ('at_references' in currentUserMessage && currentUserMessage.at_references) {
        articleCandidates = currentUserMessage.at_references
          .map((ref) => `<title>${ref.at_name}</title>`)
          .join('\n');
      }

      // Call the LLM with the prompt
      const response = await this.youllmService.queryAnalyze(
        {
          article_candidates: articleCandidates,
          previous_messages: formattedMessages,
          current_message: `<user>${userMessage}</user>`,
          current_time: new Date().toISOString(),
        },
        false,
        {
          useCache: false,
          promptPrefer: 'local',
        },
      );

      return this.parseAnalysisResponse(
        response?.choices?.[0]?.message?.content ?? '',
        userMessage,
      );
    } catch (error) {
      this.logger.error('Error calling LLM for message analysis:', error);

      return {
        needsContext: true,
        searchKeywords: userMessage,
        contextType: null,
      };
    }
  }

  /**
   * Parse the LLM response into a MessageAnalysis object
   * @param response The LLM response
   * @param originalMessage The original message (for fallback)
   * @param hasAtReferences Whether the message has at-references
   * @returns The parsed MessageAnalysis
   */
  private parseAnalysisResponse(response: string, originalMessage: string): MessageAnalysis {
    try {
      // Try to parse the JSON response
      const jsonResponse = JSON.parse(response.trim());

      // Validate the response has the required fields
      if (
        typeof jsonResponse.needsContext === 'boolean' &&
        typeof jsonResponse.searchKeywords === 'string'
      ) {
        return {
          needsContext: jsonResponse.needsContext,
          searchKeywords: jsonResponse.searchKeywords || originalMessage,
          contextType: jsonResponse.contextType as MessageAnalyzedContextType,
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      this.logger.error('Error parsing LLM analysis response:', error);

      return {
        needsContext: false,
        searchKeywords: '',
        contextType: null,
      };
    }
  }
}
