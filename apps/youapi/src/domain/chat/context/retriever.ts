import { Injectable } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { type AtReference, MessageAtReferenceTypeEnum } from '@/common/types';
import { estimateTokens, runConcurrently } from '@/common/utils';
import { SourceTypeEnum } from '@/domain/chat/types';
import { BaseContextBuilder } from '@/domain/llm/context/base';
import { BoardContextBuilder, BoardGroupContextBuilder } from '@/domain/llm/context/board';
import { SnipContextBuilder } from '@/domain/llm/context/snip';
import { ThoughtContextBuilder } from '@/domain/llm/context/thought';
import { SearchDomainService } from '@/domain/search';
import { ChunkResult } from '@/domain/search/result';
import { ContextBuildingChunk, ReferenceAssessment, RetrievedContent } from './types';

/**
 * Interface for content retrievers
 */
export interface ContentRetriever {
  /**
   * Retrieve content from references
   * @param references References to retrieve content from
   * @param query Query for search
   * @param userId User ID for permission checks
   * @param availableTokens Available token budget
   * @returns Retrieved content
   */
  retrieveContent(
    references: AtReference[],
    query: string,
    userId: string,
    availableTokens: number,
    trace?: LangfuseSpanClient,
  ): Promise<RetrievedContent[]>;
}

// Type for search parameters
interface SearchParams {
  query: string;
  userId: string;
  maxResults: number;
  enrichMetadata: boolean;
  snipIds?: string[];
  thoughtIds?: string[];
  boardIds?: string[];
  boardGroupIds?: string[];
}

/**
 * Default implementation of content retriever
 */
@Injectable()
export class DefaultContentRetriever implements ContentRetriever {
  constructor(
    private readonly snipContextBuilder: SnipContextBuilder,
    private readonly thoughtContextBuilder: ThoughtContextBuilder,
    private readonly boardGroupContextBuilder: BoardGroupContextBuilder,
    private readonly boardContextBuilder: BoardContextBuilder,
    private readonly searchDomain: SearchDomainService,
  ) {}

  private get contextBuilders(): Partial<Record<MessageAtReferenceTypeEnum, BaseContextBuilder>> {
    return {
      [MessageAtReferenceTypeEnum.SNIP]: this.snipContextBuilder,
      [MessageAtReferenceTypeEnum.THOUGHT]: this.thoughtContextBuilder,
      [MessageAtReferenceTypeEnum.BOARD_GROUP]: this.boardGroupContextBuilder,
      [MessageAtReferenceTypeEnum.BOARD]: this.boardContextBuilder,
    };
  }

  private entityTypeToSourceType: Partial<Record<MessageAtReferenceTypeEnum, SourceTypeEnum>> = {
    [MessageAtReferenceTypeEnum.SNIP]: SourceTypeEnum.SNIP,
    [MessageAtReferenceTypeEnum.THOUGHT]: SourceTypeEnum.THOUGHT,
    [MessageAtReferenceTypeEnum.BOARD_GROUP]: SourceTypeEnum.BOARD_GROUP,
    [MessageAtReferenceTypeEnum.BOARD]: SourceTypeEnum.BOARD,
  };

  /**
   * Retrieve content from references
   * @param references References to retrieve content from
   * @param query Query for search
   * @param userId User ID for permission checks
   * @param availableTokens Available token budget
   * @returns Retrieved content
   */
  async retrieveContent(
    references: AtReference[],
    query: string,
    userId: string,
    availableTokens: number,
    trace?: LangfuseSpanClient,
    quickMode?: boolean,
  ): Promise<RetrievedContent[]> {
    if (!references || references.length === 0) {
      return [];
    }

    // 1. Assess references to determine their relevance and size
    const assessments = await this.assessReferences(references);

    // 2. Prioritize references based on type (simple first, then complex)
    const prioritizedReferences = this.prioritizeReferences(references, assessments);

    // 3. Retrieve content with optimized token allocation
    return this.retrieveWithOptimizedAllocation(
      prioritizedReferences,
      assessments,
      query,
      userId,
      availableTokens,
      trace,
      quickMode,
    );
  }

  /**
   * Assess references to determine their relevance and size
   * @param references References to assess
   * @returns Reference assessments
   */
  protected async assessReferences(
    references: AtReference[],
  ): Promise<Map<string, ReferenceAssessment>> {
    const assessments = new Map<string, ReferenceAssessment>();

    await Promise.all(
      references.map(async (reference) => {
        try {
          const isComplex = this.isComplexEntityType(reference.entity_type);
          if (isComplex) {
            assessments.set(reference.entity_id, {
              isComplex,
              estimatedTokens: Infinity,
            });
            return;
          }

          let estimatedTokens = 0;
          let title = '';
          let content = '';

          const contextBuilder = this.contextBuilders[reference.entity_type];
          if (contextBuilder) {
            try {
              const contextObj = await contextBuilder.buildContextObject(reference.entity_id);

              if (contextObj && 'title' in contextObj) {
                title = (contextObj as { title: string }).title || '';
              }

              if (contextObj && 'content' in contextObj) {
                content = (contextObj as { content: string }).content || '';
              }

              estimatedTokens = estimateTokens(content);
            } catch (error) {
              console.error(`Error getting context for ${reference.entity_id}:`, error);
              estimatedTokens = Infinity;
            }
          } else {
            estimatedTokens = Infinity;
          }

          assessments.set(reference.entity_id, {
            isComplex,
            estimatedTokens,
            title,
          });
        } catch (error) {
          console.error(`Error assessing reference: ${reference.entity_id}`, error);
          assessments.set(reference.entity_id, {
            isComplex: true,
            estimatedTokens: Infinity,
          });
        }
      }),
    );

    return assessments;
  }

  /**
   * Prioritize references based on type (simple first, then complex)
   * @param references References to prioritize
   * @param assessments Reference assessments
   * @returns Prioritized references
   */
  protected prioritizeReferences(
    references: AtReference[],
    assessments: Map<string, ReferenceAssessment>,
  ): AtReference[] {
    // Sort references by type (simple first, then complex)
    return [...references].sort((a, b) => {
      const assessmentA = assessments.get(a.entity_id);
      const assessmentB = assessments.get(b.entity_id);

      if (!assessmentA) return 1;
      if (!assessmentB) return -1;

      // First prioritize by complexity (simple first)
      if (assessmentA.isComplex !== assessmentB.isComplex) {
        return assessmentA.isComplex ? 1 : -1;
      }

      // For same complexity level, prioritize by estimated tokens (smaller first)
      // This helps include more references within the token budget
      if (assessmentA.estimatedTokens !== Infinity && assessmentB.estimatedTokens !== Infinity) {
        return assessmentA.estimatedTokens - assessmentB.estimatedTokens;
      }

      return 0;
    });
  }

  /**
   * Retrieve content with optimized token allocation
   * @param prioritizedReferences Prioritized references
   * @param assessments Reference assessments
   * @param query Query for search
   * @param userId User ID for permission checks
   * @param availableTokens Available token budget
   * @returns Retrieved content
   */
  protected async retrieveWithOptimizedAllocation(
    prioritizedReferences: AtReference[],
    assessments: Map<string, ReferenceAssessment>,
    query: string,
    userId: string,
    availableTokens: number,
    trace?: LangfuseSpanClient,
    quickMode?: boolean,
  ): Promise<RetrievedContent[]> {
    const simpleRefs: AtReference[] = [];
    const complexRefs: AtReference[] = [];

    for (const ref of prioritizedReferences) {
      const assessment = assessments.get(ref.entity_id);
      if (!assessment) continue;

      if (assessment.isComplex || assessment.estimatedTokens > availableTokens) {
        complexRefs.push(ref);
      } else {
        simpleRefs.push(ref);
      }
    }

    let simpleTokensNeeded = 0;
    for (const ref of simpleRefs) {
      const assessment = assessments.get(ref.entity_id);
      if (assessment) {
        simpleTokensNeeded += assessment.estimatedTokens;
      }
    }
    const remainingTokens = Math.max(0, availableTokens - simpleTokensNeeded);
    const complexTokenAllocations: number[] = [];
    if (complexRefs.length > 0 && remainingTokens > 0) {
      const tokensPerComplexRef = Math.floor(remainingTokens / complexRefs.length);
      complexRefs.forEach(() => {
        complexTokenAllocations.push(tokensPerComplexRef);
      });
    }

    trace?.event({
      name: 'retrieve-content-with-optimized-allocation',
      level: 'DEBUG',
      metadata: {
        simpleRefs,
        complexRefs,
        complexTokenAllocations,
      },
    });

    const result: RetrievedContent[] = [];

    const simpleRefResults = await runConcurrently(
      simpleRefs.map((ref) => async () => {
        try {
          return await this.retrieveFullContent(ref, userId, query);
        } catch (error) {
          console.error(`Error retrieving full content for ${ref.entity_id}:`, error);
          return null;
        }
      }),
    );

    simpleRefResults
      .filter((content): content is RetrievedContent => content !== null)
      .forEach((content) => {
        result.push(content);
      });

    const complexRefResults = await runConcurrently(
      complexRefs.map((ref, index) => async () => {
        try {
          const tokensForRef = complexTokenAllocations[index];
          if (tokensForRef <= 0) {
            trace?.event({
              name: 'skip-complex-ref',
              level: 'DEBUG',
              metadata: {
                ref,
                tokensForRef,
              },
            });
            return null;
          }

          const span = trace?.span({
            name: 'retrieve-complex-ref',
            input: ref,
            metadata: {
              tokensForRef,
            },
          });
          const searchResult = await this.retrieveWithSearch(
            ref,
            query,
            userId,
            tokensForRef,
            quickMode,
          );

          span?.end({
            output: searchResult,
          });

          return searchResult;
        } catch (error) {
          console.error(`Error retrieving content for ${ref.entity_id}:`, error);
          return null;
        }
      }),
    );

    complexRefResults
      .filter((content): content is RetrievedContent => content !== null)
      .forEach((content) => {
        result.push(content);
      });

    return result;
  }

  /**
   * Convert ChunkResult to ContextBuildingChunk
   * @param chunk The ChunkResult to convert
   * @param tokenUsed The number of tokens used from this chunk
   * @returns A ContextBuildingChunk
   */
  private convertToContextBuildingChunk(
    chunk: ChunkResult,
    tokenUsed: number,
  ): ContextBuildingChunk {
    const { chunk: chunkContent, distance, id, type, title } = chunk;
    return {
      id,
      type,
      title,
      chunk: chunkContent,
      distance,
      tokenUsed,
    };
  }

  /**
   * Retrieve content with search
   * @param reference Reference to retrieve content from
   * @param query Query for search
   * @param userId User ID for permission checks
   * @param maxTokens Maximum tokens to retrieve
   * @returns Retrieved content
   */
  protected async retrieveWithSearch(
    reference: AtReference,
    query: string,
    userId: string,
    maxTokens: number,
    quickMode?: boolean,
  ): Promise<RetrievedContent> {
    const searchParams: SearchParams = {
      query,
      userId,
      maxResults: 20,
      enrichMetadata: true,
    };

    if (reference.entity_type === MessageAtReferenceTypeEnum.SNIP) {
      searchParams.snipIds = [reference.entity_id];
    } else if (reference.entity_type === MessageAtReferenceTypeEnum.THOUGHT) {
      searchParams.thoughtIds = [reference.entity_id];
    } else if (reference.entity_type === MessageAtReferenceTypeEnum.BOARD) {
      searchParams.boardIds = [reference.entity_id];
    } else if (reference.entity_type === MessageAtReferenceTypeEnum.BOARD_GROUP) {
      searchParams.boardGroupIds = [reference.entity_id];
    }

    try {
      const searchResults = quickMode
        ? await this.searchDomain.semanticSearchChunks(searchParams)
        : await this.searchDomain.hybridSearchChunks(searchParams);
      if ((!searchResults || searchResults.length === 0) && !quickMode) {
        return this.retrieveFullContent(reference, userId, query);
      }

      let combinedContent = '';
      let currentTokens = 0;
      const usedChunks: ContextBuildingChunk[] = [];

      for (const result of searchResults) {
        const chunk = result.chunk || '';
        const chunkTokens = estimateTokens(chunk);

        if (currentTokens + chunkTokens > maxTokens) {
          break;
        }

        if (combinedContent) {
          combinedContent += '\n\n---\n\n';
        }
        combinedContent += chunk;
        currentTokens += chunkTokens;

        usedChunks.push(this.convertToContextBuildingChunk(result, chunkTokens));
      }

      if (!combinedContent && searchResults[0] && searchResults[0].chunk) {
        combinedContent = searchResults[0].chunk;
        const chunkTokens = estimateTokens(combinedContent);
        currentTokens = chunkTokens;
        usedChunks.push(this.convertToContextBuildingChunk(searchResults[0], chunkTokens));
      }

      return {
        atReferenceId: reference.entity_id,
        atReferenceType: reference.entity_type,
        content: combinedContent,
        tokenCount: currentTokens,
        chunks: usedChunks,
        searchKeyword: query,
      };
    } catch (error) {
      console.error(`Error searching for content: ${error}`);
      return this.retrieveFullContent(reference, userId, query);
    }
  }

  /**
   * Retrieve full content from a reference
   * @param reference Reference to retrieve content from
   * @param userId User ID for permission checks
   * @param searchKeyword Optional search keyword
   * @returns Retrieved content
   */
  protected async retrieveFullContent(
    reference: AtReference,
    userId: string,
    searchKeyword?: string,
  ): Promise<RetrievedContent> {
    const contextBuilder = this.contextBuilders[reference.entity_type];

    if (!contextBuilder) {
      throw new Error(`No context builder for entity type: ${reference.entity_type}`);
    }

    try {
      const content = await contextBuilder.getContent(reference.entity_id);
      const tokenCount = estimateTokens(content);
      const fullContentChunk: ContextBuildingChunk = {
        id: reference.entity_id,
        type: reference.entity_type,
        chunk: content,
        distance: 0,
        tokenUsed: tokenCount,
      };

      return {
        atReferenceId: reference.entity_id,
        atReferenceType: reference.entity_type,
        content,
        tokenCount,
        chunks: [fullContentChunk],
        searchKeyword,
      };
    } catch (error) {
      console.error(`Error getting full content: ${error}`);
      return {
        atReferenceId: reference.entity_id,
        atReferenceType: reference.entity_type,
        content: `Error retrieving content for ${reference.entity_id}: ${error}`,
        tokenCount: 0,
        chunks: [],
        searchKeyword,
      };
    }
  }

  /**
   * Check if an entity type is complex
   * @param entityType Entity type to check
   * @returns Whether the entity type is complex
   */
  protected isComplexEntityType(entityType: MessageAtReferenceTypeEnum): boolean {
    return (
      entityType === MessageAtReferenceTypeEnum.BOARD_GROUP ||
      entityType === MessageAtReferenceTypeEnum.BOARD
    );
  }
}
