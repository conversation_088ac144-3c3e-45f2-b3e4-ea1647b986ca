/**
 * @migration Migrated from Next.js to NestJS
 */

import { Injectable, Logger } from '@nestjs/common';
import { customAlphabet } from 'nanoid';

import type { EntityTypeEnum } from '../../common/types';
import { ShortLinkDAO } from '../../dao/short-link';
import {
  InsertShortLinkDOParam,
  ShortLinkDO,
  UpdateShortLinkDOParam,
} from '../../dao/short-link/types';

import type { ShortLink } from './types';

// 使用大小写字母和数字生成 14 位的短 ID
const generateShortId = customAlphabet(
  '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
  14,
);

@Injectable()
export class ShortLinkDomainService {
  private readonly logger = new Logger(ShortLinkDomainService.name);

  constructor(private readonly shortLinkDAO: ShortLinkDAO) {}
  async create(entityType: EntityTypeEnum, entityId: string): Promise<ShortLink> {
    // 先查询是否已存在
    const existing = await this.shortLinkDAO.tryGetByEntity(entityType, entityId);
    if (existing) {
      return this.do2entity(existing);
    }

    // 生成短 ID，确保唯一性
    let shortId: string;
    let existingByShortId;
    do {
      shortId = generateShortId();
      existingByShortId = await this.shortLinkDAO.tryGetByShortId(shortId);
    } while (existingByShortId);

    // 创建短链接
    const result = await this.shortLinkDAO.insert({
      short_id: shortId,
      entity_type: entityType,
      entity_id: entityId,
      active: true,
    } as InsertShortLinkDOParam);

    return this.do2entity(result);
  }

  async tryGetByShortId(shortId: string): Promise<ShortLink | undefined> {
    const result = await this.shortLinkDAO.tryGetByShortId(shortId);
    return result ? this.do2entity(result) : undefined;
  }

  async tryGetByEntity(
    entityType: EntityTypeEnum,
    entityId: string,
  ): Promise<ShortLink | undefined> {
    const result = await this.shortLinkDAO.tryGetByEntity(entityType, entityId);
    return result ? this.do2entity(result) : undefined;
  }

  async activate(id: string): Promise<ShortLink> {
    const result = await this.shortLinkDAO.update(id, {
      active: true,
    } as UpdateShortLinkDOParam);
    return this.do2entity(result);
  }

  async deactivate(id: string): Promise<ShortLink> {
    const result = await this.shortLinkDAO.update(id, {
      active: false,
    } as UpdateShortLinkDOParam);
    return this.do2entity(result);
  }

  private do2entity(shortLinkDO: ShortLinkDO): ShortLink {
    return {
      ...shortLinkDO,
      entity_type: shortLinkDO.entity_type as EntityTypeEnum,
    };
  }
}
