/**
 * @migration Migrated from Next.js to NestJS
 */

import { Injectable, Logger } from '@nestjs/common';
import type { PlaylistItemStatusEnum as CommonPlaylistItemStatusEnum } from '../../common/types';
import { PlaylistItemDAO } from '../../dao/playlist-item';
import {
  PlaylistItemDO,
  PlaylistItemUpdateDO,
  PlaylistItemVO,
} from '../../dao/playlist-item/types';
import { type PlaylistItemSourceEntityTypeEnum, PlaylistItemStatusEnum } from './types';

@Injectable()
export class PlaylistItemDomainService {
  private readonly logger = new Logger(PlaylistItemDomainService.name);

  constructor(private readonly playlistItemDAO: PlaylistItemDAO) {}

  async createPlaylistItem(param: {
    creator_id: string;
    space_id: string;
    board_id?: string;
    entity_type: PlaylistItemSourceEntityTypeEnum;
    entity_id?: string;
  }) {
    const playlistItemDO = await this.playlistItemDAO.insert({
      creator_id: param.creator_id,
      space_id: param.space_id,
      status: PlaylistItemStatusEnum.GENERATING,
      title: '',
      play_url: '',
    });

    return this.do2entity(playlistItemDO, param.board_id, param.entity_type, param.entity_id);
  }

  async patchPlaylistItem(param: {
    id: string;
    status?: PlaylistItemStatusEnum;
    title?: string;
    play_url?: string;
    album_cover_url?: string;
    transcript?: string;
    rank?: string;
    playback_progress?: number;
    duration?: number;
  }) {
    const playlistItemDO = await this.playlistItemDAO.update(param.id, {
      status: param.status,
      title: param.title,
      play_url: param.play_url,
      album_cover_url: param.album_cover_url,
      transcript: param.transcript,
      rank: param.rank,
      playback_progress: param.playback_progress,
      duration: param.duration,
    } as PlaylistItemUpdateDO);

    return this.do2entity(playlistItemDO);
  }

  async deletePlaylistItem(id: string) {
    await this.playlistItemDAO.delete(id);
  }

  async deleteManyPlaylistItems(ids: string[]) {
    await this.playlistItemDAO.deleteMany(ids);
  }

  async getPlaylistItem(id: string) {
    const playlistItemDO = await this.playlistItemDAO.selectById(id);
    return this.do2entity(playlistItemDO);
  }

  async listPlaylistItems(param: { space_id: string; board_id?: string }) {
    const playlistItems = await this.playlistItemDAO.select({
      space_id: param.space_id,
      board_id: param.board_id || '',
    });
    return playlistItems.map((item) => this.do2entity(item));
  }

  private do2entity(
    playlistItemDO: PlaylistItemDO,
    board_id?: string,
    entity_type?: PlaylistItemSourceEntityTypeEnum,
    entity_id?: string,
  ): PlaylistItemVO {
    return {
      id: playlistItemDO.id,
      created_at: playlistItemDO.created_at,
      updated_at: playlistItemDO.updated_at,
      creator_id: playlistItemDO.creator_id,
      space_id: playlistItemDO.space_id,
      board_id: board_id,
      entity_type: entity_type,
      entity_id: entity_id,
      title: playlistItemDO.title,
      play_url: playlistItemDO.play_url,
      duration: playlistItemDO.duration ?? 0,
      status: playlistItemDO.status as any as CommonPlaylistItemStatusEnum,
      album_cover_url: playlistItemDO.album_cover_url ?? undefined,
      transcript: playlistItemDO.transcript ?? undefined,
      rank: playlistItemDO.rank ?? undefined,
      playback_progress: playlistItemDO.playback_progress ?? undefined,
    };
  }
}
