/**
 * Transform business entity to a typesense search document
 * @see https://typesense.org/docs/27.1/api/documents.html
 */

import { ContentVO } from '@repo/common';
import { ChunkingStrategy } from '@/common/utils';
import { Article, Image, Snip } from '../snip';
import { SnipTypeEnum, type UploadFileMeta, type WebpageMeta } from '../snip/types';
import type { Thought } from '../thought/types';
import {
  AllFieldsNeedLocalize,
  type BaseMetaFields,
  DEFAULT_LOCALE,
  type EmbeddingsTableItem,
  type EntityToBeIndexed,
  FieldsNeedSplit,
  isValidLocale,
  type Locales,
  SnipFieldsNeedLocalize,
  type SnipSearchDocument,
  ThoughtFieldsNeedLocalize,
  type ThoughtSearchDocument,
} from './definition';
import {
  addCJKSpacing,
  detectLang,
  parsePlainContent,
  preprocessText,
  toLocalizedFieldNames,
} from './utils';

export async function toSearchDocument({
  entity,
  docType,
}: {
  entity: EntityToBeIndexed;
  docType?: 'snip' | 'thought';
}): Promise<Partial<SnipSearchDocument | ThoughtSearchDocument>[]> {
  if (docType === 'snip') {
    return toSnipSearchDocument({ entity });
  }
  return toThoughtSearchDocument({ entity });
}

export async function toEmbeddingsTableItem<T extends object = Snip | Thought>({
  entity,
  userId,
  docType,
}: {
  entity: T;
  userId?: string;
  docType: 'snip' | 'thought';
}): Promise<EmbeddingsTableItem[]> {
  // Collect all string contents to embed
  const contentsToEmbed: { field: string; text: string }[] = [];
  const fieldsToProcess = docType === 'snip' ? SnipFieldsNeedLocalize : ThoughtFieldsNeedLocalize;

  for (const fieldName of fieldsToProcess) {
    if (fieldName in entity) {
      const content = entity[fieldName as keyof T] as unknown as ContentVO | ContentVO[] | string;

      if (Array.isArray(content)) {
        if (content.length === 0) {
          continue;
        }

        for (const item of content) {
          const { plain = '' } = item;
          if (plain.trim() === '') {
            continue;
          }
          contentsToEmbed.push({
            field: fieldName,
            text: plain,
          });
        }
      } else {
        let finalContent = content;
        if (typeof content === 'object' && content !== null && 'plain' in content) {
          finalContent = content?.plain || '';
        }

        if (typeof finalContent !== 'string') {
          console.warn(
            `[search] field ${fieldName} is not a string, cannot convert to search document, skip it`,
          );
          continue;
        }

        contentsToEmbed.push({
          field: fieldName,
          text: finalContent,
        });
      }
    }
  }

  // Chunk contents into smaller pieces
  const chunkedContents: {
    original_field: string;
    chunk: string;
    chunk_index: number;
  }[] = [];
  for (const content of contentsToEmbed) {
    const chunks = await parsePlainContent({
      content: content.text,
      fieldName: content.field as FieldsNeedSplit,
      chunkingStrategy: ChunkingStrategy.SINGLE_CHUNK_TOKEN_SIZE,
    });
    for (const [, chunk] of chunks.entries()) {
      chunkedContents.push({
        chunk_index: chunk.chunkIndex,
        chunk: chunk.text,
        original_field: chunk.originalField,
      });
    }
  }

  // Create embeddings
  // TODO: Fix this to use dependency injection - YouLLMService should be injected
  // For now, we'll skip embedding creation in this utility function
  // This should be handled in the calling service that has access to YouLLMService
  const embeddings: number[][] = chunkedContents.map(() => []);

  // Create embedding documents
  const tableItems: EmbeddingsTableItem[] = [];
  for (let index = 0; index < embeddings.length; index++) {
    const embedding = embeddings[index];
    tableItems.push({
      id: (entity as Snip).id!,
      creator_id: (entity as Snip).creator_id,
      space_id: (entity as Snip).space_id,
      type: docType,
      original_field: chunkedContents[index].original_field,
      chunk: chunkedContents[index].chunk,
      chunk_index: chunkedContents[index].chunk_index,
      embedding,
      updated_at: new Date(
        (entity as BaseMetaFields).updated_at?.valueOf() || Date.now(),
      ).toISOString(),
    });
  }

  return tableItems;
}

async function toSnipSearchDocument({ entity }: { entity: Partial<Snip> }) {
  // Base fields
  let document: Partial<SnipSearchDocument> = {
    id: entity.id!,
    space_id: entity.space_id,
    creator_id: entity.creator_id,
    type: entity.type,
    from: entity.from,
    updated_at: entity.updated_at?.valueOf() || Date.now(),
  };

  patchDescription(entity);
  patchFileTitle(entity);
  patchTitleMixedWithCJK(entity);

  if ('published_at' in entity) {
    document.published_at = entity.published_at?.valueOf() as number;
  }

  if ('authors' in entity) {
    document.authors = (entity as Article).authors?.map((author) => author.name);

    document.author_pictures = (entity as Article).authors
      ?.map((author) => author.picture)
      .filter((picture) => picture !== undefined);
  }

  if ('hero_image_url' in entity) {
    document.hero_image_url = (entity as Article).hero_image_url;
  }

  // Set hero_image_url for image snip for better search result preview
  if (!document.hero_image_url && document.type === SnipTypeEnum.IMAGE) {
    const file = (entity as Image).file;
    if ('storage_url' in file && file.storage_url.length > 0) {
      // Note: fileDomain should be injected as a service in NestJS context
      // For now, we'll use a placeholder - this needs to be fixed when integrating with DI
      // Simplified for migration
      document.hero_image_url = file.storage_url;
    } else if ('original_url' in file && file.original_url.length > 0) {
      document.hero_image_url = file.original_url;
    }
  }

  if ('webpage' in entity && (entity as Article).webpage?.url) {
    document['webpage.url'] = (entity as Article).webpage!.url;
  }

  if ('webpage' in entity && (entity as Article).webpage?.site?.favicon_url) {
    document['webpage.favicon_url'] = (entity as Article).webpage!.site.favicon_url;
  }

  if ('webpage' in entity && (entity as Article).webpage?.site?.name) {
    document['webpage.site_name'] = (entity as Article).webpage!.site.name;
  }

  for (const fieldName of SnipFieldsNeedLocalize) {
    if (fieldName in entity) {
      const content = entity[fieldName as keyof Snip] as unknown as
        | ContentVO
        | ContentVO[]
        | string;

      let localizedFields: Partial<SnipSearchDocument> = {};
      if (Array.isArray(content) && content.length > 0) {
        for (const item of content) {
          const { plain = '', language } = item;
          const locale = language ? language.split('-')[0] : DEFAULT_LOCALE;
          if (!isValidLocale(locale)) {
            console.warn(`[search] locale ${locale} is not supported, skipping`);
            continue;
          }

          const parsedContents = await parsePlainContent({
            content: plain,
            fieldName: fieldName as FieldsNeedSplit,
            locale: locale as Locales,
          });

          const parsedFields = await toLocalizedSearchDocument(
            ...parsedContents.flatMap((content) => [content.name, content.text]),
          );

          localizedFields = {
            ...localizedFields,
            ...parsedFields,
          };
        }
      } else {
        let finalContent = (content as string) || '';
        if (typeof content === 'object' && content !== null && 'plain' in content) {
          finalContent = content?.plain || '';
        }

        if (typeof finalContent !== 'string') {
          console.warn(
            `[search] field ${fieldName} is not a string, cannot convert to search document, skip it`,
          );
          continue;
        }

        localizedFields = (await toLocalizedSearchDocument(
          fieldName,
          finalContent,
        )) as Partial<SnipSearchDocument>;
      }

      document = { ...document, ...localizedFields };
    }
  }

  return [document];
}

async function toThoughtSearchDocument({ entity }: { entity: Partial<Thought> }) {
  // Base fields
  let document: BaseMetaFields = {
    id: entity.id!,
    space_id: entity.space_id!,
    creator_id: entity.creator_id!,
    updated_at: entity.updated_at?.valueOf() || Date.now(),
  };

  patchTitleMixedWithCJK(entity);

  for (const fieldName of ThoughtFieldsNeedLocalize) {
    if (fieldName in entity) {
      const content = entity[fieldName as keyof Thought] as unknown as
        | ContentVO
        | ContentVO[]
        | string;

      let localizedFields: Partial<ThoughtSearchDocument> = {};
      if (Array.isArray(content) && content.length > 0) {
        for (const item of content) {
          const { plain = '', language } = item;
          const locale = language ? language.split('-')[0] : DEFAULT_LOCALE;
          if (!isValidLocale(locale)) {
            console.warn(`[search] locale ${locale} is not supported, skipping`);
            continue;
          }

          const parsedContents = await parsePlainContent({
            content: plain,
            fieldName: fieldName as FieldsNeedSplit,
            locale: locale as Locales,
          });

          const parsedFields = await toLocalizedSearchDocument(
            ...parsedContents.flatMap((content) => [content.name, content.text]),
          );

          localizedFields = {
            ...localizedFields,
            ...parsedFields,
          };
        }
      } else {
        let finalContent = (content as string) || '';
        if (typeof content === 'object' && content !== null && 'plain' in content) {
          finalContent = content?.plain || '';
        }

        if (typeof finalContent !== 'string') {
          console.warn(
            `[search] field ${fieldName} is not a string, cannot convert to search document, skip it`,
          );
          continue;
        }

        localizedFields = (await toLocalizedSearchDocument(
          fieldName,
          finalContent,
        )) as Partial<SnipSearchDocument>;
      }

      document = { ...document, ...localizedFields };
    }
  }

  return [document];
}

/**
 * Handle fields that need localization when transforming to a search document
 */
async function toLocalizedSearchDocument(
  ...fieldNamesAndTexts: string[]
): Promise<Partial<SnipSearchDocument | ThoughtSearchDocument>> {
  const document: Record<string, string> = {};

  const fields = fieldNamesAndTexts.reduce(
    (prev, curr, index) => {
      if (index % 2 === 0) {
        prev.push({ name: curr, text: '' });
      } else {
        prev[prev.length - 1].text = curr;
      }
      return prev;
    },
    [] as { name: string; text: string }[],
  );

  for (const item of fields) {
    if (!AllFieldsNeedLocalize.includes(item.name as AllFieldsNeedLocalize)) {
      (document[item.name as AllFieldsNeedLocalize] as string) = item.text;
      continue;
    }

    if (FieldsNeedSplit.includes(item.name as FieldsNeedSplit)) {
      const paragraphs = await parsePlainContent({
        content: item.text,
        fieldName: item.name as FieldsNeedSplit,
      });
      for (const paragraph of paragraphs) {
        (document[paragraph.name as FieldsNeedSplit] as string) = paragraph.text;
      }
    } else {
      const preprocessedContent = await preprocessText(item.text);
      const lang = detectLang(preprocessedContent);
      const localizedFieldName = toLocalizedFieldNames(item.name, lang)[0];
      (document[localizedFieldName as keyof SnipSearchDocument] as string) = preprocessedContent;
    }
  }

  return document;
}

// @ref YOU-677
function patchDescription(entity: Partial<Snip> & { description?: string }) {
  if ('webpage' in entity && entity.webpage && 'description' in (entity.webpage as WebpageMeta)) {
    const description = entity.description || '';
    const webpageDescription = (entity.webpage as WebpageMeta).description;
    if (description && webpageDescription) {
      entity.description = `${description} ${webpageDescription}`;
    } else if (description) {
      entity.description = description;
    } else if (webpageDescription) {
      entity.description = webpageDescription;
    }
  }
}

// @ref YOU-2481
function patchFileTitle(entity: Partial<Snip> & { file?: UploadFileMeta }) {
  if (entity.title) {
    return;
  }

  if ('file' in entity && entity.file && 'name' in entity.file) {
    entity.title = entity.file.name;
  }
}

// @ref YOU-3280
function patchTitleMixedWithCJK(entity: Partial<Snip> & { title?: string }) {
  if (entity.title && typeof entity.title === 'string') {
    entity.title = addCJKSpacing(entity.title);
  }
}
