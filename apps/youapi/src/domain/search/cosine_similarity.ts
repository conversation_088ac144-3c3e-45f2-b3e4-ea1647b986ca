import { Tiktoken } from 'js-tiktoken/lite';

const cl100k_base = require('js-tiktoken/ranks/cl100k_base');

import { ChunkingStrategy, chunkText } from '@/common/utils';

interface ChunkMatch {
  chunk: string;
  similarity: number;
}

interface SparseVector {
  [key: string]: number;
}

/**
 * A min-heap implementation to maintain top-K similar chunks efficiently
 */
class TopKHeap {
  private heap: ChunkMatch[];
  private k: number;
  private queryVector: SparseVector;
  private encoder: Tiktoken;

  constructor(k: number, queryText: string) {
    if (k <= 0) {
      throw new Error('k must be a positive integer');
    }
    this.k = k;
    this.heap = [];
    this.encoder = new Tiktoken(cl100k_base);

    // Normalize and encode query once
    const normalizedQuery = this.normalizeText(queryText);
    const queryTokens = Array.from(this.encoder.encode(normalizedQuery));
    this.queryVector = this.getTokenFrequencies(queryTokens);
  }

  // Get minimum similarity in our top-k
  getMinSimilarity(): number {
    return this.heap.length < this.k ? -Infinity : this.heap[0].similarity;
  }

  // Try to add a new chunk
  tryAdd(chunk: string, similarity: number): boolean {
    this.insert({ chunk, similarity });
    return true;
  }

  // Process a new chunk with proper vector weights
  processChunk(chunk: string): boolean {
    // Normalize chunk text
    const normalizedChunk = this.normalizeText(chunk);
    const chunkTokens = Array.from(this.encoder.encode(normalizedChunk));

    // Skip empty chunks
    if (chunkTokens.length === 0) {
      return false;
    }

    const chunkVector = this.getTokenFrequencies(chunkTokens);
    const similarity = this.computeCosineSimilarity(this.queryVector, chunkVector);

    return this.tryAdd(chunk, similarity);
  }

  private insert(item: ChunkMatch): void {
    this.heap.push(item);
    this.siftUp(this.heap.length - 1);
  }

  private siftUp(index: number): void {
    while (index > 0) {
      const parentIndex = Math.floor((index - 1) / 2);
      if (this.heap[index].similarity < this.heap[parentIndex].similarity) {
        [this.heap[index], this.heap[parentIndex]] = [this.heap[parentIndex], this.heap[index]];
        index = parentIndex;
      } else {
        break;
      }
    }
  }

  private siftDown(index: number): void {
    const length = this.heap.length;
    let currentIndex = index;

    while (currentIndex < length) {
      let smallest = currentIndex;
      const leftChild = 2 * currentIndex + 1;
      const rightChild = 2 * currentIndex + 2;

      if (leftChild < length && this.heap[leftChild].similarity < this.heap[smallest].similarity) {
        smallest = leftChild;
      }

      if (
        rightChild < length &&
        this.heap[rightChild].similarity < this.heap[smallest].similarity
      ) {
        smallest = rightChild;
      }

      if (smallest !== currentIndex) {
        [this.heap[currentIndex], this.heap[smallest]] = [
          this.heap[smallest],
          this.heap[currentIndex],
        ];
        currentIndex = smallest;
      } else {
        break;
      }
    }
  }

  private normalizeText(text: string): string {
    return (
      text
        .toLowerCase()
        // Replace HTML entities
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        // Normalize whitespace
        .replace(/\s+/g, ' ')
        .trim()
    );
  }

  private getTokenFrequencies(tokens: number[]): SparseVector {
    const frequencies: SparseVector = {};

    // Group similar tokens and apply TF-IDF like weighting
    const normalizedTokens = tokens
      .map((token) => {
        const text = this.encoder.decode([token]);
        const normalized = text.toLowerCase().trim();
        // Skip very short tokens and common words
        if (normalized.length < 2) {
          return null;
        }
        return Array.from(this.encoder.encode(normalized))[0];
      })
      .filter((t): t is number => t !== null);

    // Calculate term frequencies
    for (const token of normalizedTokens) {
      const tokenStr = String(token);
      frequencies[tokenStr] = (frequencies[tokenStr] || 0) + 1;
    }

    // Apply log normalization to frequencies to prevent bias towards longer texts
    for (const token in frequencies) {
      frequencies[token] = 1 + Math.log(frequencies[token]);
    }

    return frequencies;
  }

  private computeCosineSimilarity(vec1: SparseVector, vec2: SparseVector): number {
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    // Compute dot product and norms with position-based weighting
    const allTokens = new Set([...Object.keys(vec1), ...Object.keys(vec2)]);

    for (const token of allTokens) {
      const weight1 = vec1[token] || 0;
      const weight2 = vec2[token] || 0;

      // Apply positional weighting - tokens that appear in both vectors get boosted
      const boost = weight1 > 0 && weight2 > 0 ? 1.5 : 1;

      if (weight1 > 0) norm1 += weight1 * weight1;
      if (weight2 > 0) norm2 += weight2 * weight2;

      dotProduct += weight1 * weight2 * boost;
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }

  // Get results sorted by similarity (descending)
  getResults(): ChunkMatch[] {
    return [...this.heap].sort((a, b) => b.similarity - a.similarity).slice(0, this.k);
  }
}

/**
 * Finds the top K most similar chunks in a document compared to a query
 * @param query - The query text to compare against
 * @param document - The document to chunk and compare
 * @param k - Number of most similar chunks to return (default: 3)
 * @param maxTokens - Maximum tokens per chunk (default: 256)
 * @returns Array of chunks with their similarity scores, sorted by similarity
 */
const findTopKSimilarChunks = async (
  query: string,
  document: string,
  k: number = 3,
  maxTokens: number = 256,
): Promise<ChunkMatch[]> => {
  if (!query.trim() || !document.trim()) {
    return [];
  }

  if (k <= 0) {
    throw new Error('k must be a positive integer');
  }

  const chunks = await chunkText({
    content: document,
    maxTokenPerChunk: maxTokens,
    chunkingStrategy: ChunkingStrategy.SINGLE_CHUNK_TOKEN_SIZE,
  });
  const heap = new TopKHeap(k, query);

  for (const chunk of chunks) {
    heap.processChunk(chunk.text);
  }

  return heap.getResults();
};

export { findTopKSimilarChunks, type ChunkMatch };
