/**
 * Search Query Builder Service - 搜索查询构建服务
 * 构建 Typesense 搜索查询的 NestJS 服务
 *
 * Migrated from:
 * - youapp/src/domain/search/query.ts
 *
 * @see https://typesense.org/docs/27.1/api/search.html
 */

import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { SearchParams } from 'typesense/lib/Typesense/Documents';
import { MultiSearchRequestSchema } from 'typesense/lib/Typesense/MultiSearch';
import { BoardItemDomainService } from '../board-item';
import { TypesenseCollection } from './definition';
import { SCHEMA_MAP } from './schema';
import { detectLocale, toLocalizedFieldNames } from './utils';

interface FieldInfo {
  name: string;
  weight: number;
}

interface CollectionSearchParam {
  collectionName: string;
  fieldNames: FieldInfo[];
}

@Injectable()
export class SearchQueryService {
  private readonly logger = new Logger(SearchQueryService.name);

  constructor(
    @Inject(forwardRef(() => BoardItemDomainService))
    private readonly boardItemDomain: BoardItemDomainService,
  ) {}

  /**
   * 解析字段到集合参数
   * Parse field to collection parameter
   */
  private parseFieldToCollectionParam(field: string, queryLang?: string): CollectionSearchParam {
    const [collectionName = TypesenseCollection.SNIPS, ...fieldNameParts] = field.split('.');
    const fieldName = fieldNameParts.join('.');
    const schema = SCHEMA_MAP[collectionName as TypesenseCollection];
    const localizedFieldNames = toLocalizedFieldNames(fieldName, queryLang);

    return {
      collectionName,
      fieldNames: localizedFieldNames.map((localizedFieldName) => {
        const fieldSchema = schema.find((schema) => schema.name === localizedFieldName);

        if (!fieldSchema) {
          throw new Error(
            `Field ${localizedFieldName} not found in schema, cannot build search query`,
          );
        }

        return {
          name: localizedFieldName,
          weight: (fieldSchema?.weight as number) || 1,
        };
      }),
    };
  }

  /**
   * 合并集合参数
   * Merge collection parameters
   */
  private mergeCollectionParams(queries: CollectionSearchParam[]): CollectionSearchParam[] {
    return queries.reduce((merged, current) => {
      const existing = merged.find((item) => item.collectionName === current.collectionName);

      if (existing) {
        existing.fieldNames.push(...current.fieldNames);
      } else {
        merged.push(current);
      }

      return merged;
    }, [] as CollectionSearchParam[]);
  }

  /**
   * 获取过滤后的ID列表
   * Get filtered IDs based on boards and collections
   */
  private async getFilteredIds(params: {
    collectionName: TypesenseCollection;
    boardIds?: string[];
    snipIds?: string[];
    thoughtIds?: string[];
    boardGroupIds?: string[];
  }): Promise<string[] | undefined> {
    const { collectionName, boardIds, snipIds, thoughtIds, boardGroupIds } = params;

    // 如果没有 boardIds 和 boardGroupIds，直接返回集合特定的 IDs
    if (!boardIds?.length && !boardGroupIds?.length) {
      return collectionName === TypesenseCollection.SNIPS ? snipIds : thoughtIds;
    }

    // 从 boards 和 board groups 获取 IDs
    const entityType = collectionName === TypesenseCollection.SNIPS ? 'snip' : 'thought';

    // 如果同时存在 boardIds 和 boardGroupIds，并行获取实体 IDs
    let boardEntityIds: (string | null)[] = [];
    let boardGroupEntityIds: (string | null)[] = [];

    const fetchPromises: Promise<(string | null)[]>[] = [];

    if (boardIds?.length) {
      fetchPromises.push(
        this.boardItemDomain.getEntityIdsByBoardIds(entityType as 'snip' | 'thought', boardIds),
      );
    }

    if (boardGroupIds?.length) {
      fetchPromises.push(
        this.boardItemDomain.getEntityIdsByBoardGroupIds(
          entityType as 'snip' | 'thought',
          boardGroupIds,
        ),
      );
    }

    const results = await Promise.all(fetchPromises);

    // 根据执行的 promises 合并结果
    if (boardIds?.length && boardGroupIds?.length) {
      // 两个都被获取了
      boardEntityIds = results[0];
      boardGroupEntityIds = results[1];
    } else if (boardIds?.length) {
      // 只获取了 boardIds
      boardEntityIds = results[0];
    } else if (boardGroupIds?.length) {
      // 只获取了 boardGroupIds
      boardGroupEntityIds = results[0];
    }

    // 合并所有实体 IDs
    const allEntityIds = [...boardEntityIds, ...boardGroupEntityIds];

    if (!allEntityIds.length) {
      // 如果在 boards 或 board groups 中没有找到实体，但有集合特定的 IDs，返回这些 IDs
      const collectionIds = collectionName === TypesenseCollection.SNIPS ? snipIds : thoughtIds;
      return collectionIds?.length ? collectionIds : undefined;
    }

    // 过滤掉实体 IDs 中的 null 值
    const validEntityIds = allEntityIds.filter((id): id is string => id !== null);

    // 如果有集合特定的 IDs，将它们与实体 IDs 合并
    const collectionIds = collectionName === TypesenseCollection.SNIPS ? snipIds : thoughtIds;
    if (collectionIds?.length) {
      // 使用 Set 去重 IDs
      const combinedIds = [...new Set([...validEntityIds, ...collectionIds])];
      return combinedIds;
    }

    return validEntityIds;
  }

  /**
   * 构建集合搜索查询
   * Build collection search query
   */
  private async buildCollectionSearchQuery(
    collection: CollectionSearchParam,
    params: {
      userId: string;
      query?: string;
      boardIds?: string[];
      snipIds?: string[];
      thoughtIds?: string[];
      boardGroupIds?: string[];
    },
  ): Promise<MultiSearchRequestSchema | undefined> {
    const { collectionName, fieldNames } = collection;
    const { userId, query: searchQuery, boardIds, snipIds, thoughtIds, boardGroupIds } = params;

    // 基于 boards 和集合特定 IDs 获取过滤后的 IDs
    const filteredIds = await this.getFilteredIds({
      collectionName: collectionName as TypesenseCollection,
      boardIds,
      snipIds,
      thoughtIds,
      boardGroupIds,
    });

    // 如果有 boardIds 但没有找到匹配的实体，跳过此查询
    if (boardIds?.length && filteredIds === undefined) {
      return undefined;
    }

    if (collectionName === TypesenseCollection.SNIPS) {
      return this.buildSnipSearchQuery({
        userId,
        fields: fieldNames,
        query: searchQuery,
        ids: filteredIds,
      });
    }

    if (collectionName === TypesenseCollection.THOUGHTS) {
      return this.buildThoughtSearchQuery({
        userId,
        fields: fieldNames,
        query: searchQuery,
        ids: filteredIds,
      });
    }

    throw new Error(`Unsupported collection ${collectionName}, cannot build search query`);
  }

  /**
   * 解析带有精确匹配的查询
   * Parse query with exact matches (quoted phrases)
   */
  private parseQueryWithExactMatches(query?: string): string[] {
    if (!query || query.trim() === '') {
      return [];
    }

    const parsedQueries: string[] = [];
    let remainingTerms = '';

    // 匹配双引号内文本的正则表达式
    const regex = /("[^"]*")/g;
    let match;
    let lastIndex = 0;

    // 提取所有引用的短语
    while ((match = regex.exec(query)) !== null) {
      // 将引用短语前的文本添加到 remainingTerms
      const textBefore = query.substring(lastIndex, match.index).trim();
      if (textBefore) {
        remainingTerms += (remainingTerms ? ' ' : '') + textBefore;
      }

      // 将引用的短语作为单独的查询添加（不包括引号）
      if (match[1].trim()) {
        parsedQueries.push(match[1]);
      }

      lastIndex = regex.lastIndex;
    }

    // 添加最后一个引用短语后的任何剩余文本
    const textAfter = query.substring(lastIndex).trim();
    if (textAfter) {
      remainingTerms += (remainingTerms ? ' ' : '') + textAfter;
    }

    // 如果不为空，将剩余术语作为单独的查询添加
    if (remainingTerms) {
      parsedQueries.push(remainingTerms);
    }

    return parsedQueries;
  }

  /**
   * 构建搜索查询
   * Build search queries for multiple collections
   */
  async buildSearchQueries({
    fields,
    queryLang,
    userId,
    query,
    boardIds,
    snipIds,
    thoughtIds,
    boardGroupIds,
  }: {
    fields: string[];
    queryLang?: string;
    userId?: string;
    query?: string;
    boardIds?: string[];
    snipIds?: string[];
    thoughtIds?: string[];
    boardGroupIds?: string[];
  }): Promise<MultiSearchRequestSchema[]> {
    try {
      // 将每个字段解析为集合信息
      const collectionParams = fields.map((field) =>
        this.parseFieldToCollectionParam(field, queryLang),
      );

      // 合并同一集合的查询
      const mergedParams = this.mergeCollectionParams(collectionParams);

      // 为每个解析的查询文本构建搜索查询
      const allQueries: MultiSearchRequestSchema[] = [];

      // 为此查询文本构建最终搜索查询
      const queries = (
        await Promise.all(
          mergedParams.map((collectionParam) =>
            this.buildCollectionSearchQuery(collectionParam, {
              userId: userId || '',
              query,
              boardIds,
              snipIds,
              thoughtIds,
              boardGroupIds,
            }),
          ),
        )
      ).filter((query): query is MultiSearchRequestSchema => query !== undefined);

      allQueries.push(...queries);

      this.logger.debug(`Built ${allQueries.length} search queries for user ${userId}`);
      return allQueries;
    } catch (error) {
      this.logger.error('Failed to build search queries', error);
      throw error;
    }
  }

  /**
   * 构建 Snip 搜索查询
   * Build Snip search query
   */
  private async buildSnipSearchQuery({
    userId,
    fields,
    query,
    ids,
  }: {
    userId: string;
    fields: { name: string; weight: number }[];
    query?: string;
    ids?: string[];
  }): Promise<MultiSearchRequestSchema> {
    const locale = detectLocale(query || '');
    const needStopwords = (query?.split(' ') || []).length > 1;

    const filter_by = `creator_id := ${userId}${ids?.length ? ` && id:[${ids.join(',')}]` : ''}`;

    return {
      q: query,
      collection: TypesenseCollection.SNIPS,
      filter_by,
      query_by: fields.map((q) => q.name).join(','),
      query_by_weights: fields.map((q) => q.weight).join(','),
      infix: fields
        .map((q) => (q.name === 'webpage.url' ? 'always' : 'off'))
        .join(',') as NonNullable<SearchParams['infix']>,
      stopwords: needStopwords ? `${locale}-stopwords` : undefined,
    };
  }

  /**
   * 构建 Thought 搜索查询
   * Build Thought search query
   */
  private async buildThoughtSearchQuery({
    userId,
    fields,
    query,
    ids,
  }: {
    userId: string;
    fields: { name: string; weight: number }[];
    query?: string;
    ids?: string[];
  }): Promise<MultiSearchRequestSchema> {
    const locale = detectLocale(query || '');
    const needStopwords = (query?.split(' ') || []).length > 1;
    const filter_by = `creator_id := ${userId}${ids?.length ? ` && id:[${ids.join(',')}]` : ''}`;

    return {
      q: query,
      collection: TypesenseCollection.THOUGHTS,
      filter_by,
      query_by: fields.map((q) => q.name).join(','),
      query_by_weights: fields.map((q) => q.weight).join(','),
      stopwords: needStopwords ? `${locale}-stopwords` : undefined,
    };
  }
}
