/**
 * Search Event Handler for Snip Events - Snip 事件搜索处理器
 * 处理 Snip 相关事件的搜索同步逻辑
 *
 * Migrated from:
 * - youapp/src/lib/domain/search/events.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventsHandler, type IEventHandler } from '@nestjs/cqrs';
import { SnipThoughtRelationDAO } from '../../../dao';
import { BoardItemDomainService } from '../../board-item';
import {
  PartialSnipUpdatedEvent,
  SnipCleanupEvent,
  SnipCreatedEvent,
  SnipDeletedEvent,
  SnipUpdatedEvent,
} from '../../events/snip.events';
import { TypesenseCollection } from '../definition';
import { SearchDomainService } from '../index';

@EventsHandler(SnipCreatedEvent, SnipUpdatedEvent, PartialSnipUpdatedEvent)
@Injectable()
export class SnipUpdateEventHandler
  implements IEventHandler<SnipCreatedEvent | SnipUpdatedEvent | PartialSnipUpdatedEvent>
{
  private readonly logger = new Logger(SnipUpdateEventHandler.name);

  constructor(private readonly searchDomain: SearchDomainService) {}

  async handle(event: SnipCreatedEvent | SnipUpdatedEvent | PartialSnipUpdatedEvent) {
    try {
      await this.searchDomain.syncDocuments({
        entities: [event.payload] as any[],
        collection: TypesenseCollection.SNIPS,
        userId: (event.payload as any).creator_id,
        docType: 'snip',
      });
      this.logger.debug(`Successfully synced snip documents for event: ${event.constructor.name}`);
    } catch (error) {
      this.logger.error(
        `Failed to sync snip documents for event: ${event.constructor.name}`,
        error,
      );
    }
  }
}

@EventsHandler(SnipDeletedEvent)
@Injectable()
export class SnipDeleteEventHandler implements IEventHandler<SnipDeletedEvent> {
  private readonly logger = new Logger(SnipDeleteEventHandler.name);

  constructor(private readonly searchDomain: SearchDomainService) {}

  async handle(event: SnipDeletedEvent) {
    try {
      await this.searchDomain.deleteDocuments(TypesenseCollection.SNIPS, [
        event.payload,
      ] as string[]);
      this.logger.debug(`Successfully deleted snip documents for snip: ${event.payload}`);
    } catch (error) {
      this.logger.error(`Failed to delete snip documents for snip: ${event.payload}`, error);
    }
  }
}

@EventsHandler(SnipCleanupEvent)
@Injectable()
export class SnipCleanupEventHandler implements IEventHandler<SnipCleanupEvent> {
  private readonly logger = new Logger(SnipCleanupEventHandler.name);

  constructor(
    private readonly boardItemDomain: BoardItemDomainService,
    private readonly snipThoughtRelationDAO: SnipThoughtRelationDAO,
  ) {}

  async handle(event: SnipCleanupEvent) {
    try {
      const { snipId } = event.payload;
      await Promise.all([
        this.boardItemDomain.deleteBySnipId(snipId),
        this.snipThoughtRelationDAO.deleteBySnipId(snipId),
      ]);
      this.logger.debug(`Successfully cleaned up snip relations for snip: ${snipId}`);
    } catch (error) {
      this.logger.error(
        `Failed to cleanup snip relations for snip: ${event.payload.snipId}`,
        error,
      );
    }
  }
}
