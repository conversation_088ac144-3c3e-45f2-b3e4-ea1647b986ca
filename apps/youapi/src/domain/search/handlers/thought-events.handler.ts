/**
 * Search Event Handler for Thought Events - Thought 事件搜索处理器
 * 处理 Thought 相关事件的搜索同步逻辑
 *
 * Migrated from:
 * - youapp/src/lib/domain/search/events.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventsHandler, type IEventHandler } from '@nestjs/cqrs';
import { SnipThoughtRelationDAO } from '../../../dao/snip-thought-relation';
import { BoardDomainService } from '../../board';
import { BoardItemDomainService } from '../../board-item';
import { BoardItemTypeEnum } from '../../board-item/types';
import {
  ThoughtBoardItemUpdateEvent,
  ThoughtCleanupEvent,
  ThoughtCreatedEvent,
  ThoughtDeletedEvent,
  ThoughtUpdatedEvent,
} from '../../events/thought.events';
import { TypesenseCollection } from '../definition';
import { SearchDomainService } from '../index';

@EventsHandler(ThoughtCreatedEvent)
@Injectable()
export class ThoughtCreateEventHandler implements IEventHandler<ThoughtCreatedEvent> {
  private readonly logger = new Logger(ThoughtCreateEventHandler.name);

  constructor(private readonly searchDomain: SearchDomainService) {}

  async handle(event: ThoughtCreatedEvent) {
    try {
      await this.searchDomain.syncDocuments({
        entities: [event.payload] as any[],
        collection: TypesenseCollection.THOUGHTS,
        userId: (event.payload as any).creator_id,
        docType: 'thought',
      });
      this.logger.debug(
        `Successfully synced thought documents for event: ${event.constructor.name}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to sync thought documents for event: ${event.constructor.name}`,
        error,
      );
    }
  }
}

@EventsHandler(ThoughtUpdatedEvent)
@Injectable()
export class ThoughtUpdateEventHandler implements IEventHandler<ThoughtUpdatedEvent> {
  private readonly logger = new Logger(ThoughtUpdateEventHandler.name);

  constructor(private readonly searchDomain: SearchDomainService) {}

  async handle(event: ThoughtUpdatedEvent) {
    try {
      await this.searchDomain.syncDocuments({
        entities: [event.payload] as any[],
        collection: TypesenseCollection.THOUGHTS,
        userId: (event.payload as any).creator_id,
        docType: 'thought',
      });
      this.logger.debug(
        `Successfully synced thought documents for event: ${event.constructor.name}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to sync thought documents for event: ${event.constructor.name}`,
        error,
      );
    }
  }
}

@EventsHandler(ThoughtDeletedEvent)
@Injectable()
export class ThoughtDeleteEventHandler implements IEventHandler<ThoughtDeletedEvent> {
  private readonly logger = new Logger(ThoughtDeleteEventHandler.name);

  constructor(private readonly searchDomain: SearchDomainService) {}

  async handle(event: ThoughtDeletedEvent) {
    try {
      await this.searchDomain.deleteDocuments(TypesenseCollection.THOUGHTS, [
        event.payload,
      ] as string[]);
      this.logger.debug(`Successfully deleted thought documents for thought: ${event.payload}`);
    } catch (error) {
      this.logger.error(`Failed to delete thought documents for thought: ${event.payload}`, error);
    }
  }
}

@EventsHandler(ThoughtBoardItemUpdateEvent)
@Injectable()
export class ThoughtBoardItemUpdateEventHandler
  implements IEventHandler<ThoughtBoardItemUpdateEvent>
{
  private readonly logger = new Logger(ThoughtBoardItemUpdateEventHandler.name);

  constructor(
    private readonly boardItemDomain: BoardItemDomainService,
    private readonly boardDomain: BoardDomainService,
  ) {}

  async handle(event: ThoughtBoardItemUpdateEvent) {
    try {
      const { thoughtId } = event.payload;
      const boardItem = await this.boardItemDomain.tryGetByEntityId(
        BoardItemTypeEnum.THOUGHT,
        thoughtId,
      );
      if (boardItem) {
        this.boardDomain.notifyBoardItemsChange(boardItem.board_id);
      }
    } catch (error) {
      this.logger.error(
        `Failed to handle thought board item update event: ${event.payload.thoughtId}`,
        error,
      );
    }
  }
}

@EventsHandler(ThoughtCleanupEvent)
@Injectable()
export class ThoughtCleanupEventHandler implements IEventHandler<ThoughtCleanupEvent> {
  private readonly logger = new Logger(ThoughtCleanupEventHandler.name);

  constructor(
    private readonly snipThoughtRelationDAO: SnipThoughtRelationDAO,
    private readonly boardItemDomain: BoardItemDomainService,
  ) {}

  async handle(event: ThoughtCleanupEvent) {
    try {
      const { thoughtId } = event.payload;
      await Promise.all([
        this.snipThoughtRelationDAO.deleteByThoughtId(thoughtId),
        this.boardItemDomain.deleteByThoughtId(thoughtId),
      ]);
    } catch (error) {
      this.logger.error(
        `Failed to handle thought cleanup event: ${event.payload.thoughtId}`,
        error,
      );
    }
  }
}
