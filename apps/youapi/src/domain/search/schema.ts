/**
 * Define search schema for typesense
 * @see https://typesense.org/docs/27.1/api/collections.html
 */
import { CollectionFieldSchema } from 'typesense/lib/Typesense/Collection';
import {
  SnipFieldsNeedLocalize,
  SnipFieldWeightMap,
  ThoughtFieldsNeedLocalize,
  ThoughtFieldWeightMap,
  TypesenseCollection,
} from './definition';
import { toLocalizedFieldNames, toLocalizedFieldSchema } from './utils';

export const localizedSnipFieldNames = SnipFieldsNeedLocalize.flatMap((field) =>
  toLocalizedFieldNames(field),
);
const localizedSnipSchemas = SnipFieldsNeedLocalize.flatMap((field) =>
  toLocalizedFieldSchema(field, SnipFieldWeightMap[field]),
);
export const localizedThoughtFieldNames = ThoughtFieldsNeedLocalize.flatMap((field) =>
  toLocalizedFieldNames(field),
);
const localizedThoughtSchemas = ThoughtFieldsNeedLocalize.flatMap((field) =>
  toLocalizedFieldSchema(field, ThoughtFieldWeightMap[field]),
);

export const SNIP_SEARCH_SCHEMA: CollectionFieldSchema[] = [
  {
    name: 'id',
    type: 'string',
  },
  {
    name: 'space_id',
    type: 'string',
  },
  {
    name: 'creator_id',
    type: 'string',
  },
  {
    name: 'type',
    type: 'string',
    facet: true,
  },
  {
    name: 'from',
    type: 'string',
    facet: true,
  },
  {
    name: 'updated_at',
    type: 'int64',
  },
  {
    name: 'published_at',
    type: 'int64',
    optional: true,
  },
  {
    name: 'authors',
    type: 'string[]',
    optional: true,
  },
  {
    name: 'author_pictures',
    type: 'string[]',
    optional: true,
    index: false,
  },
  {
    name: 'webpage.url',
    type: 'string',
    optional: true,
    infix: true,
  },
  {
    name: 'webpage.favicon_url',
    type: 'string',
    optional: true,
    index: false,
  },
  {
    name: 'webpage.site_name',
    type: 'string',
    optional: true,
    index: false,
  },
  {
    // for search result display only
    name: 'hero_image_url',
    type: 'string',
    optional: true,
    index: false,
  },
  // for short text that needs to be indexed
  ...localizedSnipSchemas,
];

export const THOUGHT_SEARCH_SCHEMA: CollectionFieldSchema[] = [
  {
    name: 'id',
    type: 'string',
  },
  {
    name: 'space_id',
    type: 'string',
  },
  {
    name: 'creator_id',
    type: 'string',
  },
  {
    name: 'updated_at',
    type: 'int64',
  },
  ...localizedThoughtSchemas,
];

export const SCHEMA_MAP = {
  [TypesenseCollection.SNIPS]: SNIP_SEARCH_SCHEMA,
  [TypesenseCollection.THOUGHTS]: THOUGHT_SEARCH_SCHEMA,
};
