/**
 * Search Result Domain Service - 搜索结果处理领域服务
 * 处理Typesense搜索结果，转换为前端友好格式
 *
 * Migrated from:
 * - /lib/domain/search/result.ts (youapp)
 */

import { Injectable, Logger } from '@nestjs/common';
import { SearchResponseHit } from 'typesense/lib/Typesense/Documents';
import { SnipDAO } from '@/dao/snip';
import { ThoughtDAO } from '@/dao/thought';
import { ClickhouseService } from '@/infra/clickhouse';
import { JinaService } from '@/infra/jina';

import {
  SnipSearchDocument,
  SnipSearchResultItem,
  ThoughtSearchDocument,
  ThoughtSearchResultItem,
} from './definition';
import { restoreLocalizedFields } from './utils';

export type SearchResult = Partial<SnipSearchResultItem | ThoughtSearchResultItem>;
export type ProcessedSearchHit = SearchResponseHit<SearchResult>;

export type ChunkResult = {
  id: string;
  type: string;
  original_field: string;
  chunk_index: number;
  chunk: string;
  updated_at: string;
  distance: number;
  document?: Partial<SnipSearchResultItem | ThoughtSearchResultItem>;
  title?: string;
};

export type HybridSearchChunksParams = {
  query: string;
  enrichMetadata?: boolean;
  maxResults?: number;
  rerank?: boolean;
};

@Injectable()
export class SearchResultDomainService {
  private readonly logger = new Logger(SearchResultDomainService.name);

  constructor(
    private readonly clickhouseService: ClickhouseService,
    private readonly snipDAO: SnipDAO,
    private readonly thoughtDAO: ThoughtDAO,
    private readonly jinaService: JinaService,
  ) {}

  /**
   * 处理单个搜索命中结果
   * 转换搜索命中结果为前端友好格式
   */
  private processSearchHit(
    hit: SearchResponseHit<SnipSearchDocument | ThoughtSearchDocument>,
    runtimeCollectionName: string,
  ): ProcessedSearchHit {
    try {
      return {
        document: {
          ...restoreLocalizedFields(hit.document),
          docType: this.clickhouseService.toClickhouseEntityType(runtimeCollectionName),
        },
        highlight: restoreLocalizedFields(
          hit.highlight as Partial<SnipSearchDocument | ThoughtSearchDocument>,
        ),
        text_match: hit.text_match,
      };
    } catch (error) {
      this.logger.error(`Failed to process search hit: ${error}`, {
        runtimeCollectionName,
        hitId: hit.document?.id,
      });
      throw error;
    }
  }

  /**
   * 合并重复的搜索命中结果
   * 合并相同ID的搜索结果，保留最高的文本匹配分数
   */
  private mergeSearchHits(hits: ProcessedSearchHit[]): ProcessedSearchHit[] {
    return hits.reduce((acc, curr) => {
      const existing = acc.find((item) => item.document.id === curr.document.id);
      if (existing) {
        existing.highlight = Object.assign({}, existing.highlight, curr.highlight);
        existing.text_match = Math.max(existing.text_match, curr.text_match);
      } else {
        acc.push(curr);
      }
      return acc;
    }, [] as ProcessedSearchHit[]);
  }

  /**
   * 处理搜索结果
   * 处理Typesense多搜索响应，转换为统一格式
   */
  async processSearchResult(result: any) {
    try {
      const hits = result.results
        .flatMap(
          (result: any) =>
            result.hits?.map((hit: any) =>
              this.processSearchHit(hit, result.request_params.collection_name!),
            ) || [],
        )
        .sort((a: any, b: any) => b.text_match - a.text_match);

      const mergedHits = this.mergeSearchHits(hits);

      const thoughtHits = mergedHits.filter((hit) => hit.document.docType === 'thought');

      // 修复思考标题可能被更新到多语言字段的问题
      // @FIXME thought 更新时可能 title 会被更新到 title_en 或 title_zh 上，导致最终搜出来的 title
      // 跟 db 里不匹配。垃圾 TypeSense 的多语言分词机制的锅。
      if (thoughtHits.length > 0) {
        const thoughtIds = hits
          .filter((hit) => hit.document.docType === 'thought')
          .map((hit) => hit.document.id!)
          .filter(Boolean);

        if (thoughtIds.length > 0) {
          const titles = await this.thoughtDAO.selectBriefsByIds(thoughtIds);
          const titleMap = new Map(titles.map((t) => [t.id, t.title]));

          mergedHits.forEach((hit) => {
            if (hit.document.docType === 'thought' && hit.document.id) {
              hit.document.title = titleMap.get(hit.document.id) || undefined;
            }
          });
        }
      }

      return {
        found: mergedHits.length,
        hits: mergedHits,
      };
    } catch (error) {
      this.logger.error(`Failed to process search result: ${error}`);
      throw error;
    }
  }

  /**
   * 处理混合搜索块结果
   * 合并全文搜索和语义搜索的块结果
   */
  async processHybridChunksResult(
    fullTextResult: any,
    semanticResult: any,
    options: HybridSearchChunksParams,
  ) {
    try {
      const { enrichMetadata, maxResults = 5, rerank } = options;

      // 处理全文搜索结果中的块
      const fullTextChunks = (fullTextResult.hits || []).flatMap((hit) => {
        const doc = hit.document;

        // 获取命中结果的所有高亮块
        const highlights = hit.highlight || {};
        const highlightEntries = Object.entries(highlights);

        if (highlightEntries.length === 0) {
          // 如果没有高亮，使用内容的前200字符作为后备方案
          const result: ChunkResult = {
            id: doc.id!,
            type: doc.docType || 'snip',
            original_field: 'content',
            chunk_index: 0,
            chunk: (doc.content_0 || '').slice(0, 200),
            updated_at:
              typeof doc.updated_at === 'string' ? doc.updated_at : new Date().toISOString(),
            distance: 0.7,
            document: doc,
          };
          return [result];
        }

        // 为每个高亮字段创建一个块
        return highlightEntries.map(
          ([field, highlight], index): ChunkResult => ({
            id: doc.id!,
            type: doc.docType || 'snip',
            original_field: field,
            chunk_index: index,
            chunk: Array.isArray(highlight)
              ? highlight[0]?.snippet || ''
              : (highlight as unknown as { snippet: string }).snippet || '',
            updated_at:
              typeof doc.updated_at === 'string' ? doc.updated_at : new Date().toISOString(),
            distance: 0.7,
            document: doc,
          }),
        );
      });

      // 合并结果，基于ID和块索引去重
      const combinedResults: ChunkResult[] = [...semanticResult];
      for (const chunk of fullTextChunks) {
        if (
          !combinedResults.some(
            (r) =>
              r.id === chunk.id && (r.chunk.includes(chunk.chunk) || chunk.chunk.includes(r.chunk)),
          )
        ) {
          combinedResults.push(chunk);
        }
      }

      // 丰富元数据
      if (enrichMetadata) {
        await this.enrichResultsWithMetadata(combinedResults);
      }

      // 重新排序
      if (rerank) {
        return await this.rerankChunks(combinedResults, options.query, maxResults);
      }

      // 按距离排序并限制结果数量
      return combinedResults.sort((a, b) => a.distance - b.distance).slice(0, maxResults);
    } catch (error) {
      this.logger.error(`Failed to process hybrid chunks result: ${error}`, {
        query: options.query,
        maxResults: options.maxResults,
      });
      throw error;
    }
  }

  /**
   * 丰富结果元数据
   * 为搜索结果添加标题等元数据信息
   */
  private async enrichResultsWithMetadata(results: ChunkResult[]): Promise<void> {
    try {
      const resultIdsWithNoTitle = results
        .filter((r) => (r.document && !('title' in r.document)) || r.original_field !== 'title')
        .map((r) => ({ type: r.type, id: r.id }));

      const snipIds = resultIdsWithNoTitle.filter((r) => r.type === 'snip').map((r) => r.id);
      const thoughtIds = resultIdsWithNoTitle.filter((r) => r.type === 'thought').map((r) => r.id);

      const [snipTitles, thoughtTitles] = await Promise.all([
        snipIds.length > 0 ? this.snipDAO.selectTitlesByIds(snipIds) : [],
        thoughtIds.length > 0 ? this.thoughtDAO.selectBriefsByIds(thoughtIds) : [],
      ]);

      // 添加Snip标题
      snipTitles.forEach((t) => {
        const result = results.find((r) => r.id === t.id);
        if (result) {
          result.title = t.title || undefined;
        }
      });

      // 添加Thought标题
      thoughtTitles.forEach((t) => {
        const result = results.find((r) => r.id === t.id);
        if (result) {
          result.title = t.title || undefined;
        }
      });
    } catch (error) {
      this.logger.error(`Failed to enrich results with metadata: ${error}`);
      throw error;
    }
  }

  /**
   * 重新排序块
   * 使用Jina重新排序服务对搜索块进行重新排序
   */
  private async rerankChunks(
    chunks: ChunkResult[],
    query: string,
    maxResults: number,
  ): Promise<ChunkResult[]> {
    try {
      const rerankResult = await this.jinaService.rerankDocuments({
        query,
        documents: chunks.map((e) => `${e.title || e.document?.title || ''}\n\n${e.chunk}`),
        top_n: maxResults,
      });

      if (rerankResult !== null) {
        // 重新排序的实体和块
        return rerankResult.results
          .map((r) => {
            const item = chunks[r.index];
            if (!item) {
              return null;
            }
            return {
              ...item,
            };
          })
          .filter((e) => e !== null);
      }

      // 如果重新排序失败，返回原始排序结果
      return chunks.sort((a, b) => a.distance - b.distance).slice(0, maxResults);
    } catch (error) {
      this.logger.error(`Failed to rerank chunks: ${error}`, { query });
      // 如果重新排序失败，返回原始排序结果
      return chunks.sort((a, b) => a.distance - b.distance).slice(0, maxResults);
    }
  }
}
