/**
 * Internet Search Domain Service - 互联网搜索领域服务
 * 基于 Brave 和 SERP 的搜索服务，提供网页、视频、事件、学术论文等搜索功能
 *
 * Migrated from:
 * - youapp/src/domain/search/internet.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { truncateByTokens } from '@/common/utils';
import { type InternetSearchResult, InternetSearchResultTypeEnum } from '../../common/types';
import { isVideoUrl, runConcurrently, runSafely, SafeParse } from '../../common/utils';
import { BochaSearchService } from '../../infra/bocha';
import { BraveSearchService } from '../../infra/brave';
import { GoogleService } from '../../infra/google';
import { searchAppStore } from '../../infra/serp/app_store';
import { searchGoogleEvents } from '../../infra/serp/google_events';
import { searchGoogleScholar } from '../../infra/serp/google_scholar';
import { searchGoogle } from '../../infra/serp/google_search';
import youget from '../../infra/youget';
import {
  ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse,
  ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner,
} from '../../infra/youget/typescript-axios-client';
import { UsageRecordDomainService } from '../usage-record';

interface InternetSearchParam {
  query: string;
  region?: string;
  language?: string;
  page_count?: number;
  current_page?: number;
  freshness?: string;
}

interface ScrapeResult {
  title: string;
  content: string;
  images: Record<string, string>;
}

@Injectable()
export class InternetSearchDomainService {
  private readonly logger = new Logger(InternetSearchDomainService.name);

  constructor(
    private readonly usageRecordDomain: UsageRecordDomainService,
    private readonly braveSearchService: BraveSearchService,
    private readonly googleService: GoogleService,
    private readonly bochaSearchService: BochaSearchService,
  ) {}

  /**
   * 从URL中提取主机名
   * Extract hostname from URL
   */
  private getHostname(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return '';
    }
  }

  /**
   * 生成网站图标URL
   * Generate a favicon URL for a given URL
   */
  private getFaviconUrl(url: string): string {
    const hostname = this.getHostname(url || '');
    return hostname ? `http://www.google.com/s2/favicons?domain=${hostname}&sz=64` : '';
  }

  /**
   * 语言代码转换为CSE格式
   * Convert language code to CSE HL format
   */
  private langCodeToCSEHL(lang: string): string {
    switch (lang) {
      case 'zh':
        return 'zh-CN';
      case 'zh-Hant':
        return 'zh-TW';
      default:
        return lang;
    }
  }

  /**
   * 时间范围适配器转换为博查格式
   * Convert freshness parameter to Bocha format
   */
  private freshnessAdapterToBocha(freshness: string): string {
    switch (freshness) {
      case 'pd':
        return 'oneDay';
      case 'pw':
        return 'oneWeek';
      case 'pm':
        return 'oneMonth';
      case 'py':
        return 'oneYear';
      default:
        // 处理日期范围格式：YYYY-MM-DDtoYYYY-MM-DD -> YYYY-MM-DD..YYYY-MM-DD
        if (freshness.includes('to') && freshness.match(/^\d{4}-\d{2}-\d{2}to\d{4}-\d{2}-\d{2}$/)) {
          return freshness.replace('to', '..');
        }
        // 处理单个日期格式：YYYY-MM-DD (直接返回)
        if (freshness.match(/^\d{4}-\d{2}-\d{2}$/)) {
          return freshness;
        }
        // 如果格式不匹配，返回默认值
        return 'noLimit';
    }
  }

  /**
   * Brave 网页搜索
   * Brave web search implementation
   */
  async braveWebSearch(param: InternetSearchParam): Promise<InternetSearchResult[]> {
    const { query, page_count = 10 } = param;
    try {
      const results = await this.braveSearchService.webSearch(query, {
        maxResults: page_count.toString(),
        ...(param.freshness && { freshness: param.freshness }),
      });

      // 记录使用情况
      this.usageRecordDomain.createBraveSearchUsageRecord({}).catch((error) => {
        this.logger.warn('Failed to record Brave search usage', error);
      });

      const toResult = (r: any) => ({
        type: isVideoUrl(r.url)
          ? InternetSearchResultTypeEnum.VIDEO
          : InternetSearchResultTypeEnum.WEBPAGE,
        url: r.url,
        title: r.title,
        site_name:
          r.profile?.name || r.meta_url?.netloc || r.meta_url?.hostname || this.getHostname(r.url),
        related_chunk: r.extra_snippets?.join('. ') || r.description,
        favicon: this.getFaviconUrl(r.url),
      });

      return [
        ...(results.web?.results || []).map(toResult),
        ...(results.news?.results || []).map(toResult),
        ...(results.videos?.results || []).map(toResult),
        ...(results.discussions?.results || []).map(toResult),
      ];
    } catch (error) {
      this.logger.error('Brave web search failed', error);
      return [];
    }
  }

  /**
   * 博查网页搜索
   * Bocha web search implementation
   */
  async bochaWebSearch(param: InternetSearchParam): Promise<InternetSearchResult[]> {
    const { query, page_count = 10, freshness } = param;

    try {
      const results = await this.bochaSearchService.search({
        query,
        count: page_count,
        // 使用 freshness 参数，如果提供的话
        ...(freshness && {
          freshness: this.freshnessAdapterToBocha(freshness),
        }),
        summary: true,
      });

      // 记录使用情况
      this.usageRecordDomain.createBochaSearchUsageRecord({}).catch((error) => {
        this.logger.warn('Failed to record Bocha search usage', error);
      });

      return results.map((r) => ({
        type: isVideoUrl(r.url)
          ? InternetSearchResultTypeEnum.VIDEO
          : InternetSearchResultTypeEnum.WEBPAGE,
        url: r.url || '',
        title: r.name || '',
        related_chunk: r.summary || r.snippet || '',
        site_name: r.siteName || this.getHostname(r.url || ''),
        favicon: this.getFaviconUrl(r.url || ''),
      }));
    } catch (error) {
      this.logger.error('Bocha web search failed', error);
      return [];
    }
  }

  /**
   * Google 网页搜索
   * Google web search with fallback strategy
   */
  async googleWebSearch(param: InternetSearchParam): Promise<InternetSearchResult[]> {
    const { query, language = 'en', page_count = 10, current_page = 1 } = param;
    let results: InternetSearchResult[] = [];

    try {
      // 中文查询使用博查，提供更好的中文搜索体验
      if (language === 'zh') {
        results = await this.bochaWebSearch(param);
      } else {
        // 非中文查询使用 Brave 搜索
        results = await this.braveWebSearch(param);
      }

      if (results.length === 0) {
        const serp_results = await searchGoogle({
          q: query,
          hl: this.langCodeToCSEHL(language),
          num: page_count,
          start: current_page - 1,
        });

        results = serp_results.map((r) => ({
          type: isVideoUrl(r.link || '')
            ? InternetSearchResultTypeEnum.VIDEO
            : InternetSearchResultTypeEnum.WEBPAGE,
          url: r.link || '',
          title: r.title,
          related_chunk: r.snippet || '',
          site_name: r.source || this.getHostname(r.link || ''),
          favicon: this.getFaviconUrl(r.link || ''),
        }));

        this.usageRecordDomain.createGoogleSearchUsageRecord({}).catch((error) => {
          this.logger.warn('Failed to record Google search usage', error);
        });
      }

      this.logger.debug(`Google web search completed with ${results.length} results`);
      return results;
    } catch (error) {
      this.logger.error('Google web search failed', error);
      return [];
    }
  }

  /**
   * 视频搜索
   * Video search implementation
   */
  async videoSearch(param: InternetSearchParam): Promise<InternetSearchResult[]> {
    const { query, current_page = 1, page_count = 10, language = 'en' } = param;

    try {
      const results = await this.googleService.search({
        q: query,
        num: page_count,
        start: current_page - 1,
        sites: ['youtube.com', 'tiktok.com', 'bilibili.com'],
        hl: this.langCodeToCSEHL(language),
      });

      this.usageRecordDomain.createGoogleSearchUsageRecord({}).catch((error) => {
        this.logger.warn('Failed to record video search usage', error);
      });

      return results.map((r) => ({
        type: InternetSearchResultTypeEnum.VIDEO,
        url: r.link || '',
        title: r.title || '',
        related_chunk: r.snippet || '',
        cover_image: r.pagemap?.cse_image?.[0]?.src || '',
        favicon: this.getFaviconUrl(r.link || ''),
      }));
    } catch (error) {
      this.logger.error('Video search failed', error);
      return [];
    }
  }

  /**
   * 事件搜索
   * Event search implementation
   * @param param.event_filter should follow google htichips syntax
   * @doc https://serpapi.com/google-events-api
   */
  async eventSearch(
    param: InternetSearchParam & { event_filter: string; location: string },
  ): Promise<InternetSearchResult[]> {
    const { query, location, event_filter, language = 'en', current_page = 1 } = param;

    try {
      const results = await searchGoogleEvents({
        q: query,
        start: current_page - 1,
        hl: this.langCodeToCSEHL(language),
        location,
        htichips: event_filter,
      });

      this.usageRecordDomain
        .createSerpSearchUsageRecord({
          service: 'google_events',
        })
        .catch((error) => {
          this.logger.warn('Failed to record event search usage', error);
        });

      return results.map((r) => ({
        type: InternetSearchResultTypeEnum.EVENTS,
        url: r.link,
        time: r?.date?.start_date + ' ' + r.date?.when,
        title: r.title,
        related_chunk: r.description,
        ...(r.address && {
          address: typeof r.address === 'string' ? r.address : r.address.join(', '),
        }),
        favicon: this.getFaviconUrl(r.link || ''),
      }));
    } catch (error) {
      this.logger.error('Event search failed', error);
      return [];
    }
  }

  /**
   * 学术搜索
   * Scholar search implementation
   * @param param.query should follow google scholar query syntax
   * @doc https://library.acg.edu/how-to-guides/google-scholar/advanced-searching
   */
  async scholarSearch(
    param: InternetSearchParam & { from_year?: number; to_year?: number },
  ): Promise<InternetSearchResult[]> {
    const { query, language = 'en', current_page = 1, page_count = 10 } = param;

    try {
      const results = await searchGoogleScholar({
        q: query,
        num: page_count,
        start: current_page - 1,
        hl: this.langCodeToCSEHL(language),
        safe: 'active',
        scisbd: 1, // show abstract
        ...(param.to_year && { as_yhi: param.to_year }),
        ...(param.from_year && { as_ylo: param.from_year }),
      });

      return results.map((r) => ({
        type: InternetSearchResultTypeEnum.SCHOLAR,
        url: r.link || '',
        title: r.title,
        author: r.publication_info?.summary,
        related_chunk: r.snippet,
        favicon: this.getFaviconUrl(r.link || ''),
      }));
    } catch (error) {
      this.logger.error('Scholar search failed', error);
      return [];
    }
  }

  /**
   * App Store 搜索
   * App Store search implementation
   */
  async appStoreSearch(
    param: InternetSearchParam & { device: 'desktop' | 'tablet' | 'mobile' },
  ): Promise<InternetSearchResult[]> {
    const {
      query,
      region = 'en-us',
      language = 'en',
      device = 'mobile',
      page_count = 10,
      current_page = 1,
    } = param;

    try {
      const results = await searchAppStore({
        term: query,
        num: page_count,
        page: current_page - 1,
        device,
        country: region,
        // [FIXME] 注意这个 lang 需要转化。https://serpapi.com/apple-app-store
        lang: language,
      });

      this.usageRecordDomain
        .createSerpSearchUsageRecord({
          service: 'apple_app_store',
        })
        .catch((error) => {
          this.logger.warn('Failed to record App Store search usage', error);
        });

      return results.map((r) => ({
        type: InternetSearchResultTypeEnum.APPLE_APP_STORE,
        url: r.link,
        title: r.title,
        author: r.developer?.name,
        related_chunk: r.description,
        favicon: r.logos?.pop()?.link || '',
      }));
    } catch (error) {
      this.logger.error('App Store search failed', error);
      return [];
    }
  }

  /**
   * 过滤有用的图片
   * Filters image objects to only include useful images with descriptive keys
   * Removes entries that are just "Image xx" without additional descriptions
   * Also filters out logos, favicons, and other non-useful images
   */
  private filterUsefulImages(
    files: Array<ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponseResultFilesInner>,
  ): Record<string, string> {
    if (!files || files?.length === 0) {
      return {};
    }

    const filteredFiles = files.filter((file) => {
      if (!file.original_url) return false;
      if (!file.mime_type || !file.mime_type.startsWith('image/')) return false;
      if (!file.name || /^Image \d+(?:,\d+)?$/.test(file.name)) return false;

      const hasDescription = file.name.includes(':');
      // Skip logos, favicons, and other non-useful images based on key or URL
      const lowerKey = file.name.toLowerCase();
      const lowerValue = file.original_url.toLowerCase();
      const isUnwanted =
        lowerKey.includes('logo') ||
        lowerKey.includes('favicon') ||
        lowerKey.includes('avatar') ||
        lowerKey.includes('icon') ||
        lowerValue.includes('logo') ||
        lowerValue.includes('favicon') ||
        lowerValue.includes('icon') ||
        lowerValue.includes('avatar') ||
        lowerValue.includes('thumb') ||
        lowerValue.includes('.svg') ||
        lowerValue.includes('preview.redd.it') ||
        lowerValue.includes('redditmedia') ||
        lowerValue.startsWith('blob:') ||
        lowerValue.startsWith('data:image');

      return hasDescription && !isUnwanted;
    });

    return filteredFiles.reduce(
      (accu, file) => {
        accu[file.name!] = file.original_url;
        return accu;
      },
      {} as Record<string, string>,
    );
  }

  /**
   * 处理抓取结果
   * Process scraped content and clean it up
   */
  private processScrapeResult(content: string): string {
    if (!content) return '';

    const is_content_valid =
      content &&
      ![
        'Please verify you are a human',
        'Sorry, you have been blocked',
        "verify you're human",
      ].includes(content);
    if (!is_content_valid) return '';

    // check if content is full of control characters such as newlines/tabs/others
    if (!/\S/.test(content)) return '';

    return truncateByTokens(content, {
      maxTokens: 800,
      reserveForSuffix: false,
    });
  }

  /**
   * 抓取多个URL的内容
   * Scrape content from multiple URLs
   */
  async scrapeUrls(urls: string[], timeout = 20000): Promise<Record<string, ScrapeResult>> {
    const request_urls = urls.filter(Boolean);
    const requests = request_urls.map((url) => {
      return async () =>
        runSafely(
          (async () => {
            return Promise.race([
              youget
                .createScrapPageTask({
                  target_url: url,
                  save_image: false,
                  scrape_service: 'jina',
                })
                .then((result: unknown) =>
                  youget.executeScrapPageTask((result as { id: string }).id),
                ),
              new Promise((resolve) => setTimeout(() => resolve(null), timeout)),
            ]);
          }) as unknown as () => Promise<null>,
          null,
        );
    });

    try {
      const pages = (await runConcurrently(requests, 20)) as Array<{
        data: ApiV1ShortcutCreateAndRunScrapPageTaskPostDefaultResponse;
      } | null>;

      let total_tokens = 0;
      const url_map = pages.reduce<Record<string, ScrapeResult>>((acc, page, index) => {
        if (!page || !page.data?.result) return acc;
        acc[request_urls[index]] = {
          title: page.data?.result?.title,
          images: this.filterUsefulImages(page.data?.result?.files),
          content: this.processScrapeResult(page.data?.result?.content?.plain || ''),
        };
        const extra = SafeParse(page?.data?.result?.extra || '{}', true, {
          usage: { tokens: 0 },
        });
        total_tokens += extra?.usage?.tokens || 0;
        return acc;
      }, {});

      this.usageRecordDomain
        .createJinaUsageRecord({
          amount: total_tokens,
        })
        .catch((error) => {
          this.logger.warn('Failed to record Jina usage', error);
        });

      this.logger.debug(`Scraped ${Object.keys(url_map).length} URLs successfully`);
      return url_map;
    } catch (error) {
      this.logger.error('URL scraping failed', error);
      return {};
    }
  }
}
