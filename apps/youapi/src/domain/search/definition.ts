import { pick } from 'lodash';

import type { SnipFromEnum, SnipTypeEnum } from '../../common/types/snip.types';

// Temporary Snip interface until proper migration is complete
interface Snip {
  id: string;
  creator_id: string;
  space_id: string;
  type: SnipTypeEnum;
  from: SnipFromEnum;
  updated_at: Date;
  published_at?: Date;
  [key: string]: any; // Allow additional properties
}

import type { Thought } from '../thought/types';

export const DEFAULT_SEMANTIC_DISTANCE = 0.6;

export enum TypesenseAction {
  UPSERT = 'upsert',
  EMPLACE = 'emplace',
}

export enum TypesenseCollection {
  SNIPS = 'snips',
  THOUGHTS = 'thoughts',
}
// Raw entity to be indexed, will be converted to SnipSearchDocument
export type EntityToBeIndexed = Snip | Thought;

// split long content into smaller chunks for better search performance
export const CONTENT_SPLIT_SIZE = 5 as const;
export type SplitIndex = 0 | 1 | 2 | 3 | 4;
export const FieldsNeedSplit = ['content', 'overview', 'transcript'] as const;
export type FieldsNeedSplit = (typeof FieldsNeedSplit)[number];

// map language detection result to typesense supported locale,
// for now we only process Chinese letters.
// `en` represents all space-separated languages like english, spanish, french, etc.
export const LANGUAGE_LOCALE_MAP = {
  cmn: 'zh',
  eng: 'en',
  jpn: 'ja',
  kor: 'ko',
  tha: 'th',
  ell: 'el',
  rus: 'ru',
  srp: 'sr',
  ukr: 'uk',
  bel: 'be',
} as const;
export const SEARCH_LANGUAGE_LOCALE_MAP = pick(LANGUAGE_LOCALE_MAP, ['cmn', 'eng']);
export const DEFAULT_LOCALE = 'en';
export type DetectedLangKeys = keyof typeof SEARCH_LANGUAGE_LOCALE_MAP;
export type Locales = (typeof SEARCH_LANGUAGE_LOCALE_MAP)[DetectedLangKeys];
export const isValidLocale = (locale: string): locale is Locales =>
  Object.values(SEARCH_LANGUAGE_LOCALE_MAP).includes(locale as Locales);

export type Localized<T extends string> = {
  [K in Locales as `${K}_${T}`]?: string;
};
export function localize(key: string, locale: Locales) {
  return `${locale}_${key}` as keyof Localized<string>;
}
export type Splitted<T extends string> = {
  [K in `${T}_${SplitIndex}`]?: string;
};
export type LocalizedAndSplitted<T extends string> = {
  [K in Locales as `${K}_${T}_${SplitIndex}`]?: string;
};
export function localizeAndSplit(fieldName: string, locale: Locales, index: SplitIndex) {
  return `${locale}_${fieldName}_${index}` as keyof LocalizedAndSplitted<string>;
}

// fields that need to be localized, key will be transformed to `${locale}_${field_name}`
// and send to typesense.
// for `content` field, will be split into `CONTENT_SPLIT_SIZE` pieces, `${locale}_content_${index}`
export const SnipFieldsNeedLocalize = [
  'title',
  'overview',
  'transcript',
  'description',
  'raw_summary',
  'show_notes',
  'content',
  'extracted_text',
] as const;
type SnipFieldsNeedLocalize = (typeof SnipFieldsNeedLocalize)[number];
export const SnipFieldWeightMap: Record<SnipFieldsNeedLocalize, number> = {
  title: 10,
  overview: 1,
  transcript: 3,
  description: 2,
  show_notes: 2,
  raw_summary: 2,
  content: 3,
  extracted_text: 5,
};

export const ThoughtFieldsNeedLocalize = ['title', 'content'] as const;
type ThoughtFieldsNeedLocalize = (typeof ThoughtFieldsNeedLocalize)[number];
export const ThoughtFieldWeightMap: Record<ThoughtFieldsNeedLocalize, number> = {
  content: 5,
  title: 10,
};

export const AllFieldsNeedLocalize = Array.from(
  new Set([...SnipFieldsNeedLocalize, ...ThoughtFieldsNeedLocalize]),
);
export type AllFieldsNeedLocalize = (typeof AllFieldsNeedLocalize)[number];

type OriginalSnipFields = {
  [K in SnipFieldsNeedLocalize]: string;
};
type LocalizedSnipFields = Localized<Exclude<SnipFieldsNeedLocalize, 'content'>>;
type OriginalThoughtFields = {
  [K in ThoughtFieldsNeedLocalize]: string;
};
type LocalizedThoughtFields = Localized<Exclude<ThoughtFieldsNeedLocalize, 'content'>>;

export interface BaseMetaFields {
  id: string;
  creator_id: string;
  space_id: string;
  /** typesense only accept timestamp as number instead of Date */
  updated_at?: number;
}
export interface BaseSnipFields extends BaseMetaFields {
  published_at?: number;
  type: SnipTypeEnum;
  from: SnipFromEnum;
  authors?: string[];
  author_pictures?: string[];
  'webpage.url'?: string;
  'webpage.favicon_url'?: string;
  'webpage.site_name'?: string;
  hero_image_url?: string;
}
export interface BaseThoughtFields extends BaseMetaFields {}

/**
 * snip schema for typesense
 */
export interface SnipSearchDocument
  extends BaseSnipFields,
    LocalizedSnipFields,
    LocalizedAndSplitted<'content'> {}

export interface ThoughtSearchDocument
  extends BaseThoughtFields,
    LocalizedThoughtFields,
    LocalizedAndSplitted<'content'> {}

export type WithReferencedSnipDocument<T> = T & {
  snips?: Partial<SnipSearchDocument>;
};
export type WithReferencedThoughtDocument<T> = T & {
  thoughts?: Partial<ThoughtSearchDocument>;
};

/**
 * Final search result for frontend, merged snip and block search results
 */
export interface SnipSearchResultItem
  extends BaseSnipFields,
    Omit<OriginalSnipFields, 'content' | 'overview' | 'transcript'>,
    Splitted<'content'>,
    Splitted<'overview'>,
    Splitted<'transcript'> {
  docType: 'snip';
}
export interface ThoughtSearchResultItem
  extends BaseThoughtFields,
    Omit<OriginalThoughtFields, 'content'>,
    Splitted<'content'> {
  docType: 'thought';
}

export const DEFAULT_SEARCH_FIELDS = [
  'snips.overview',
  'snips.transcript',
  'snips.description',
  'snips.show_notes',
  'snips.title',
  'snips.content',
  'snips.authors',
  'snips.webpage.url',
];

export const DEFAULT_SEARCH_CHUNK_FIELDS = [
  // "snips.overview",
  'snips.transcript',
  'snips.description',
  'snips.show_notes',
  'snips.content',
  'thoughts.content',
];

/** For Clickhouse youmind_entity_embeddings table */
export interface EmbeddingsTableItem {
  id: string;
  creator_id: string;
  space_id: string;
  type: string;
  original_field: string;
  chunk_index: number;
  chunk: string;
  embedding: number[];
  updated_at: string;
}
