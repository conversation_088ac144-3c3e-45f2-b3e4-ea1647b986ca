export interface BoardGroup {
  /**
   * 唯一标识
   */
  id: string;
  /**
   * 创作板 ID
   */
  board_id: string;
  /**
   * 创建人 ID
   */
  creator_id: string;
  /**
   * 创建时间
   */
  created_at: Date;
  /**
   * 更新时间
   */
  updated_at: Date;
  /**
   * 名称
   */
  name: string;
  /**
   * 图标
   */
  icon: Icon;
  /**
   * 类型
   */
  type?: BoardGroupTypeEnum;
}

/**
 * 图标值对象
 */
export interface Icon {
  name?: string;
  color?: string;
}

/**
 * 分组类型枚举
 */
export enum BoardGroupTypeEnum {
  NORMAL = 'normal',
  UPLOADS = 'uploads',
}

export interface CreateBoardGroupParam {
  board_id: string;
  user_id: string;
  name: string;
  icon?: Icon;
  type?: BoardGroupTypeEnum;
}

export interface PatchBoardGroupParam {
  id: string;
  name?: string;
  icon?: Icon;
}
