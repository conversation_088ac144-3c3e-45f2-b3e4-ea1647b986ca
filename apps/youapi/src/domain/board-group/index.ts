/**
 * Board Group Domain Service - 看板分组领域服务
 * 处理看板分组相关的业务逻辑
 *
 * Migrated from:
 * - youapp/src/lib/domain/board-group/index.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { NotFound, ResourceEnum } from '../../common/errors';
import { BoardGroupDAO } from '../../dao/board-group';
import { BoardGroupDO } from '../../dao/board-group/types';
import { BoardItemDomainService } from '../board-item';
import { BoardItemTypeEnum } from '../board-item/types';
import {
  type BoardGroup,
  BoardGroupTypeEnum,
  type CreateBoardGroupParam,
  type PatchBoardGroupParam,
} from './types';

@Injectable()
export class BoardGroupDomainService {
  private readonly logger = new Logger(BoardGroupDomainService.name);

  constructor(
    private readonly boardGroupDao: BoardGroupDAO,
    private readonly boardItemDomain: BoardItemDomainService,
  ) {}

  async create(param: CreateBoardGroupParam): Promise<BoardGroup> {
    // const range = await this.getRankRange(param.board_id, param.rank_after);
    const boardGroupDO = await this.boardGroupDao.insert({
      ...param,
      creator_id: param.user_id,
      icon_name: param.icon?.name,
      icon_color: param.icon?.color,
      type: param.type,
    });
    return this.do2entity(boardGroupDO);
  }

  async patch(param: PatchBoardGroupParam): Promise<BoardGroup> {
    const patchedBoardGroupDO = await this.boardGroupDao.update({
      id: param.id,
      name: param.name,
      icon_name: param.icon?.name,
      icon_color: param.icon?.color,
    });
    return this.do2entity(patchedBoardGroupDO);
  }

  async delete(id: string): Promise<void> {
    await this.boardGroupDao.delete({ id });
  }

  async getById(id: string): Promise<BoardGroup> {
    const boardGroupDO = await this.boardGroupDao.selectById({ id });
    if (!boardGroupDO) {
      throw new NotFound({ resource: ResourceEnum.BOARD_GROUP, id });
    }
    return this.do2entity(boardGroupDO);
  }

  async list(board_id: string): Promise<BoardGroup[]> {
    const boardGroupDOs = await this.boardGroupDao.selectByBoardId({
      board_id,
    });
    return boardGroupDOs.map((boardGroupDO) => this.do2entity(boardGroupDO));
  }

  async getBriefById(id: string) {
    // For now, use the regular selectById method since selectBriefById is not implemented
    const briefDO = await this.boardGroupDao.selectById({ id });
    if (!briefDO) {
      throw new NotFound({ resource: ResourceEnum.BOARD_GROUP, id });
    }
    return {
      id: briefDO.id,
      name: briefDO.name,
      icon:
        briefDO.icon_name && briefDO.icon_color
          ? { name: briefDO.icon_name, color: briefDO.icon_color }
          : undefined,
    };
  }

  async listBriefsByIds(ids: string[]) {
    // For now, iterate through each ID since selectBriefsByIds is not implemented
    const briefDOs = await Promise.all(
      ids.map(async (id) => {
        const result = await this.boardGroupDao.selectById({ id });
        return result;
      }),
    );
    return briefDOs.filter(Boolean).map((briefDO) => ({
      id: briefDO.id,
      name: briefDO.name,
      icon:
        briefDO.icon_name && briefDO.icon_color
          ? { name: briefDO.icon_name, color: briefDO.icon_color }
          : undefined,
    }));
  }

  async listByIdsWithItems(ids: string[]) {
    // For now, iterate through each ID since selectByIdsWithItems is not implemented
    const results = await Promise.all(
      ids.map(async (id) => {
        const result = await this.boardGroupDao.selectById({ id });
        return result;
      }),
    );
    return results.filter(Boolean);
  }

  async getOrCreateUploadsGroup(board_id: string, user_id: string): Promise<BoardGroup> {
    // For now, try to find existing uploads group by querying all groups and filtering
    const allGroups = await this.boardGroupDao.selectByBoardId({ board_id });
    let uploadsGroupDO = allGroups.find((group) => group.type === BoardGroupTypeEnum.UPLOADS);

    if (!uploadsGroupDO) {
      uploadsGroupDO = await this.boardGroupDao.insert({
        name: 'Attachments',
        creator_id: user_id,
        board_id,
        icon_name: 'GroupAsterisk',
        icon_color: '--function-mint',
        type: BoardGroupTypeEnum.UPLOADS,
      });
      // 插入成 board_item
      await this.boardItemDomain.putEntityToBoard({
        board_id,
        entity_type: BoardItemTypeEnum.BOARD_GROUP,
        entity_id: uploadsGroupDO.id,
      });
    }
    return this.do2entity(uploadsGroupDO);
  }

  async clone(
    template_board_group_id: string,
    user_id: string,
    board_id: string,
  ): Promise<BoardGroup> {
    const boardGroupDO = await this.boardGroupDao.selectById({
      id: template_board_group_id,
    });
    if (!boardGroupDO) {
      throw new NotFound({
        resource: ResourceEnum.BOARD_GROUP,
        id: template_board_group_id,
      });
    }
    const newBoardGroupDO = await this.boardGroupDao.insert({
      name: boardGroupDO.name,
      creator_id: user_id,
      board_id,
      icon_name: boardGroupDO.icon_name,
      icon_color: boardGroupDO.icon_color,
      type: boardGroupDO.type,
    });
    const newBoardGroup = this.do2entity(newBoardGroupDO);
    return newBoardGroup;
  }

  async estimateBoardGroupTotalTokens(board_group_id: string): Promise<number> {
    try {
      // For now, return 0 since estimateTotalTokens is not implemented
      // TODO: Implement token estimation logic
      this.logger.warn(
        `estimateBoardGroupTotalTokens not implemented for board_group_id: ${board_group_id}`,
      );
      return 0;
    } catch (error) {
      this.logger.error('estimateBoardGroupTotalTokens error', error);
      return 0;
    }
  }

  private do2entity(boardGroupDO: BoardGroupDO): BoardGroup {
    // eslint-disable-next-line unused-imports/no-unused-vars
    const { deleted_at, icon_name, icon_color, type, ...rest } = boardGroupDO;

    return {
      ...rest,
      icon: {
        name: icon_name ?? undefined,
        color: icon_color ?? undefined,
      },
      type: type as BoardGroupTypeEnum,
    };
  }
}
