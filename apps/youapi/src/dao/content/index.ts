/**
 * Content DAO - 内容数据访问对象
 *
 * Migrated from:
 * - youapp/src/lib/dao/content/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, eq, inArray, isNull, type SQL, sql } from 'drizzle-orm';
import { identity, pickBy } from 'lodash';

import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { contents } from '../db/public.schema';
import type { ContentDO, SelectContentParam, UpdateContentParam } from './types';

@Injectable()
export class ContentDAO extends BaseDAO<typeof contents._.config, ContentDO> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, contents);
  }

  async selectOne(param: SelectContentParam): Promise<ContentDO | null> {
    if (param.id) {
      return await this.selectOneById(param.id);
    }

    const clauses = [isNull(contents.deleted_at)];
    if (param.block_id) {
      clauses.push(eq(contents.block_id, param.block_id));
    } else if (param.snip_id) {
      clauses.push(eq(contents.snip_id, param.snip_id));
    }
    if (param.status?.length) {
      if (param.status.length === 1) {
        clauses.push(eq(contents.status, param.status[0]));
      } else {
        clauses.push(inArray(contents.status, param.status));
      }
    }
    if (param.format) {
      clauses.push(eq(contents.format, param.format));
    }
    if (param.language) {
      clauses.push(eq(contents.language, param.language));
    }

    const result = await this.db
      .select()
      .from(contents)
      .where(and(...clauses) as SQL);

    if (result.length === 1) {
      return result[0];
    }

    return null;
  }

  // selectOneById is inherited from BaseDAO

  async updateById(id: string, param: UpdateContentParam): Promise<ContentDO | null> {
    const updatedFields = pickBy(param, (v, k) => {
      return ['language', 'raw', 'plain', 'status'].includes(k) && identity(v);
    });

    return await this.updateOne(id, updatedFields);
  }

  async selectBySnipId(snip_id: string): Promise<ContentDO[]> {
    return await this.db
      .select()
      .from(contents)
      .where(and(eq(contents.snip_id, snip_id), isNull(contents.deleted_at)));
  }

  async updateManyContents(
    updates: { id: string; param: UpdateContentParam }[],
  ): Promise<{ id: string }[]> {
    if (!updates.length) return [];

    const processedUpdates = updates.map(({ id, param }) => {
      const updatedFields = pickBy(param, (v, k) => {
        return ['language', 'raw', 'plain', 'status'].includes(k) && identity(v);
      }) as Partial<UpdateContentParam>;

      return {
        id,
        ...updatedFields,
      };
    });

    const validUpdates = processedUpdates.filter((update) => Object.keys(update).length > 1);

    if (validUpdates.length === 0) return [];

    const updateColumns = ['language', 'raw', 'plain', 'status'] as const;
    const columnsToUpdate = updateColumns.filter((col) =>
      validUpdates.some((update) => update[col as keyof typeof update] !== undefined),
    );

    if (columnsToUpdate.length === 0) return [];

    const ids = validUpdates.map((update) => update.id);

    await this.db.execute(
      sql`UPDATE ${contents}
          SET ${sql.join(
            columnsToUpdate.map(
              (col) =>
                sql`${sql.identifier(col)} = CASE
                ${sql.join(
                  validUpdates
                    .filter((update) => update[col as keyof typeof update] !== undefined)
                    .map(
                      (update) =>
                        sql`WHEN id = ${update.id} THEN ${update[col as keyof typeof update]}`,
                    ),
                  sql` `,
                )}
                ELSE ${sql.identifier(col)}
              END`,
            ),
            sql`, `,
          )}
          WHERE id IN (${sql.join(
            ids.map((id) => sql`${id}`),
            sql`, `,
          )})`,
    );

    return validUpdates.map((update) => ({ id: update.id }));
  }

  async deleteManyContents(ids: string[]): Promise<number> {
    const result = await this.db
      .update(contents)
      .set({ deleted_at: new Date() } as any)
      .where(inArray(contents.id, ids));
    return result.length;
  }
}
