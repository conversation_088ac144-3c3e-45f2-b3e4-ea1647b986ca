/**
 * Content DAO Types - 内容数据类型
 *
 * Migrated from:
 * - youapp/src/lib/dao/content/types.ts
 */

import type { ContentFormatEnum, LanguageEnum, ProcessStatusEnum } from '../../common/types';
import type { contents } from '../db/public.schema';

export type ContentDO = typeof contents.$inferSelect;

export type InsertBlockContentParam = typeof contents.$inferInsert & {
  block_id: string;
};

export type UpdateBlockContentParam = Partial<
  Omit<typeof contents.$inferInsert, 'created_at' | 'updated_at' | 'deleted_at' | 'snip_id'>
>;

export type UpdateSnipContentParam = Partial<
  Omit<typeof contents.$inferInsert, 'created_at' | 'updated_at' | 'deleted_at' | 'block_id'>
>;

export interface SelectContentParam {
  id?: string;
  block_id?: string;
  snip_id?: string;
  status?: ProcessStatusEnum[];
  format?: ContentFormatEnum;
  language?: LanguageEnum;
}

export interface UpdateContentParam {
  language?: LanguageEnum;
  raw?: string;
  plain?: string;
  status?: ProcessStatusEnum;
}
