/**
 * Note DAO Types - 笔记数据类型
 * 定义笔记相关的数据对象类型
 *
 * Migrated from:
 * - youapp/src/lib/dao/note/types.ts
 */

import type { PgUpdateSetSource } from 'drizzle-orm/pg-core';
import type { NoteSourceEntityTypeEnum } from '../../common/types';
import type { notes } from '../db/public.schema';

export type NoteDO = typeof notes.$inferSelect;
export type InsertNoteDOParam = typeof notes.$inferInsert;
export type UpdateNoteDOParam = Omit<
  PgUpdateSetSource<typeof notes>,
  'id' | 'created_at' | 'deleted_at'
>;

export interface SelectNoteParam {
  space_id: string;
  board_id?: string;
  source?: {
    entity_type: NoteSourceEntityTypeEnum;
    entity_id: string;
  };
}
