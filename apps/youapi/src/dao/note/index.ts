/**
 * Note DAO - 笔记数据访问对象
 * 管理笔记的CRUD操作
 *
 * Migrated from:
 * - youapp/src/lib/dao/note/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, eq, inArray, isNull } from 'drizzle-orm';

import { NotFound, ResourceEnum } from '../../common/errors';
import { DatabaseService } from '../db/database.service';
import { notes } from '../db/public.schema';
import type { InsertNoteDOParam, NoteDO, SelectNoteParam, UpdateNoteDOParam } from './types';

@Injectable()
export class NoteDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  async insert(param: InsertNoteDOParam): Promise<NoteDO> {
    const result = await this.db.insert(notes).values(param).returning();
    return result[0];
  }

  async update(id: string, param: UpdateNoteDOParam): Promise<NoteDO> {
    const result = await this.db
      .update(notes)
      .set({
        ...param,
        updated_at: new Date(),
      } as UpdateNoteDOParam)
      .where(and(isNull(notes.deleted_at), eq(notes.id, id)))
      .returning();

    return result[0];
  }

  async delete(id: string): Promise<void> {
    await this.db
      .update(notes)
      .set({
        deleted_at: new Date(),
      } as UpdateNoteDOParam)
      .where(and(isNull(notes.deleted_at), eq(notes.id, id)));
  }

  async deleteMany(ids: string[]): Promise<void> {
    await this.db
      .update(notes)
      .set({
        deleted_at: new Date(),
      } as UpdateNoteDOParam)
      .where(and(isNull(notes.deleted_at), inArray(notes.id, ids)));
  }

  async trySelectById(id: string): Promise<NoteDO | null> {
    const result = await this.db
      .select()
      .from(notes)
      .where(and(isNull(notes.deleted_at), eq(notes.id, id)));
    if (result.length === 0) {
      return null;
    }
    return result[0];
  }

  async selectById(id: string): Promise<NoteDO> {
    const result = await this.trySelectById(id);
    if (result === null) {
      throw new NotFound({
        resource: ResourceEnum.NOTE,
        id,
      });
    }
    return result;
  }

  async select(param: SelectNoteParam): Promise<NoteDO[]> {
    const conditions = [isNull(notes.deleted_at), eq(notes.space_id, param.space_id)];
    if (param.board_id) {
      conditions.push(eq(notes.board_id, param.board_id));
    }
    if (param.source) {
      conditions.push(eq(notes.entity_type, param.source.entity_type));
      conditions.push(eq(notes.entity_id, param.source.entity_id));
    }
    return this.db
      .select()
      .from(notes)
      .where(and(...conditions));
  }
}
