/**
 * User Customer Relation DAO - 用户客户关系数据访问对象
 * 处理用户与Stripe客户关系的数据库操作
 */

import { Injectable } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { user_customer_relations } from '../db/public.schema';

export type UserCustomerRelationDO = {
  created_at: Date;
  user_id: string;
  customer_id: string;
};

@Injectable()
export class UserCustomerRelationDAO extends BaseDAO<
  typeof user_customer_relations._.config,
  UserCustomerRelationDO
> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, user_customer_relations);
  }

  /**
   * Try to select customer ID by user ID
   */
  async trySelectCustomerIdByUserId(userId: string): Promise<string | null> {
    const result = await this.selectOneByQuery(eq(this.table.user_id, userId));
    return result?.customer_id || null;
  }

  /**
   * Insert a new user-customer relation
   */
  async insert(param: { user_id: string; customer_id: string }): Promise<UserCustomerRelationDO> {
    return this.insertOne(param);
  }

  /**
   * Delete user-customer relation by user ID
   */
  async deleteByUserId(userId: string): Promise<void> {
    // Since this table doesn't have deleted_at, we'll use a direct delete
    await this.db.delete(this.table).where(eq(this.table.user_id, userId));
  }
}
