/**
 * Thought DAO - 思考数据访问对象
 * 管理思考内容的CRUD操作
 *
 * Migrated from:
 * - youapp/src/lib/dao/thought/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, count, desc, eq, gte, inArray, isNull, lte, sql } from 'drizzle-orm';
import { NotFound, ResourceEnum } from '../../common/errors';
import { BoardStatusEnum } from '../../common/types';
import { DatabaseService } from '../db/database.service';
import { board_items, boards, snip_thought_relations, thoughts } from '../db/public.schema';
import type {
  InsertThoughtDOParam,
  PagingParam,
  SelectThoughtParam,
  ThoughtBriefDTO,
  ThoughtDO,
  UpdateThoughtDOParam,
} from './types';

@Injectable()
export class ThoughtDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  async select(param: SelectThoughtParam): Promise<ThoughtDO[]> {
    const conditions = [isNull(thoughts.deleted_at), eq(thoughts.space_id, param.space_id)];
    if (param.ids) {
      conditions.push(inArray(thoughts.id, param.ids));
    }
    return this.db
      .select()
      .from(thoughts)
      .where(and(...conditions))
      .orderBy(desc(thoughts.updated_at));
  }

  /** 没有关联到 board 的认为是 unused */
  async selectUnused(space_id: string, limit?: number): Promise<ThoughtDO[]> {
    const query = this.db
      .select({ thoughts })
      .from(thoughts)
      .leftJoin(
        board_items,
        and(eq(thoughts.id, board_items.thought_id), isNull(board_items.deleted_at)),
      )
      .where(
        and(eq(thoughts.space_id, space_id), isNull(thoughts.deleted_at), isNull(board_items.id)),
      )
      .orderBy(desc(thoughts.updated_at))
      .$dynamic();
    if (limit) {
      query.limit(limit);
    }
    const result = await query;
    return result.map((it) => it.thoughts);
  }

  async selectInProgress(space_id: string): Promise<ThoughtDO[]> {
    const result = await this.db
      .select({ thoughts })
      .from(thoughts)
      .innerJoin(board_items, eq(thoughts.id, board_items.thought_id))
      .innerJoin(boards, eq(board_items.board_id, boards.id))
      .where(
        and(
          isNull(thoughts.deleted_at),
          isNull(board_items.deleted_at),
          isNull(boards.deleted_at),
          eq(thoughts.space_id, space_id),
          eq(boards.status, BoardStatusEnum.IN_PROGRESS),
        ),
      )
      .orderBy(desc(thoughts.updated_at));
    return result.map((it) => it.thoughts);
  }

  async trySelectById(id: string): Promise<ThoughtDO | null> {
    const result = await this.db
      .select()
      .from(thoughts)
      .where(and(isNull(thoughts.deleted_at), eq(thoughts.id, id)));
    if (result.length === 0) {
      return null;
    }
    return result[0];
  }

  async selectById(id: string): Promise<ThoughtDO> {
    const result = await this.trySelectById(id);
    if (!result) {
      throw new NotFound({
        resource: ResourceEnum.THOUGHT,
        id,
      });
    }
    return result;
  }

  async selectByRelatedSnipId(related_snip_id: string): Promise<ThoughtDO[]> {
    const result = await this.db
      .select({ thoughts })
      .from(thoughts)
      .innerJoin(snip_thought_relations, eq(thoughts.id, snip_thought_relations.thought_id))
      .where(
        and(
          isNull(thoughts.deleted_at),
          isNull(snip_thought_relations.deleted_at),
          eq(snip_thought_relations.snip_id, related_snip_id),
        ),
      )
      .orderBy(desc(thoughts.updated_at));
    return result.map((it) => it.thoughts);
  }

  async tryGetBriefById(id: string): Promise<ThoughtBriefDTO | null> {
    const result = await this.db
      .select({
        id: thoughts.id,
        title: thoughts.title,
      })
      .from(thoughts)
      .where(and(isNull(thoughts.deleted_at), eq(thoughts.id, id)));
    if (result.length === 0) {
      return null;
    }
    return result[0];
  }

  async getBriefById(id: string): Promise<ThoughtBriefDTO> {
    const briefDO = await this.tryGetBriefById(id);
    if (!briefDO) {
      throw new NotFound({
        resource: ResourceEnum.THOUGHT,
        id,
      });
    }
    return briefDO;
  }

  async selectBriefsByIds(ids: string[]): Promise<ThoughtBriefDTO[]> {
    return this.db
      .select({
        id: thoughts.id,
        title: thoughts.title,
      })
      .from(thoughts)
      .where(and(isNull(thoughts.deleted_at), inArray(thoughts.id, ids)));
  }

  async insert(insertParam: InsertThoughtDOParam): Promise<ThoughtDO> {
    const result = await this.db.insert(thoughts).values(insertParam).returning();
    return result[0];
  }

  async update(id: string, updateParam: UpdateThoughtDOParam): Promise<ThoughtDO> {
    const result = await this.db
      .update(thoughts)
      .set({
        ...updateParam,
        updated_at: new Date(),
      } as any)
      .where(eq(thoughts.id, id))
      .returning();
    return result[0];
  }

  async delete(id: string): Promise<void> {
    await this.db
      .update(thoughts)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(and(isNull(thoughts.deleted_at), eq(thoughts.id, id)));
  }

  async deleteByIds(ids: string[]): Promise<void> {
    await this.db
      .update(thoughts)
      .set({ deleted_at: new Date() } as any)
      .where(inArray(thoughts.id, ids));
  }

  /** 超级危险，仅供定时搜索结果同步使用 */
  async superDangerousSelectAllByUpdatedAtRange(
    timeStart: Date,
    timeEnd: Date,
    paging: Required<PagingParam>,
    userId?: string,
  ): Promise<ThoughtDO[]> {
    return this.db
      .select()
      .from(thoughts)
      .where(
        and(
          lte(thoughts.updated_at, timeEnd),
          gte(thoughts.updated_at, timeStart),
          isNull(thoughts.deleted_at),
          userId ? eq(thoughts.creator_id, userId) : undefined,
        ),
      )
      .offset(paging.current * paging.pageSize)
      .limit(paging.pageSize);
  }

  async count(userId?: string, startDate?: Date, endDate?: Date): Promise<{ count: number }[]> {
    return this.db
      .select({ count: count(thoughts.id) })
      .from(thoughts)
      .where(
        and(
          isNull(thoughts.deleted_at),
          userId ? eq(thoughts.creator_id, userId) : undefined,
          startDate ? gte(thoughts.updated_at, startDate) : undefined,
          endDate ? lte(thoughts.updated_at, endDate) : undefined,
        ),
      );
  }

  async getLatestTimestamp(userId?: string): Promise<{ timestamp: unknown }[]> {
    return this.db
      .select({
        timestamp: sql`(max(${thoughts.updated_at}) AT TIME ZONE 'UTC')::timestamp`,
      })
      .from(thoughts)
      .where(
        and(isNull(thoughts.deleted_at), userId ? eq(thoughts.creator_id, userId) : undefined),
      );
  }
}
