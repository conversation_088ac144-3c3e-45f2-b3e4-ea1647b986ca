/**
 * Thought DAO Types - 思考数据类型
 * 定义思考相关的数据对象类型
 *
 * Migrated from:
 * - youapp/src/lib/dao/thought/types.ts
 */

import type { PgUpdateSetSource } from 'drizzle-orm/pg-core';
import type { thoughts } from '../db/public.schema';

export type ThoughtDO = typeof thoughts.$inferSelect;
export type InsertThoughtDOParam = typeof thoughts.$inferInsert;
export type UpdateThoughtDOParam = Omit<
  PgUpdateSetSource<typeof thoughts>,
  'id' | 'created_at' | 'deleted_at'
>;

export type ThoughtBriefDTO = Pick<ThoughtDO, 'id' | 'title'>;

export interface SelectThoughtParam {
  space_id: string;
  ids?: string[];
}

export interface PagingParam {
  current: number;
  pageSize: number;
}
