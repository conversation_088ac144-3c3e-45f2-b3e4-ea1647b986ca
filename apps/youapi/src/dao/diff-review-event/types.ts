/**
 * Diff Review Event DAO Types
 * 差异审查事件数据访问对象类型定义
 *
 * Migrated from:
 * - youapp/src/lib/dao/diff-review-event/types.ts
 */

import type { diff_review_events } from '../db/public.schema';

// Database Object (DO) - 数据库实体类型
export type DiffReviewEventDO = typeof diff_review_events.$inferSelect;

// Insert parameter type
export type InsertDiffReviewEventDOParam = typeof diff_review_events.$inferInsert;

// Update parameter type (Note: this table doesn't support updates, only inserts)
export type UpdateDiffReviewEventDOParam = Partial<
  Omit<InsertDiffReviewEventDOParam, 'id' | 'created_at' | 'deleted_at'>
>;
