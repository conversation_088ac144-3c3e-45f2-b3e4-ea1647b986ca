/**
 * Diff Review Event DAO - 差异审查事件数据访问对象
 * 管理差异审查事件的CRUD操作
 *
 * Migrated from:
 * - youapp/src/lib/dao/diff-review-event/index.ts
 */

import { Injectable } from '@nestjs/common';
import { desc, eq } from 'drizzle-orm';

import { DatabaseService } from '../db/database.service';
import { diff_review_events } from '../db/public.schema';
import type { DiffReviewEventDO, InsertDiffReviewEventDOParam } from './types';

@Injectable()
export class DiffReviewEventDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  async insert(param: InsertDiffReviewEventDOParam): Promise<DiffReviewEventDO> {
    const result = await this.db.insert(diff_review_events).values(param).returning();
    return result[0];
  }

  async delete(id: string): Promise<void> {
    await this.db.delete(diff_review_events).where(eq(diff_review_events.id, id));
  }

  async trySelectById(id: string): Promise<DiffReviewEventDO | null> {
    const result = await this.db
      .select()
      .from(diff_review_events)
      .where(eq(diff_review_events.id, id))
      .limit(1);
    return result.length > 0 ? result[0] : null;
  }

  async selectByThoughtId(thoughtId: string): Promise<DiffReviewEventDO[]> {
    return await this.db
      .select()
      .from(diff_review_events)
      .where(eq(diff_review_events.thought_id, thoughtId))
      .orderBy(desc(diff_review_events.created_at));
  }
}
