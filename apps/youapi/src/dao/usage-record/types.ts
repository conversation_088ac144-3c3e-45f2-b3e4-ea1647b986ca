/**
 * Usage Record DAO Types
 * 使用记录数据访问对象类型定义
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/dao/usage-record/types.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/domain/usage-record/types.ts
 */

import type { QuotaResourceEnum } from '@/common/types';

// DAO types (database objects)
export interface UsageRecordDO {
  id: string;
  created_at: Date;
  deleted_at?: Date | null;
  space_id: string;
  user_id: string;
  resource: QuotaResourceEnum;
  amount: number;
  extra?: string | null;
}

export interface InsertUsageRecordDOParam {
  space_id: string;
  user_id: string;
  resource: QuotaResourceEnum;
  amount: number;
  extra?: string;
}

// Domain types (from subscription domain)
export enum QuotaPeriodEnum {
  DAY = 'day',
  MONTH = 'month',
  TOTAL = 'total',
}

export interface Quota {
  resource: QuotaResourceEnum;
  limit: number;
  period: QuotaPeriodEnum;
  unit?: string;
  normalizer?: number;
}

// Query and ordering types
export enum OrderByDirectionEnum {
  ASC = 'asc',
  DESC = 'desc',
}

export interface OrderByResourceParam {
  resource: QuotaResourceEnum;
  direction?: OrderByDirectionEnum;
}

// Usage record entity (domain object)
export interface UsageRecord {
  id: string;
  created_at: Date;
  space_id: string;
  user_id: string;
  resource: QuotaResourceEnum;
  amount: number;
  extra?: string;
}

// Create usage record parameters
export interface CreateUsageRecordParam {
  space_id?: string;
  user_id?: string;
  resource: QuotaResourceEnum;
  amount: number;
  extra?: string;
}

// Storage usage specific types
export enum StorageUsageFromEnum {
  EDITOR = 'editor',
  SNIP = 'snip',
  TRANSFER = 'transfer',
  UPLOAD = 'upload',
}

export interface CreateStorageUsageRecordParam {
  space_id?: string;
  user_id?: string;
  from: StorageUsageFromEnum;
  file: {
    name: string;
    mime_type: string;
    size: number;
    storage_url: string;
    original_url?: string;
  };
}

// AI service usage types
export interface CreateLLMUsageRecordParam {
  space_id?: string;
  user_id?: string;
  completion_tokens: number;
  prompt_tokens: number;
  total_tokens: number;
  biz_type?: 'search' | 'chat' | 'action';
  biz_target?: string;
  prompt_name?: string;
  model?: string;
  cache_hit?: boolean;
  provider?: string;
}

export interface CreateEmbeddingUsageRecordParam {
  space_id?: string;
  user_id?: string;
  amount: number;
  model?: string;
  provider?: string;
  cache_hit?: boolean;
}

export interface CreateASRUsageRecordParam {
  space_id?: string;
  user_id?: string;
  duration: number;
}

export interface CreateTTSUsageRecordParam {
  space_id?: string;
  user_id?: string;
  voice?: string;
  /** 文本生成音频长度 */
  amount: number;
}

// Search service usage types
export interface CreateBraveSearchUsageRecordParam {
  space_id?: string;
  user_id?: string;
}

export interface CreateJinaUsageRecordParam {
  space_id?: string;
  user_id?: string;
  amount: number;
}

export interface CreateSerpSearchUsageRecordParam {
  space_id?: string;
  user_id?: string;
  service?: string;
}

export interface CreateImageGenerationUsageRecordParam {
  space_id?: string;
  user_id?: string;
  amount: number;
}

// User space quota usage for admin dashboard
export interface UserSpaceQuotaUsage
  extends Record<QuotaResourceEnum, { limit: number; usage: number }> {
  space_id: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

// Cost calculation types
export interface ResourceCostRate {
  // Storage cost per GB per month
  storage: number;
  // LLM cost per 1K tokens
  llm: number;
  // ASR cost per minute
  asr: number;
  // TTS cost per 1K characters
  tts: number;
  // Embedding cost per 1K tokens
  embedding: number;
  // Brave Search cost per query
  brave_search: number;
  // SERP Search cost per query
  serp_search: number;
  // Google Search cost per query
  google_search: number;
  // Bocha Search cost per query
  bocha_search: number;
  // Jina Scraping cost per query
  jina: number;
  // Image Generation cost per image
  image_generation: number;
}

export interface ResourceCost {
  amount: number; // The actual cost in USD
  usage: number; // Raw usage amount
  unit: string; // Display unit (GB, tokens, minutes, etc)
  rate: string; // Display rate (e.g. "$0.02/1K tokens")
}

export interface SpaceResourceCosts {
  storage: ResourceCost;
  llm: ResourceCost;
  asr: ResourceCost;
  tts: ResourceCost;
  embedding: ResourceCost;
  brave_search: ResourceCost;
  serp_search: ResourceCost;
  google_search: ResourceCost;
  bocha_search: ResourceCost;
  jina: ResourceCost;
  image_generation: ResourceCost;
  total: number; // Sum of all resource costs
}
