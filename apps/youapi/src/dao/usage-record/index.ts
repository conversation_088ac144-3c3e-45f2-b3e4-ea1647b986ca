/**
 * Usage Record DAO - Handles usage record data access operations
 * 使用记录数据访问对象 - 处理使用记录相关的数据库操作
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/dao/usage-record/index.ts
 */

import { Injectable } from '@nestjs/common';
import { endOfMonth, parse, startOfMonth } from 'date-fns';
import { and, asc, desc, eq, gte, isNull, lte, sql } from 'drizzle-orm';

import type { QuotaResourceEnum } from '@/common/types';
import { users } from '../db/auth.schema';
import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { spaces, usage_records } from '../db/public.schema';

import {
  type InsertUsageRecordDOParam,
  type OrderByResourceParam,
  type Quota,
  QuotaPeriodEnum,
  type UsageRecordDO,
} from './types';

@Injectable()
export class UsageRecordDAO extends BaseDAO<typeof usage_records._.config, UsageRecordDO> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, usage_records);
  }

  /**
   * 计算指定时间段内的使用量总和
   */
  async sum(
    space_id: string,
    resource: QuotaResourceEnum,
    start?: Date,
    end?: Date,
  ): Promise<number> {
    const conditions = [
      isNull(usage_records.deleted_at),
      eq(usage_records.space_id, space_id),
      eq(usage_records.resource, resource),
    ];

    if (start) {
      conditions.push(gte(usage_records.created_at, start));
    }
    if (end) {
      conditions.push(lte(usage_records.created_at, end));
    }

    const result = await this.db
      .select({ usage: sql<number>`sum(${usage_records.amount})` })
      .from(usage_records)
      .where(and(...conditions));

    return result.length > 0 ? result[0].usage || 0 : 0;
  }

  /**
   * 插入单条使用记录
   */
  async insert(insertParam: InsertUsageRecordDOParam): Promise<UsageRecordDO> {
    const result = await this.db.insert(usage_records).values(insertParam).returning();
    return result[0] as UsageRecordDO;
  }

  /**
   * 批量插入使用记录
   */
  async insertMany(insertParams: InsertUsageRecordDOParam[]): Promise<UsageRecordDO[]> {
    if (insertParams.length === 0) {
      return [];
    }
    return this.db.insert(usage_records).values(insertParams).returning() as Promise<
      UsageRecordDO[]
    >;
  }

  /**
   * 查询用户空间配额使用情况
   * 用于管理后台统计和分析
   */
  async selectUserSpaceQuotaUsages(
    quotas: Quota[],
    month: string,
    order_by?: OrderByResourceParam,
  ) {
    const monthDate = parse(month, 'yyyy-MM', new Date());
    const start = startOfMonth(monthDate);
    const end = endOfMonth(monthDate);
    const startStr = start.toISOString();
    const endStr = end.toISOString();

    const columns = {
      space_id: spaces.id,
      user: {
        id: users.id,
        name: sql<string>`${users.raw_user_meta_data}->>'name'`,
        email: sql<string>`${users.raw_user_meta_data}->>'email'`,
      },
    };

    // 动态构建每个资源的使用量和限制列
    quotas.forEach((quota) => {
      (columns as any)[quota.resource] = {
        usage:
          quota.period === QuotaPeriodEnum.TOTAL
            ? sql<number>`sum(case when ${usage_records.resource} = ${quota.resource} then ${usage_records.amount} else 0 end)`.mapWith(
                Number,
              )
            : sql<number>`sum(case when ${usage_records.resource} = ${quota.resource} and ${usage_records.created_at} between ${startStr} and ${endStr} then ${usage_records.amount} else 0 end)`.mapWith(
                Number,
              ),
        limit: sql<number>`${quota.limit}`.mapWith(Number),
        period: sql<QuotaPeriodEnum>`${quota.period}`,
      };
    });

    const query = this.db
      .select(columns)
      .from(spaces)
      .leftJoin(users, eq(spaces.creator_id, users.id))
      .leftJoin(
        usage_records,
        and(
          eq(spaces.id, usage_records.space_id),
          isNull(spaces.deleted_at),
          isNull(usage_records.deleted_at),
        ),
      )
      .groupBy(spaces.id, users.id);

    if (order_by) {
      const column = (columns as any)[order_by.resource].usage;
      return query.orderBy(order_by.direction === 'asc' ? asc(column) : desc(column));
    } else {
      return query;
    }
  }
}
