/**
 * Thought Version DAO Types
 * 思考版本数据访问对象类型定义
 *
 * Migrated from:
 * - youapp/src/lib/dao/thought-version/types.ts
 */

import type { thought_versions } from '../db/public.schema';

// Database Object (DO) - 数据库实体类型
export type ThoughtVersionDO = typeof thought_versions.$inferSelect;

// Insert parameter type
export type InsertThoughtVersionDOParam = typeof thought_versions.$inferInsert;

// Update parameter type
export type UpdateThoughtVersionDOParam = Partial<
  Omit<InsertThoughtVersionDOParam, 'id' | 'created_at' | 'deleted_at'>
>;
