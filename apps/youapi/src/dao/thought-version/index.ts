/**
 * Thought Version DAO - 思考版本数据访问对象
 * 管理思考版本的CRUD操作
 *
 * Migrated from:
 * - youapp/src/lib/dao/thought-version/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, desc, eq, isNull } from 'drizzle-orm';

import { DatabaseService } from '../db/database.service';
import { thought_versions } from '../db/public.schema';
import type {
  InsertThoughtVersionDOParam,
  ThoughtVersionDO,
  UpdateThoughtVersionDOParam,
} from './types';

@Injectable()
export class ThoughtVersionDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  async insert(param: InsertThoughtVersionDOParam): Promise<ThoughtVersionDO> {
    const result = await this.db.insert(thought_versions).values(param).returning();
    return result[0];
  }

  async update(id: string, param: UpdateThoughtVersionDOParam): Promise<ThoughtVersionDO> {
    const result = await this.db
      .update(thought_versions)
      .set({
        ...param,
        updated_at: new Date(),
      } as any)
      .where(and(eq(thought_versions.id, id), isNull(thought_versions.deleted_at)))
      .returning();
    return result[0];
  }

  async delete(id: string): Promise<void> {
    await this.db
      .update(thought_versions)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(and(eq(thought_versions.id, id), isNull(thought_versions.deleted_at)));
  }

  async trySelectById(id: string): Promise<ThoughtVersionDO | null> {
    const result = await this.db
      .select()
      .from(thought_versions)
      .where(and(eq(thought_versions.id, id), isNull(thought_versions.deleted_at)))
      .limit(1);
    return result.length > 0 ? result[0] : null;
  }

  async selectByThoughtId(thoughtId: string): Promise<ThoughtVersionDO[]> {
    return await this.db
      .select()
      .from(thought_versions)
      .where(and(eq(thought_versions.thought_id, thoughtId), isNull(thought_versions.deleted_at)))
      .orderBy(desc(thought_versions.created_at));
  }

  async trySelectLatestByThoughtId(thoughtId: string): Promise<ThoughtVersionDO | null> {
    const result = await this.db
      .select()
      .from(thought_versions)
      .where(and(eq(thought_versions.thought_id, thoughtId), isNull(thought_versions.deleted_at)))
      .orderBy(desc(thought_versions.created_at))
      .limit(1);
    return result.length > 0 ? result[0] : null;
  }
}
