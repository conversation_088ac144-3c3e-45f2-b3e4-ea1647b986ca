import { Injectable } from '@nestjs/common';
import { and, asc, eq, getTableColumns, inArray, isNull } from 'drizzle-orm';

import { NotFound, ResourceEnum } from '../../common/errors';
import type { AssistantTypeEnum } from '../../common/types';
import { DatabaseService } from '../db/database.service';
import { assistants, space_assistant_configs } from '../db/public.schema';
import type { AssistantDO, InsertAssistantDOParam, UpdateAssistantDOParam } from './types';

@Injectable()
export class AssistantDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * 根据ID获取Assistant，如果不存在返回null
   */
  async trySelectById(id: string): Promise<AssistantDO | null> {
    const result = await this.databaseService.db
      .select()
      .from(assistants)
      .where(and(isNull(assistants.deleted_at), eq(assistants.id, id)));

    if (result.length === 0) {
      return null;
    }

    return result[0];
  }

  /**
   * 根据ID获取Assistant，如果不存在抛出NotFound异常
   */
  async selectById(id: string): Promise<AssistantDO> {
    const result = await this.trySelectById(id);
    if (!result) {
      throw new NotFound({
        resource: ResourceEnum.ASSISTANT,
        id,
      });
    }
    return result;
  }

  /**
   * 创建Assistant
   */
  async insert(param: InsertAssistantDOParam): Promise<AssistantDO> {
    const result = await this.databaseService.db.insert(assistants).values(param).returning();
    return result[0];
  }

  /**
   * 更新Assistant
   */
  async update(id: string, updateParam: UpdateAssistantDOParam): Promise<AssistantDO> {
    const result = await this.databaseService.db
      .update(assistants)
      .set(updateParam)
      .where(and(isNull(assistants.deleted_at), eq(assistants.id, id)))
      .returning();

    return result[0];
  }

  /**
   * 软删除Assistant
   */
  async delete(id: string): Promise<void> {
    await this.databaseService.db
      .update(assistants)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(and(isNull(assistants.deleted_at), eq(assistants.id, id)));
  }

  /**
   * 根据类型查询Assistant列表
   */
  async selectByTypes(types: AssistantTypeEnum[]): Promise<AssistantDO[]> {
    return this.databaseService.db
      .select()
      .from(assistants)
      .where(and(isNull(assistants.deleted_at), inArray(assistants.type, types)));
  }

  /**
   * 根据类型查询单个Assistant
   */
  async selectByType(type: AssistantTypeEnum): Promise<AssistantDO | null> {
    const result = await this.databaseService.db
      .select()
      .from(assistants)
      .where(and(isNull(assistants.deleted_at), eq(assistants.type, type)));
    return result[0] || null;
  }

  /**
   * 根据创建者ID查询Assistant列表（包含排序信息）
   */
  async selectByCreatorId(
    creator_id: string,
  ): Promise<Array<AssistantDO & { rank: string | null }>> {
    return this.databaseService.db
      .select({
        ...getTableColumns(assistants),
        rank: space_assistant_configs.rank,
      })
      .from(assistants)
      .leftJoin(space_assistant_configs, eq(assistants.id, space_assistant_configs.assistant_id))
      .where(
        and(
          isNull(assistants.deleted_at),
          eq(assistants.creator_id, creator_id),
          isNull(space_assistant_configs.deleted_at),
        ),
      )
      .orderBy(asc(space_assistant_configs.rank));
  }
}
