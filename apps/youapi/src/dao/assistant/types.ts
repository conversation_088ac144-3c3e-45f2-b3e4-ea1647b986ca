import type { PgUpdateSetSource } from 'drizzle-orm/pg-core';

import type { assistants } from '../db/public.schema';

/**
 * 数据库 Assistant 记录类型
 */
export type AssistantDO = typeof assistants.$inferSelect;

/**
 * 插入 Assistant 记录的参数类型
 */
export type InsertAssistantDOParam = typeof assistants.$inferInsert;

/**
 * 更新 Assistant 记录的参数类型
 * 排除了不可更新的字段
 */
export type UpdateAssistantDOParam = Omit<
  PgUpdateSetSource<typeof assistants>,
  'id' | 'created_at' | 'deleted_at'
>;
