import { Injectable } from '@nestjs/common';
import { eq } from 'drizzle-orm';

import { SnipTypeEnum } from '../../common/types/snip.types';
import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { actions } from '../db/public.schema';
import type { ActionDO } from './types';

@Injectable()
export class ActionDAO extends BaseDAO<(typeof actions)['_']['config'], ActionDO> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, actions);
  }

  async selectAll() {
    return this.db.select().from(actions);
  }

  async selectOneByName(name: string) {
    return super.selectOneByQuery(eq(actions.name, name));
  }

  async getSnipActions() {
    const actions = await this.selectAll();
    const cacheData = {
      snip: {
        [SnipTypeEnum.ARTICLE]: actions.filter((action) => {
          return ['overview'].includes(action.name);
        }),
        [SnipTypeEnum.PDF]: actions.filter((action) => {
          return ['overview'].includes(action.name);
        }),
        [SnipTypeEnum.OFFICE]: actions.filter((action) => {
          return ['overview'].includes(action.name);
        }),
        [SnipTypeEnum.TEXT_FILE]: actions.filter((action) => {
          return ['overview'].includes(action.name);
        }),
        [SnipTypeEnum.VOICE]: actions.filter((action) => {
          return ['overview', 'transcript'].includes(action.name);
        }),
        [SnipTypeEnum.VIDEO]: actions.filter((action) => {
          return ['overview', 'transcript'].includes(action.name);
        }),
        [SnipTypeEnum.SNIPPET]: [],
        [SnipTypeEnum.IMAGE]: [],
        [SnipTypeEnum.OTHER_WEBPAGE]: [],
        [SnipTypeEnum.UNKNOWN_WEBPAGE]: [],
        // deprecated
        [SnipTypeEnum.THOUGHT]: [],
      },
    };

    return cacheData.snip;
  }
}
