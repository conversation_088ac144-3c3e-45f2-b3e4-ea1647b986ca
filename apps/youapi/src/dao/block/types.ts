/**
 * Block DAO Types - 区块数据类型
 *
 * Migrated from:
 * - youapp/src/lib/dao/block/types.ts
 */

import type { PgUpdateSetSource } from 'drizzle-orm/pg-core';
import type {
  BlockDisplayEnum,
  LanguageEnum,
  ProcessStatusEnum,
  SnipFeatureEnum,
} from '../../common/types';
import type { ContentDO } from '../content/types';
import type { blocks } from '../db/public.schema';

export type BlockDO = typeof blocks.$inferSelect & {
  contents?: ContentDO[];
};

export type BlockInsertDO = typeof blocks.$inferInsert;
export type BlockUpdateDO = Omit<PgUpdateSetSource<typeof blocks>, 'id' | 'created_at'>;

export interface BlockQueryParams {
  type?: SnipFeatureEnum[];
  status?: ProcessStatusEnum[];
  display?: BlockDisplayEnum[];
  language?: LanguageEnum;
  action_id?: string;
  include_deleted?: boolean;
}

export interface PagingParam {
  current: number;
  pageSize: number;
}
