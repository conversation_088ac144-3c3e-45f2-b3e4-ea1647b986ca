/**
 * Block DAO - 区块数据访问对象
 *
 * Migrated from:
 * - youapp/src/lib/dao/block/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, desc, eq, gte, inArray, isNull, lte } from 'drizzle-orm';
import type { ContentDO } from '../content/types';
import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { blocks, contents, snips } from '../db/public.schema';
import type { BlockDO, BlockQueryParams, PagingParam } from './types';

@Injectable()
export class BlockDAO extends BaseDAO<typeof blocks._.config, BlockDO> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, blocks);
  }

  private handleJoinResult(
    result: Array<{
      blocks: typeof blocks.$inferSelect;
      contents: ContentDO | null;
    }>,
  ): BlockDO[] {
    const aggregates = result.reduce<Record<string, BlockDO>>((accu, row) => {
      const block = row.blocks;
      if (!accu[block.id]) {
        accu[block.id] = {
          ...block,
          contents: [],
        } as BlockDO;
      }
      if (row.contents) {
        accu[block.id]!.contents!.push(row.contents as ContentDO);
      }

      return accu;
    }, {});

    return Object.values(aggregates);
  }

  async selectOneById(block_id: string): Promise<BlockDO | null> {
    const query = this.db
      .select()
      .from(blocks)
      .leftJoin(contents, eq(blocks.id, contents.block_id))
      .where(and(eq(blocks.id, block_id), isNull(blocks.deleted_at), isNull(contents.deleted_at)));

    const result = await query;
    if (!result || !result.length) return null;

    return this.handleJoinResult(result)[0];
  }

  async selectOneByContentId(content_id: string): Promise<BlockDO | null> {
    const query = this.db
      .select({ id: blocks.id })
      .from(blocks)
      .leftJoin(contents, eq(blocks.id, contents.block_id))
      .where(
        and(eq(contents.id, content_id), isNull(blocks.deleted_at), isNull(contents.deleted_at)),
      )
      .limit(1);

    const result = await query;
    if (!result || !result.length) return null;

    return this.selectOneById(result[0].id);
  }

  async selectManyBySnip(snip_ids: string[], query_params?: BlockQueryParams): Promise<BlockDO[]> {
    const filter = query_params?.include_deleted ? [] : [isNull(blocks.deleted_at)];
    const contentFilter = query_params?.include_deleted ? [] : [isNull(contents.deleted_at)];

    if (snip_ids.length === 1) {
      filter.push(eq(blocks.snip_id, snip_ids[0]));
    } else {
      filter.push(inArray(blocks.snip_id, snip_ids));
    }

    if (query_params?.action_id) {
      filter.push(eq(blocks.action_id, query_params?.action_id));
    }

    if (query_params?.type?.length === 1) {
      filter.push(eq(blocks.type, query_params.type[0]));
    } else if (query_params?.type?.length) {
      filter.push(inArray(blocks.type, query_params.type));
    }

    if (query_params?.display?.length) {
      if (query_params.display.length === 1) {
        filter.push(eq(blocks.display, query_params.display[0]));
      } else {
        filter.push(inArray(blocks.display, query_params.display));
      }
    }

    if (query_params?.language) {
      contentFilter.push(eq(contents.language, query_params.language));
    }

    if (query_params?.status?.length) {
      if (query_params.status.length === 1) {
        contentFilter.push(eq(contents.status, query_params.status[0]));
      } else {
        contentFilter.push(inArray(contents.status, query_params.status));
      }
    }

    const query = this.db
      .select()
      .from(blocks)
      .leftJoin(contents, eq(blocks.id, contents.block_id))
      .where(and(...filter, ...contentFilter))
      .orderBy(desc(contents.updated_at));

    const result = await query;

    if (!result || !result.length) return [];

    return this.handleJoinResult(result);
  }

  async selectManyByURL(url: string, query_params?: BlockQueryParams): Promise<BlockDO[]> {
    const filter = query_params?.include_deleted ? [] : [isNull(blocks.deleted_at)];
    const contentFilter = query_params?.include_deleted ? [] : [isNull(contents.deleted_at)];

    filter.push(eq(blocks.origin_url, url));

    if (query_params?.action_id) {
      filter.push(eq(blocks.action_id, query_params?.action_id));
    }

    if (query_params?.type?.length === 1) {
      filter.push(eq(blocks.type, query_params.type[0]));
    } else if (query_params?.type?.length) {
      filter.push(inArray(blocks.type, query_params.type));
    }

    if (query_params?.display?.length) {
      if (query_params.display.length === 1) {
        filter.push(eq(blocks.display, query_params.display[0]));
      } else {
        filter.push(inArray(blocks.display, query_params.display));
      }
    }

    if (query_params?.language) {
      contentFilter.push(eq(contents.language, query_params.language));
    }

    if (query_params?.status?.length) {
      if (query_params.status.length === 1) {
        contentFilter.push(eq(contents.status, query_params.status[0]));
      } else {
        contentFilter.push(inArray(contents.status, query_params.status));
      }
    }

    const query = this.db
      .select()
      .from(blocks)
      .leftJoin(contents, eq(blocks.id, contents.block_id))
      .where(and(...filter, ...contentFilter))
      .orderBy(desc(contents.updated_at));

    const result = await query;

    if (!result || !result.length) return [];

    return this.handleJoinResult(result);
  }

  async deleteOneById(block_id: string): Promise<{
    block: BlockDO | null;
    contents: any[];
  }> {
    const block = await this.selectOneById(block_id);
    if (!block) return { block: null, contents: [] };

    await this.db
      .update(blocks)
      .set({ deleted_at: new Date() } as any)
      .where(and(eq(blocks.id, block_id), isNull(blocks.deleted_at)));

    const deleteContentsResult = await this.db
      .update(contents)
      .set({ deleted_at: new Date() } as any)
      .where(eq(contents.block_id, block_id))
      .returning();

    return {
      block,
      contents: deleteContentsResult,
    };
  }

  /**
   * Dangerously select all blocks with contents, use for search index update
   */
  async superDangerousSelectAllByUpdatedAtRange(
    timeStart: Date,
    timeEnd: Date,
    paging: Required<PagingParam>,
    userId?: string,
  ): Promise<BlockDO[]> {
    const result = await this.db
      .select()
      .from(blocks)
      .leftJoin(contents, eq(blocks.id, contents.block_id))
      .innerJoin(
        snips,
        and(
          eq(blocks.snip_id, snips.id),
          isNull(snips.deleted_at),
          userId ? eq(snips.creator_id, userId) : undefined,
        ),
      )
      .where(
        and(
          isNull(snips.deleted_at),
          isNull(blocks.deleted_at),
          gte(blocks.updated_at, timeStart),
          lte(blocks.updated_at, timeEnd),
        ),
      )
      .limit(paging.pageSize)
      .offset(paging.current * paging.pageSize);

    return this.handleJoinResult(result);
  }
}
