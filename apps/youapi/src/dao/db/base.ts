import { count, eq, inArray, SQL } from 'drizzle-orm';
import { PgInsertValue, PgTableWithColumns, TableConfig } from 'drizzle-orm/pg-core';
import { isNumber, max, min } from 'lodash';

import { PagingParam, PagingResponse } from '../../common/types';
import { DatabaseService } from './database.service';

export abstract class BaseDAO<T extends TableConfig, S extends PgTableWithColumns<T>['columns']> {
  protected table: PgTableWithColumns<T>;

  constructor(
    protected readonly databaseService: DatabaseService,
    table: PgTableWithColumns<T>,
  ) {
    this.table = table;
  }

  protected get db() {
    return this.databaseService.db;
  }

  protected transformDO(data: typeof this.table.$inferSelect): S | null {
    if (!data) return null;
    return data as S;
  }

  public async selectOneByQuery(query: SQL): Promise<S | null> {
    const result = await this.db.select().from(this.table).where(query).limit(1);
    return this.transformDO(result[0] as typeof this.table.$inferSelect);
  }

  public async selectOneById(id: string): Promise<S | null> {
    return this.selectOneByQuery(eq(this.table.id, id));
  }

  public async selectManyByQuery(query: SQL, orderBy?: SQL[]): Promise<Array<S>> {
    const result = await (orderBy?.length
      ? this.db
          .select()
          .from(this.table)
          .where(query)
          .orderBy(...orderBy)
      : this.db.select().from(this.table).where(query));
    if (!result || !result.length) return [];
    return result.map((item) => this.transformDO(item as typeof this.table.$inferSelect)) as S[];
  }

  public async selectManyByIds(ids: string[]): Promise<S[]> {
    return this.selectManyByQuery(inArray(this.table.id, ids));
  }

  public async insertOne(param: typeof this.table.$inferInsert): Promise<S> {
    const result = await this.db
      .insert(this.table)
      .values(param as PgInsertValue<typeof this.table>)
      .returning();

    return this.transformDO(result[0] as typeof this.table.$inferSelect) as S;
  }

  public async updateOne(id: string, param: typeof this.table.$inferUpdate): Promise<S> {
    const result = await this.db
      .update(this.table)
      .set(param)
      .where(eq(this.table.id, id))
      .returning();

    return this.transformDO(result[0] as typeof this.table.$inferSelect) as S;
  }

  public async updateByQuery(query: SQL, param: typeof this.table.$inferUpdate): Promise<Array<S>> {
    const result = await this.db.update(this.table).set(param).where(query).returning();

    return result.map((item) => this.transformDO(item)) as S[];
  }

  public async _list(query: SQL, param: PagingParam, orderBy?: SQL[]): Promise<PagingResponse<S>> {
    const pageSize = (
      isNumber(param.pageSize) ? min([max([param.pageSize, 1]), 100]) : 10
    ) as number;
    const current = (isNumber(param.current) ? max([param.current, 0]) : 0) as number;

    const [total, result] = await Promise.all([
      this.db.select({ count: count() }).from(this.table).where(query),
      orderBy?.length
        ? this.db
            .select()
            .from(this.table)
            .where(query)
            .limit(pageSize)
            .offset(current * pageSize)
            .orderBy(...orderBy)
        : this.db
            .select()
            .from(this.table)
            .where(query)
            .limit(pageSize)
            .offset(current * pageSize),
    ]);

    return {
      paging: {
        current,
        pageSize,
        total: total[0].count,
      },
      data: result.map((item) => this.transformDO(item as typeof this.table.$inferSelect) as S),
    };
  }
}
