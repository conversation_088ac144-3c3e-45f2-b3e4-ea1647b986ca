import { Injectable, type OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { drizzle, type PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

import type { SystemConfig } from '../../common/types';
import * as schema from './public.schema';

@Injectable()
export class DatabaseService implements OnModuleInit {
  private client: postgres.Sql;
  private _db!: PostgresJsDatabase<typeof schema>;

  constructor(private readonly configService: ConfigService<SystemConfig>) {
    this.client = postgres(this.configService.getOrThrow('DB_URL'), {
      max: 10,
      idle_timeout: 20,
      connect_timeout: 10,
      prepare: false,
      debug: this.configService.get('LOG_SQL') === 'true',
    });
  }

  onModuleInit() {
    this._db = drizzle(this.client, {
      schema: schema,
      logger: process.env.LOG_SQL === 'true',
    });
  }

  get db(): PostgresJsDatabase<typeof schema> {
    if (!this._db) {
      throw new Error(
        'Database not initialized. Make sure DatabaseService is properly configured.',
      );
    }
    return this._db;
  }

  async closeConnection(): Promise<void> {
    if (this.client) {
      await this.client.end();
    }
  }

  getClient() {
    return this.client;
  }
}
