import { jsonb, pgSchema, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';

export const auth = pgSchema('auth');

export const users = auth.table('users', {
  id: uuid('id').primaryKey(),
  email: varchar('email', { length: 255 }),
  phone: text('phone'),
  raw_app_meta_data: jsonb('raw_app_meta_data'),
  raw_user_meta_data: jsonb('raw_user_meta_data'),
  created_at: timestamp('created_at', { withTimezone: true }),
  updated_at: timestamp('updated_at', { withTimezone: true }),
  deleted_at: timestamp('deleted_at', { withTimezone: true }),
  last_sign_in_at: timestamp('last_sign_in_at', { withTimezone: true }),
  confirmed_at: timestamp('confirmed_at', { withTimezone: true }),
});
