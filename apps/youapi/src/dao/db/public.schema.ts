import { isNull, relations, sql } from 'drizzle-orm';
import {
  bigint,
  boolean,
  doublePrecision,
  index,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { uuidv7 } from 'uuidv7';
import type { z } from 'zod';

import {
  // System types
  ActionStatusEnum,
  ActionTypeEnum,
  AILanguageEnum,
  // Assistant types
  AssistantIconTypeEnum,
  AssistantRunModeEnum,
  type AssistantTool,
  AssistantTypeEnum,
  AssistantVisibilityEnum,
  type Author,
  BlockDisplayEnum,
  BlockHeightEnum,
  // Board types
  BoardGroupTypeEnum,
  BoardStatusEnum,
  BoardTypeEnum,
  // AI types
  ChatModeEnum,
  ChatOriginTypeEnum,
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  ContentFormatEnum,
  // Thought types
  DiffReviewEventActionEnum,
  type DiffReviewEventNode,
  DisplayLanguageEnum,
  EditCommand,
  EntityTypeEnum,
  FavoriteEntityTypeEnum,
  type Filter,
  LanguageEnum,
  type MessageContext,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
  NoteSourceEntityTypeEnum,
  // Space types
  PlaylistItemStatusEnum,
  // Subscription types
  PriceLookupKeyEnum,
  // Snip types (includes content, blocks, visibility)
  ProcessStatusEnum,
  QuotaResourceEnum,
  // API types
  type RestErrorInfo,
  SnipFeatureEnum,
  SnipFromEnum,
  SnipStatusEnum,
  SnipTypeEnum,
  type SpaceMetadata,
  SpaceStatusEnum,
  SubscriptionStatusEnum,
  ThoughtVersionTypeEnum,
  TitleTypeEnum,
  type UseToolSchema,
  VisibilityEnum,
} from '../../common/types';
import { getEnumValues } from '../../common/utils';

/**
 * Database schema migrated from youapp
 * Using uuidv7 for id fields to maintain order + randomness
 * Utilizing partial unique index for soft delete scenarios
 */

/**
 * - id 使用 uuidv7，兼顾顺序性 + 随机性
 * - 利用 partial unqiue index 来解决 soft delete 带来的唯一性问题
 */
export const spaces = pgTable(
  'spaces',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    creator_id: uuid('creator_id').notNull(),
    activated_at: timestamp('activated_at', { withTimezone: true }),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(SpaceStatusEnum),
    })
      .notNull()
      .default(SpaceStatusEnum.UNINITIALIZED),
    subscription_price: varchar('subscription_price', {
      enum: getEnumValues(PriceLookupKeyEnum),
      length: 64,
    }),
    trial_days: integer('trial_days'),
    trial_expires_at: timestamp('trial_expires_at', { withTimezone: true }),
    subscription_id: varchar('subscription_id', { length: 32 }),
    subscription_status: varchar('subscription_status', {
      length: 32,
      enum: getEnumValues(SubscriptionStatusEnum),
    }),
    subscription_next_billing_date: timestamp('subscription_next_billing_date', {
      withTimezone: true,
    }),
    subscription_created_at: timestamp('subscription_created_at', {
      withTimezone: true,
    }),
    metadata: jsonb('metadata').$type<SpaceMetadata>().notNull().default({}),
  },
  (t) => {
    return {
      // 目前只支持每个用户自动初始化一个 space，因此暂时要 creator_id 作统一索引（考虑软删除），未来删除掉这个索引
      a: uniqueIndex('spaces_creator_id_idx')
        .on(t.creator_id)
        /**
         * BUG: 这里虽然支持 where 语句，但生成的 SQL 里却缺失了，暂时先手工补上
         * https://github.com/drizzle-team/drizzle-orm/issues/1519
         */
        .where(isNull(t.deleted_at)),
      b: uniqueIndex('idx_spaces_subscription_id')
        .on(t.subscription_id)
        .where(isNull(t.deleted_at)),
    };
  },
);

export const system_configs = pgTable(
  'system_configs',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    creator_id: uuid('creator_id').notNull(),
    modifier_id: uuid('modifier_id').notNull(),
    key: varchar('key', { length: 255 }).notNull(),
    value: text('value').notNull(),
  },
  (t) => {
    return {
      a: uniqueIndex('system_configs_key_idx').on(t.key).where(isNull(t.deleted_at)),
    };
  },
);

export const user_preferences = pgTable('user_preferences', {
  id: uuid('id').primaryKey(),
  created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  display_language: varchar('display_language', {
    length: 255,
    enum: getEnumValues(DisplayLanguageEnum),
  }).notNull(),
  ai_response_language: varchar('ai_response_language', {
    length: 255,
    enum: getEnumValues(AILanguageEnum),
  }).notNull(),
  ai_2nd_response_language: varchar('ai_2nd_response_language', {
    length: 255,
    enum: getEnumValues(AILanguageEnum),
  })
    .default(AILanguageEnum.system)
    .notNull(),
  enable_bilingual: boolean('enable_bilingual').default(true).notNull(),
  detected_language: varchar('detected_language', {
    length: 255,
    enum: getEnumValues(LanguageEnum),
  }),
});

export const usage_records = pgTable(
  'usage_records',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    space_id: uuid('space_id').notNull(),
    user_id: uuid('user_id')
      .notNull()
      // 用一个固定占位的 uuid，然后再用脚本订正成正确的 user_id
      .default('019222d7-8450-7a24-ae08-c67e6a21b31f'),
    resource: varchar('resource', {
      enum: getEnumValues(QuotaResourceEnum),
    }).notNull(),
    amount: bigint('amount', { mode: 'number' }).notNull(),
    extra: text('extra'),
  },
  (t) => {
    return {
      a: index('usage_records_deleted_at_space_id_resource_idx').on(
        t.deleted_at,
        t.space_id,
        t.resource,
      ),
    };
  },
);

export const snips = pgTable(
  'snips',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    space_id: uuid('space_id').notNull(),
    creator_id: uuid('creator_id').notNull(),
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(SnipTypeEnum),
    }).notNull(),
    from: varchar('from', {
      length: 32,
      enum: getEnumValues(SnipFromEnum),
    }).notNull(),

    status: varchar('status', {
      length: 32,
      enum: getEnumValues(SnipStatusEnum),
    }),

    // 网页相关元信息
    webpage_url: varchar('webpage_url', { length: 2048 }),
    webpage_normalized_url: varchar('webpage_normalized_url', { length: 2048 }),
    webpage_title: varchar('webpage_title', { length: 2048 }),
    webpage_description: varchar('webpage_description'),
    webpage_site_name: varchar('webpage_site_name', { length: 255 }),
    webpage_site_host: varchar('webpage_site_host', { length: 255 }),
    webpage_site_favicon_url: varchar('webpage_site_favicon_url', {
      length: 2048,
    }),

    // 文件相关元信息
    file_name: varchar('file_name', { length: 2048 }),
    file_mime_type: varchar('file_mime_type', { length: 255 }),
    file_size: integer('file_size'),
    file_storage_url: varchar('file_storage_url', { length: 2048 }),
    file_original_url: varchar('file_original_url', { length: 2048 }),

    // 选区相关元信息
    parent_id: uuid('parent_id'),
    selection: text('selection'),
    annotation_raw: text('annotation_raw'),
    annotation_plain: text('annotation_plain'),

    // 解析出来的信息
    title: varchar('title', { length: 2048 }),
    authors: jsonb('authors').$type<Author[]>(),
    // jsonb 可以被索引，但转换成 jsonb 较慢，对于常用的信息，还是尽量拍平存到普通列中
    hero_image_url: varchar('hero_image_url', { length: 2048 }),
    published_at: timestamp('published_at', { withTimezone: true }),

    // 主体内容
    content_format: varchar('content_format', {
      length: 32,
      enum: getEnumValues(ContentFormatEnum),
    }),
    content_raw: text('content_raw'),
    content_plain: text('content_plain'),
    content_language: varchar('content_language', {
      length: 32,
      enum: getEnumValues(LanguageEnum),
    }),
    // 主要媒体播放地址
    play_url: varchar('play_url', { length: 2048 }),
    extra: text('extra'),

    visibility: varchar('visibility', {
      length: 10,
      enum: getEnumValues(VisibilityEnum),
    })
      .notNull()
      .default(VisibilityEnum.PRIVATE),
  },
  (table) => {
    return {
      a: index('idx_snips_deleted_at_space_id').on(table.deleted_at, table.space_id),
      b: index('idx_snips_deleted_at_space_id_from').on(
        table.deleted_at,
        table.space_id,
        table.from,
      ),
      c: index('idx_snips_deleted_at_space_id_type').on(
        table.deleted_at,
        table.space_id,
        table.type,
      ),
    };
  },
);

export const snipRelations = relations(snips, ({ one }) => ({
  space: one(spaces, {
    fields: [snips.space_id],
    references: [spaces.id],
  }),
}));

export const boards = pgTable(
  'boards',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    space_id: uuid('space_id').notNull(),
    creator_id: uuid('creator_id').notNull(),
    name: varchar('name', { length: 255 }).notNull(),
    description: varchar('description', { length: 2048 }).notNull(),
    icon_name: varchar('icon_name', { length: 255 }).notNull(),
    icon_color: varchar('icon_color', { length: 255 }).notNull(),
    pinned_at: timestamp('pinned_at', { withTimezone: true }),
    filter: jsonb('filter').$type<Filter>(),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(BoardStatusEnum),
    })
      .notNull()
      .default(BoardStatusEnum.IN_PROGRESS),
    hero_image_urls: jsonb('hero_image_urls').$type<string[]>(),
    intro: text('intro'),
    rank: varchar('rank', { length: 255 }).notNull().default('~'), // 占位符，用脚本订正
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(BoardTypeEnum),
    })
      .notNull()
      .default(BoardTypeEnum.NORMAL),
  },
  (table) => {
    return {
      a: index('idx_boards_deleted_at_space_id').on(table.deleted_at, table.space_id),
      // TODO 待 rank 订正完之后才能加上
      // b: uniqueIndex("idx_boards_space_id_rank")
      //   .on(table.space_id, table.rank)
      //   .where(isNull(table.deleted_at)),
    };
  },
);

export const board_items = pgTable(
  'board_items',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    board_id: uuid('board_id').notNull(),
    parent_board_group_id: uuid('parent_board_group_id'),
    snip_id: uuid('snip_id'),
    thought_id: uuid('thought_id'),
    board_group_id: uuid('board_group_id'),
    chat_id: uuid('chat_id'),
    rank: varchar('rank', { length: 255 }).notNull().default('~'), // 占位符，用脚本订正
  },
  (t) => {
    return {
      a: uniqueIndex('idx_board_items_board_id_snip_id')
        .on(t.board_id, t.snip_id)
        .where(isNull(t.deleted_at)),
      b: uniqueIndex('idx_board_items_board_id_thought_id')
        .on(t.board_id, t.thought_id)
        .where(isNull(t.deleted_at)),
      c: uniqueIndex('idx_board_items_board_id_board_group_id')
        .on(t.board_id, t.board_group_id)
        .where(isNull(t.deleted_at)),
      d: uniqueIndex('idx_board_items_board_id_chat_id')
        .on(t.board_id, t.chat_id)
        .where(isNull(t.deleted_at)),
      e: index('idx_board_items_deleted_at_board_id_parent_board_group_id_rank').on(
        t.deleted_at,
        t.board_id,
        t.parent_board_group_id,
        t.rank,
      ),
    };
  },
);

export const board_groups = pgTable('board_groups', {
  id: uuid('id')
    .primaryKey()
    .$default(() => uuidv7()),
  created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  deleted_at: timestamp('deleted_at', { withTimezone: true }),
  creator_id: uuid('creator_id').notNull(),
  board_id: uuid('board_id').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  icon_name: varchar('icon_name', { length: 255 }),
  icon_color: varchar('icon_color', { length: 255 }),
  type: varchar('type', {
    length: 32,
    enum: getEnumValues(BoardGroupTypeEnum),
  })
    .notNull()
    .default(BoardGroupTypeEnum.NORMAL),
});

export const actions = pgTable(
  'actions',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    name: varchar('name', { length: 255 }).notNull().unique(),
    description: varchar('description', { length: 2048 }),
    // TODO 等设计
    // icon_name: varchar("icon_name", { length: 255 }).notNull(),
    // icon_color: varchar("icon_color", { length: 255 }).notNull(),
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(ActionTypeEnum),
    }).notNull(),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(ActionStatusEnum),
    }).notNull(),
    llm_prompt_name: varchar('llm_prompt_name', { length: 255 }),
    result_block_type: varchar('result_block_type', {
      length: 32,
      enum: getEnumValues(SnipFeatureEnum),
    }),
    result_block_format: varchar('result_block_format', {
      length: 32,
      enum: getEnumValues(ContentFormatEnum),
    }),
    space_id: uuid('space_id'),
  },
  (table) => {
    return {
      actions_name: uniqueIndex('uk_actions_name').on(table.name).where(isNull(table.deleted_at)),
      space: index('idx_actions_space_id').on(table.space_id),
    };
  },
);

export const actionRelations = relations(actions, ({ one, many }) => ({
  blocks: many(blocks),
  space: one(spaces, {
    fields: [actions.space_id],
    references: [spaces.id],
  }),
}));

export const blocks = pgTable(
  'blocks',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    snip_id: uuid('snip_id'),
    board_id: uuid('board_id'),
    action_id: uuid('action_id'),
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(SnipFeatureEnum),
    }),
    related_snip_id: uuid('related_snip_id'),
    display: varchar('display', {
      length: 32,
      enum: getEnumValues(BlockDisplayEnum),
    })
      .notNull()
      .default(BlockDisplayEnum.SHOW),
    current_content_id: uuid('current_content_id'),
    origin_url: varchar('origin_url', { length: 2048 }),
  },
  (table) => {
    return {
      blocks_deleted_at: index('idx_blocks_deleted_at').on(table.deleted_at),
      blocks_snip_id: index('idx_blocks_snip_id').on(table.snip_id),
      blocks_url: index('idx_blocks_url').on(table.origin_url),
    };
  },
);

export const blockRelations = relations(blocks, ({ one, many }) => ({
  action: one(actions, {
    fields: [blocks.action_id],
    references: [actions.id],
  }),
  snip: one(snips, {
    fields: [blocks.snip_id],
    references: [snips.id],
  }),
  placements: many(placements),
  contents: many(contents),
}));

// 描述元素在容器中如何放置，包括顺序、高度等
export const placements = pgTable(
  'placements',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    // 排序值，小数
    rank: doublePrecision('rank').notNull(),
    // 高度
    height: varchar('height', {
      length: 255,
      enum: getEnumValues(BlockHeightEnum),
    }),
    // 关联 ID
    snip_id: uuid('snip_id').default(sql`null`).$type<string | null>(),
    block_id: uuid('block_id').default(sql`null`).$type<string | null>(),
  },
  (table) => {
    return {
      placements_rank: index('idx_placements_rank').on(table.rank, table.updated_at),
      placements_block_id: index('idx_placements_block_id').on(table.block_id),
      placements_snip_block: index('idx_placements_snip_block').on(
        table.deleted_at,
        table.snip_id,
        table.block_id,
      ),
    };
  },
);

export const placementRelations = relations(placements, ({ one }) => ({
  snip: one(snips, {
    fields: [placements.snip_id],
    references: [snips.id],
  }),
  block: one(blocks, {
    fields: [placements.block_id, placements.snip_id],
    references: [blocks.id, blocks.snip_id],
  }),
}));

export const contents = pgTable(
  'contents',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    format: varchar('format', {
      length: 32,
      enum: getEnumValues(ContentFormatEnum),
    }).notNull(),
    raw: text('raw'),
    plain: text('plain'),
    language: varchar('language', {
      length: 32,
      enum: getEnumValues(LanguageEnum),
    }),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(ProcessStatusEnum),
    }).notNull(),
    block_id: uuid('block_id'),
    snip_id: uuid('snip_id'),
    trace_id: varchar('trace_id', {
      length: 64,
    }),
  },
  (table) => {
    return {
      contents_deleted_at: index('idx_contents_deleted_at').on(table.deleted_at),
      contents_block_id: index('idx_contents_block_id').on(table.block_id),
      contents_snip_id: index('idx_contents_snip_id').on(table.snip_id),
    };
  },
);

export const contentRelations = relations(contents, ({ one }) => ({
  snip: one(snips, {
    fields: [contents.snip_id],
    references: [snips.id],
  }),
  block: one(blocks, {
    fields: [contents.block_id],
    references: [blocks.id],
  }),
}));

export const user_customer_relations = pgTable('user_customer_relations', {
  created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  user_id: uuid('user_id').notNull().unique(),
  // stripe 的 id 不是 uuid
  customer_id: varchar('customer_id').notNull(),
});

export const chats = pgTable(
  'chats',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    creatorId: uuid('creator_id').notNull(),
    originType: varchar('origin_type', {
      length: 32,
      enum: getEnumValues(ChatOriginTypeEnum),
    }).notNull(),
    originId: uuid('origin_id'),
    title: varchar('title', { length: 255 }),
    originUrl: varchar('origin_url', { length: 2048 }),
    context: text('context'),
    // @deprecated
    essence: text('essence'),
    // @deprecated
    essenceLastUpdatedAt: timestamp('essence_last_updated_at', {
      withTimezone: true,
    }),
    // @deprecated
    essenceTraceId: varchar('essence_trace_id', {
      length: 64,
    }),
    boardId: uuid('board_id'),
    mode: varchar('mode', {
      length: 32,
      enum: getEnumValues(ChatModeEnum),
    }).default(ChatModeEnum.CHAT),
    assistantId: uuid('assistant_id'),
  },
  (table) => {
    return {
      origin_and_creator: index('idx_origin_and_creator').on(
        table.deletedAt,
        table.creatorId,
        table.mode,
        table.originType,
        table.originId,
        table.originUrl,
      ),
      board_and_creator: index('idx_board_and_creator').on(
        table.deletedAt,
        table.creatorId,
        table.mode,
        table.boardId,
      ),
      creator: index('idx_creator').on(table.deletedAt, table.creatorId, table.mode),
      assistant: index('idx_assistant').on(
        table.deletedAt,
        table.originType,
        table.originId,
        table.mode,
        table.assistantId,
      ),
    };
  },
);

export const messages = pgTable(
  'chat_messages',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    chatId: uuid('chat_id').notNull(),
    role: varchar('role', {
      length: 32,
      enum: getEnumValues(MessageRoleEnum),
    }).notNull(),
    context: jsonb('context').$type<MessageContext[]>(),
    content: text('content'),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(MessageStatusEnum),
    }).default(MessageStatusEnum.QUEUED),
    traceId: varchar('trace_id', {
      length: 64,
    }),
    reasoning: text('reasoning'),
    model: varchar('model', { length: 255 }),
    error: jsonb('error').$type<RestErrorInfo>(),
    tools: jsonb('tools').$type<z.infer<typeof UseToolSchema>>(),
    command: jsonb('command').$type<EditCommand>(),
    mode: varchar('mode', {
      length: 32,
      enum: getEnumValues(MessageModeEnum),
    }),
    shortcut: jsonb('shortcut').$type<{
      id: string;
      name: string;
    }>(),
  },
  (table) => {
    return {
      chat: index('idx_chat').on(table.deletedAt, table.chatId, table.role),
      trace_id: index('idx_trace_id').on(table.traceId),
    };
  },
);

export const completionBlocks = pgTable(
  'completion_blocks',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    type: varchar('type', {
      length: 32,
      enum: getEnumValues(CompletionBlockTypeEnum),
    }).notNull(),
    status: varchar('status', {
      length: 32,
      enum: getEnumValues(CompletionBlockStatusEnum),
    }).notNull(),
    messageId: uuid('message_id'),
    data: text('data'), // LLM 原始输出
    toolId: varchar('tool_id', {
      length: 256,
    }),
    toolName: varchar('tool_name', {
      length: 256,
    }),
    toolArguments: jsonb('tool_arguments'),
    toolResult: jsonb('tool_result'), // Tool 执行后实际结果
    toolResponse: text('tool_response'), // 给 LLM 模型的返回结果
    toolGenerateElapsedMs: integer('tool_generate_elapsed_ms'),
    toolExecuteElapsedMs: integer('tool_execute_elapsed_ms'),
    extra: jsonb('extra'),
  },
  (table) => {
    return {
      message_id: index('idx_message_id').on(table.deletedAt, table.messageId),
    };
  },
);

export const thoughts = pgTable(
  'thoughts',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    space_id: uuid('space_id').notNull(),
    creator_id: uuid('creator_id').notNull(),
    title: varchar('title', { length: 255 }),
    title_type: varchar('title_type', {
      length: 32,
      enum: getEnumValues(TitleTypeEnum),
    })
      .notNull()
      .default(TitleTypeEnum.MANUAL),
    content_raw: text('content_raw').notNull(),
    content_plain: text('content_plain'),
    visibility: varchar('visibility', {
      length: 10,
      enum: getEnumValues(VisibilityEnum),
    })
      .notNull()
      .default(VisibilityEnum.PRIVATE),
  },
  (table) => {
    return {
      a: index('idx_thoughts_deleted_at_space_id').on(table.deleted_at, table.space_id),
    };
  },
);

export const snip_thought_relations = pgTable(
  'snip_thought_relations',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    snip_id: uuid('snip_id').notNull(),
    thought_id: uuid('thought_id').notNull(),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_snip_thought_relations_deleted_at_snip_id_thought_id')
        .on(t.snip_id, t.thought_id)
        .where(isNull(t.deleted_at)),
      b: index('idx_snip_thought_relations_deleted_at_thought_id_snip_id').on(
        t.deleted_at,
        t.thought_id,
        t.snip_id,
      ),
    };
  },
);

export const short_links = pgTable(
  'short_links',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    short_id: varchar('short_id', { length: 14 }).notNull(),
    entity_type: varchar('entity_type', {
      length: 20,
      enum: getEnumValues(EntityTypeEnum),
    }).notNull(),
    entity_id: uuid('entity_id').notNull(),
    active: boolean('active').notNull().default(true),
  },
  (t) => {
    return {
      a: uniqueIndex('short_links_short_id_idx').on(t.short_id),
      b: uniqueIndex('short_links_entity_idx').on(t.entity_type, t.entity_id),
    };
  },
);

export const assistants = pgTable('assistants', {
  id: uuid('id')
    .primaryKey()
    .$default(() => uuidv7()),
  created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  deleted_at: timestamp('deleted_at', { withTimezone: true }),
  creator_id: uuid('creator_id').notNull(),

  // 基本信息
  name: varchar('name', { length: 255 }).notNull(),
  description: varchar('description', { length: 2048 }).notNull(),
  instructions: text('instructions'),
  type: varchar('type', {
    length: 20,
    enum: getEnumValues(AssistantTypeEnum),
  })
    .notNull()
    .default(AssistantTypeEnum.PROMPT_EXECUTER),
  // 模型配置
  model: varchar('model', { length: 255 }),
  temperature: doublePrecision('temperature'),

  // Icon
  icon_type: varchar('icon_type', {
    length: 32,
    enum: getEnumValues(AssistantIconTypeEnum),
  }),
  icon_value: varchar('icon_value', { length: 32 }),
  icon_bg_color: varchar('icon_bg_color', { length: 32 }),

  // 可见性
  visibility: varchar('visibility', {
    length: 20,
    enum: getEnumValues(AssistantVisibilityEnum),
  }).notNull(),

  // 功能配置
  tools: jsonb('tools').$type<AssistantTool>().default(sql`'{}'::jsonb`),
  // 运行模式
  run_mode: varchar('run_mode', {
    length: 20,
    enum: getEnumValues(AssistantRunModeEnum),
  })
    .notNull()
    .default(AssistantRunModeEnum.MANUAL),
});

export const space_assistant_configs = pgTable(
  'space_assistant_configs',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),

    // 关联ID
    space_id: uuid('space_id').notNull(),
    assistant_id: uuid('assistant_id').notNull(),

    // 排序和状态
    rank: varchar('rank', { length: 20 }).notNull(),
    enabled: boolean('enabled').notNull().default(true),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_space_id_assistant_id')
        .on(t.space_id, t.assistant_id)
        .where(isNull(t.deleted_at)),
    };
  },
);

export const assistant_contents = pgTable(
  'assistant_contents',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),

    assistant_id: uuid('assistant_id').notNull(),
    entity_type: varchar('entity_type', { length: 20 }).notNull(),
    entity_id: uuid('entity_id').notNull(),

    content: text('content'),
    trace_id: varchar('trace_id', { length: 64 }),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_assistant_id_entity_type_entity_id')
        .on(t.assistant_id, t.entity_type, t.entity_id)
        .where(isNull(t.deleted_at)),
    };
  },
);

export const notes = pgTable(
  'notes',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    space_id: uuid('space_id').notNull(),
    creator_id: uuid('creator_id').notNull(),
    board_id: uuid('board_id').notNull(),
    entity_type: varchar('entity_type', {
      length: 20,
      enum: getEnumValues(NoteSourceEntityTypeEnum),
    }),
    entity_id: uuid('entity_id'),
    selection: text('selection'),
    quote_raw: text('quote_raw'),
    quote_plain: text('quote_plain'),
    content_raw: text('content_raw'),
    content_plain: text('content_plain'),
  },
  (t) => {
    return {
      a: index('idx_deleted_at_space_id_board_id_entity_type_entity_id').on(
        t.deleted_at,
        t.space_id,
        t.board_id,
        t.entity_type,
        t.entity_id,
      ),
    };
  },
);

export const favorites = pgTable(
  'favorites',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    space_id: uuid('space_id').notNull(),
    creator_id: uuid('creator_id').notNull(),
    entity_type: varchar('entity_type', {
      length: 20,
      enum: getEnumValues(FavoriteEntityTypeEnum),
    }).notNull(),
    entity_id: uuid('entity_id').notNull(),
    rank: varchar('rank', { length: 255 }).notNull(),
  },
  (t) => {
    return {
      a: uniqueIndex('idx_space_id_entity_type_entity_id')
        .on(t.space_id, t.entity_type, t.entity_id)
        .where(isNull(t.deleted_at)),
    };
  },
);

export const thought_versions = pgTable(
  'thought_versions',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    deleted_at: timestamp('deleted_at', { withTimezone: true }),
    thought_id: uuid('thought_id').notNull(),
    type: varchar('type', {
      length: 20,
      enum: getEnumValues(ThoughtVersionTypeEnum),
    }).notNull(),
    title: varchar('title', { length: 255 }).notNull(),
    description: varchar('description', { length: 2048 }),
    thought_title: varchar('thought_title', { length: 255 }).notNull(),
    content_raw: text('content_raw').notNull(),
    content_plain: text('content_plain'),
  },
  (t) => {
    return {
      a: index('idx_thought_id_type').on(t.thought_id, t.type).where(isNull(t.deleted_at)),
    };
  },
);

export const playlist_items = pgTable('playlist_items', {
  id: uuid('id')
    .primaryKey()
    .$default(() => uuidv7()),
  created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  deleted_at: timestamp('deleted_at', { withTimezone: true }),
  creator_id: uuid('creator_id').notNull(),
  space_id: uuid('space_id').notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  play_url: varchar('play_url', { length: 255 }).notNull(),
  duration: doublePrecision('duration'),
  status: varchar('status', {
    length: 20,
    enum: getEnumValues(PlaylistItemStatusEnum),
  }).notNull(),
  album_cover_url: varchar('album_cover_url', { length: 255 }),
  transcript: text('transcript'),
  rank: varchar('rank', { length: 255 }),
  playback_progress: doublePrecision('playback_progress'),
});

export const diff_review_events = pgTable(
  'diff_review_events',
  {
    id: uuid('id')
      .primaryKey()
      .$default(() => uuidv7()),
    created_at: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    thought_id: uuid('thought_id').notNull(),
    action: varchar('action', {
      length: 20,
      enum: getEnumValues(DiffReviewEventActionEnum),
    }).notNull(),
    nodes: jsonb('nodes').$type<DiffReviewEventNode[]>(),
    resolve_version_id: uuid('resolve_version_id'),
  },
  (t) => {
    return {
      a: index('idx_thought_id_created_at').on(t.thought_id, t.created_at),
    };
  },
);
