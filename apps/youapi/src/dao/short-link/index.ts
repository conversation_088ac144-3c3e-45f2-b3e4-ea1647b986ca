/**
 * Short Link DAO - 短链接数据访问对象
 * 管理短链接的CRUD操作
 *
 * Migrated from:
 * - youapp/src/lib/dao/short-link/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';

import type { EntityTypeEnum } from '../../common/types';
import { DatabaseService } from '../db/database.service';
import { short_links } from '../db/public.schema';
import type { InsertShortLinkDOParam, ShortLinkDO, UpdateShortLinkDOParam } from './types';

@Injectable()
export class ShortLinkDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  async insert(param: InsertShortLinkDOParam): Promise<ShortLinkDO> {
    const [result] = await this.db.insert(short_links).values(param).returning();
    return result;
  }

  async tryGetByShortId(shortId: string): Promise<ShortLinkDO | undefined> {
    const result = await this.db
      .select()
      .from(short_links)
      .where(eq(short_links.short_id, shortId));
    return result.length > 0 ? result[0] : undefined;
  }

  async tryGetByEntity(
    entityType: EntityTypeEnum,
    entityId: string,
  ): Promise<ShortLinkDO | undefined> {
    const result = await this.db
      .select()
      .from(short_links)
      .where(and(eq(short_links.entity_type, entityType), eq(short_links.entity_id, entityId)));
    return result.length > 0 ? result[0] : undefined;
  }

  async delete(id: string): Promise<void> {
    await this.db.delete(short_links).where(eq(short_links.id, id));
  }

  async update(id: string, updateParam: UpdateShortLinkDOParam): Promise<ShortLinkDO> {
    const result = await this.db
      .update(short_links)
      .set({
        ...updateParam,
        updated_at: new Date(),
      } as any)
      .where(eq(short_links.id, id))
      .returning();
    return result[0];
  }
}
