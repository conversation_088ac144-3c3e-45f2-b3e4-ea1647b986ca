/**
 * System Config DAO Types
 * 系统配置数据访问对象类型定义
 *
 * Migrated from:
 * - youapp/src/lib/dao/system-config/types.ts
 */

import type { PgUpdateSetSource } from 'drizzle-orm/pg-core';

import type { system_configs } from '../db/public.schema';

export type SystemConfigDO = typeof system_configs.$inferSelect;

export type InsertSystemConfigDOParam = typeof system_configs.$inferInsert;

export type UpdateSystemConfigDOParam = Pick<
  PgUpdateSetSource<typeof system_configs>,
  'value' | 'modifier_id'
>;
