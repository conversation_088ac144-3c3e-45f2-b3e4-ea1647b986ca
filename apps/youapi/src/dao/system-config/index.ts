/**
 * System Config DAO - 系统配置数据访问对象
 * 管理系统配置的CRUD操作
 *
 * Migrated from:
 * - youapp/src/lib/dao/system-config/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, eq, isNull } from 'drizzle-orm';

import { DatabaseService } from '../db/database.service';
import { system_configs } from '../db/public.schema';
import type { InsertSystemConfigDOParam, SystemConfigDO, UpdateSystemConfigDOParam } from './types';

@Injectable()
export class SystemConfigDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  async select(): Promise<SystemConfigDO[]> {
    return this.db
      .select()
      .from(system_configs)
      .where(isNull(system_configs.deleted_at))
      .orderBy(system_configs.id);
  }

  async trySelectByKey(key: string): Promise<SystemConfigDO | null> {
    const result = await this.db
      .select()
      .from(system_configs)
      .where(and(isNull(system_configs.deleted_at), eq(system_configs.key, key)));
    if (result.length === 0) {
      return null;
    }
    return result[0];
  }

  async selectByKey(key: string): Promise<SystemConfigDO> {
    const result = await this.trySelectByKey(key);
    if (!result) {
      throw new Error(`SystemConfig not found for key: ${key}`);
    }
    return result;
  }

  async insert(insertParam: InsertSystemConfigDOParam): Promise<SystemConfigDO> {
    const result = await this.db.insert(system_configs).values(insertParam).returning();
    return result[0];
  }

  async update(key: string, updateParam: UpdateSystemConfigDOParam): Promise<void> {
    await this.db
      .update(system_configs)
      .set(updateParam)
      .where(and(isNull(system_configs.deleted_at), eq(system_configs.key, key)));
  }
}
