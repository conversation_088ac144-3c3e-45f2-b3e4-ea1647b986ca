/**
 * Board Group DAO - 看板分组数据访问对象
 * 看板分组的数据库操作封装，包括增删改查等基础操作
 *
 * Migrated from:
 * - /lib/dao/board-group/index.ts (youapp)
 * - /lib/domain/board-group/index.ts (youapp)
 */

import { Injectable } from '@nestjs/common';
import { and, eq, isNull } from 'drizzle-orm';
import { BoardGroupTypeEnum } from '../../common/types/board.types';
import { DatabaseService } from '../db/database.service';
import { board_groups } from '../db/public.schema';
import type {
  BoardGroupDO,
  BoardGroupInsertDO,
  BoardGroupUpdateDO,
  BoardGroupVO,
  DeleteBoardGroupParams,
  SelectBoardGroupByIdParams,
  SelectBoardGroupsByBoardIdParams,
  SelectBoardGroupsByCreatorIdParams,
} from './types';

@Injectable()
export class BoardGroupDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * 插入新的看板分组
   * 创建新的看板分组记录
   */
  async insert(data: BoardGroupInsertDO): Promise<BoardGroupDO> {
    const insertData = {
      ...data,
      type: data.type || BoardGroupTypeEnum.NORMAL,
      created_at: new Date(),
      updated_at: new Date(),
    };

    const result = await this.databaseService.db
      .insert(board_groups)
      .values(insertData)
      .returning();

    return result[0] as BoardGroupDO;
  }

  /**
   * 根据ID查询看板分组
   * 获取指定ID的看板分组详情
   */
  async selectById(params: SelectBoardGroupByIdParams): Promise<BoardGroupDO | undefined> {
    const result = await this.databaseService.db
      .select()
      .from(board_groups)
      .where(and(eq(board_groups.id, params.id), isNull(board_groups.deleted_at)))
      .limit(1);

    return result[0] as BoardGroupDO;
  }

  /**
   * 根据ID查询看板分组（必须存在）
   * 如果不存在则抛出异常
   */
  async selectOneById(params: SelectBoardGroupByIdParams): Promise<BoardGroupDO> {
    const result = await this.selectById(params);
    if (!result) {
      throw new Error(`Board group not found: ${params.id}`);
    }
    return result;
  }

  /**
   * 根据看板ID查询所有分组
   * 获取指定看板下的所有分组
   */
  async selectByBoardId(params: SelectBoardGroupsByBoardIdParams): Promise<BoardGroupDO[]> {
    const result = await this.databaseService.db
      .select()
      .from(board_groups)
      .where(and(eq(board_groups.board_id, params.board_id), isNull(board_groups.deleted_at)))
      .orderBy(board_groups.created_at);

    return result as BoardGroupDO[];
  }

  /**
   * 根据创建者ID查询看板分组
   * 获取指定用户创建的所有看板分组
   */
  async selectByCreatorId(params: SelectBoardGroupsByCreatorIdParams): Promise<BoardGroupDO[]> {
    const result = await this.databaseService.db
      .select()
      .from(board_groups)
      .where(and(eq(board_groups.creator_id, params.creator_id), isNull(board_groups.deleted_at)))
      .orderBy(board_groups.created_at);

    return result as BoardGroupDO[];
  }

  /**
   * 更新看板分组信息
   * 更新指定看板分组的属性
   */
  async update(data: BoardGroupUpdateDO): Promise<BoardGroupDO> {
    const updateData = {
      ...data,
      updated_at: new Date(),
    };

    // Remove undefined values
    Object.keys(updateData).forEach((key) => {
      if (updateData[key as keyof BoardGroupUpdateDO] === undefined) {
        delete updateData[key as keyof BoardGroupUpdateDO];
      }
    });

    const result = await this.databaseService.db
      .update(board_groups)
      .set(updateData as any)
      .where(and(eq(board_groups.id, data.id), isNull(board_groups.deleted_at)))
      .returning();

    if (result.length === 0) {
      throw new Error(`Board group not found or already deleted: ${data.id}`);
    }

    return result[0] as BoardGroupDO;
  }

  /**
   * 软删除看板分组
   * 标记看板分组为已删除状态
   */
  async delete(params: DeleteBoardGroupParams): Promise<void> {
    const result = await this.databaseService.db
      .update(board_groups)
      .set({
        deleted_at: new Date(),
        updated_at: new Date(),
      } as any)
      .where(and(eq(board_groups.id, params.id), isNull(board_groups.deleted_at)))
      .returning();

    if (result.length === 0) {
      throw new Error(`Board group not found or already deleted: ${params.id}`);
    }
  }

  /**
   * 转换为VO对象
   * 将数据库对象转换为业务层对象
   */
  convertToVO(boardGroup: BoardGroupDO): BoardGroupVO {
    return {
      id: boardGroup.id,
      created_at: boardGroup.created_at,
      updated_at: boardGroup.updated_at,
      creator_id: boardGroup.creator_id,
      board_id: boardGroup.board_id,
      name: boardGroup.name,
      icon_name: boardGroup.icon_name,
      icon_color: boardGroup.icon_color,
      type: boardGroup.type,
    };
  }

  /**
   * 批量转换为VO对象
   * 将数据库对象数组转换为业务层对象数组
   */
  convertToVOs(boardGroups: BoardGroupDO[]): BoardGroupVO[] {
    return boardGroups.map((boardGroup) => this.convertToVO(boardGroup));
  }
}
