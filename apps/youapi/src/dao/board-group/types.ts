/**
 * Board Group DAO Types
 * 看板分组数据访问对象类型定义
 *
 * Migrated from:
 * - /lib/dao/board-group (youapp)
 * - /lib/domain/board-group (youapp)
 */

import { z } from 'zod';

import { BoardGroupTypeEnum } from '../../common/types/board.types';

// Database Object (DO) - 数据库实体类型
export interface BoardGroupDO {
  id: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  creator_id: string;
  board_id: string;
  name: string;
  icon_name: string | null;
  icon_color: string | null;
  type: BoardGroupTypeEnum;
}

// Insert Object - 插入数据类型
export interface BoardGroupInsertDO {
  id?: string;
  creator_id: string;
  board_id: string;
  name: string;
  icon_name?: string | null;
  icon_color?: string | null;
  type?: BoardGroupTypeEnum;
}

// Update Object - 更新数据类型
export interface BoardGroupUpdateDO {
  id: string;
  name?: string;
  icon_name?: string | null;
  icon_color?: string | null;
  type?: BoardGroupTypeEnum;
  updated_at?: Date;
}

// Value Object (VO) - 业务层对象类型
export interface BoardGroupVO {
  id: string;
  created_at: Date;
  updated_at: Date;
  creator_id: string;
  board_id: string;
  name: string;
  icon_name: string | null;
  icon_color: string | null;
  type: BoardGroupTypeEnum;
}

// DAO Method Parameters
export interface SelectBoardGroupByIdParams {
  id: string;
}

export interface SelectBoardGroupsByBoardIdParams {
  board_id: string;
}

export interface SelectBoardGroupsByCreatorIdParams {
  creator_id: string;
}

export interface DeleteBoardGroupParams {
  id: string;
}

// Validation Schemas
export const BoardGroupVOSchema = z.object({
  id: z.string().uuid(),
  created_at: z.date(),
  updated_at: z.date(),
  creator_id: z.string().uuid(),
  board_id: z.string().uuid(),
  name: z.string().min(1).max(255),
  icon_name: z.string().max(255).nullable(),
  icon_color: z.string().max(255).nullable(),
  type: z.nativeEnum(BoardGroupTypeEnum),
});

export const BoardGroupInsertDOSchema = z.object({
  id: z.string().uuid().optional(),
  creator_id: z.string().uuid(),
  board_id: z.string().uuid(),
  name: z.string().min(1).max(255),
  icon_name: z.string().max(255).nullable().optional(),
  icon_color: z.string().max(255).nullable().optional(),
  type: z.nativeEnum(BoardGroupTypeEnum).optional(),
});

export const BoardGroupUpdateDOSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255).optional(),
  icon_name: z.string().max(255).nullable().optional(),
  icon_color: z.string().max(255).nullable().optional(),
  type: z.nativeEnum(BoardGroupTypeEnum).optional(),
  updated_at: z.date().optional(),
});
