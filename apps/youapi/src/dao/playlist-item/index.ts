/**
 * Playlist Item DAO - 播放列表项目数据访问对象
 * 播放列表项目的数据库操作封装，包括增删改查等基础操作
 *
 * Migrated from Next.js to NestJS
 */

import { Injectable } from '@nestjs/common';
import { eq, inArray } from 'drizzle-orm';

import { DatabaseService } from '../db/database.service';
import { playlist_items } from '../db/public.schema';
import type {
  PlaylistItemDO,
  PlaylistItemInsertDO,
  PlaylistItemUpdateDO,
  SelectPlaylistItemParams,
} from './types';

@Injectable()
export class PlaylistItemDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  async insert(data: PlaylistItemInsertDO): Promise<PlaylistItemDO> {
    const [result] = await this.databaseService.db.insert(playlist_items).values(data).returning();
    return result;
  }

  async update(id: string, data: PlaylistItemUpdateDO): Promise<PlaylistItemDO> {
    const [result] = await this.databaseService.db
      .update(playlist_items)
      .set(data)
      .where(eq(playlist_items.id, id))
      .returning();
    return result;
  }

  async delete(id: string): Promise<void> {
    await this.databaseService.db.delete(playlist_items).where(eq(playlist_items.id, id));
  }

  async deleteMany(ids: string[]): Promise<void> {
    await this.databaseService.db.delete(playlist_items).where(inArray(playlist_items.id, ids));
  }

  async selectById(id: string): Promise<PlaylistItemDO> {
    const [result] = await this.databaseService.db
      .select()
      .from(playlist_items)
      .where(eq(playlist_items.id, id))
      .limit(1);
    return result;
  }

  async select(params: SelectPlaylistItemParams): Promise<PlaylistItemDO[]> {
    const results = await this.databaseService.db
      .select()
      .from(playlist_items)
      .where(eq(playlist_items.space_id, params.space_id))
      .orderBy(playlist_items.rank);
    return results;
  }
}
