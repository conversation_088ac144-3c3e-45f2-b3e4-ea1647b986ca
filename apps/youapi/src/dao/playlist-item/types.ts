/**
 * Playlist Item DAO Types
 * 播放列表项目数据访问对象类型定义
 *
 * Migrated from Next.js to NestJS
 */

import type { PgUpdateSetSource } from 'drizzle-orm/pg-core';
import { z } from 'zod';
import { PlaylistItemStatusEnum } from '../../common/types';
import type { playlist_items } from '../db/public.schema';

// Database Object (DO) - 数据库实体类型
export type PlaylistItemDO = typeof playlist_items.$inferSelect;
export type PlaylistItemInsertDO = typeof playlist_items.$inferInsert;
export type PlaylistItemUpdateDO = Omit<
  PgUpdateSetSource<typeof playlist_items>,
  'id' | 'created_at' | 'deleted_at'
>;

// Value Object (VO) - 业务层对象类型
export interface PlaylistItemVO {
  id: string;
  created_at: Date;
  updated_at: Date;
  creator_id: string;
  space_id: string;
  board_id?: string;
  entity_type?: string;
  entity_id?: string;
  title: string;
  play_url: string;
  duration: number;
  status: PlaylistItemStatusEnum;
  album_cover_url?: string;
  transcript?: string;
  rank?: string;
  playback_progress?: number;
}

// DAO Method Parameters
export interface SelectPlaylistItemParams {
  space_id: string;
  board_id: string;
}

// Validation Schemas
export const PlaylistItemVOSchema = z.object({
  id: z.string().uuid(),
  created_at: z.date(),
  updated_at: z.date(),
  creator_id: z.string().uuid(),
  space_id: z.string().uuid(),
  board_id: z.string().uuid().optional(),
  entity_type: z.string().optional(),
  entity_id: z.string().uuid().optional(),
  title: z.string(),
  play_url: z.string(),
  duration: z.number(),
  status: z.nativeEnum(PlaylistItemStatusEnum),
  album_cover_url: z.string().optional(),
  transcript: z.string().optional(),
  rank: z.string().optional(),
  playback_progress: z.number().optional(),
});
