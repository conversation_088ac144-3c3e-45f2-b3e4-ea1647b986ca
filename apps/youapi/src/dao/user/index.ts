/**
 * User DAO - Handles user data access operations
 * 用户数据访问对象 - 处理用户相关的数据库操作
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/dao/user/index.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/dao/user-preference/index.ts
 */

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient } from '@supabase/supabase-js';
import { and, count, desc, eq, isNull, type SQL, sql } from 'drizzle-orm';
import { isNumber, max, min } from 'lodash';

import type { SystemConfig } from '@/common/types';
import { users } from '../db/auth.schema';
import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { spaces, user_preferences } from '../db/public.schema';
import type { UpdateAppMetaDataParam, UserDO, UserPreferenceDO } from './types';

@Injectable()
export class UserDAO extends BaseDAO<typeof users._.config, UserDO> {
  private supabaseClient;

  constructor(
    database: DatabaseService,
    private readonly configService: ConfigService<SystemConfig>,
  ) {
    super(database, users);

    // Initialize Supabase admin client for user metadata updates
    this.supabaseClient = createClient(
      this.configService.getOrThrow('NEXT_PUBLIC_SUPABASE_URL'),
      this.configService.getOrThrow('SUPABASE_SERVICE_KEY'),
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      },
    );
  }

  async selectOneById(id: string): Promise<UserDO | null> {
    return this.selectOneByQuery(eq(users.id, id));
  }

  async selectOneByEmail(email: string): Promise<UserDO | null> {
    return this.selectOneByQuery(eq(users.email, email));
  }

  async updateAppMetadata(param: UpdateAppMetaDataParam): Promise<void> {
    // 通过 Supabase Admin API 更新用户的 app_metadata
    const { error } = await this.supabaseClient.auth.admin.updateUserById(param.id, {
      app_metadata: param.raw_app_meta_data as Record<string, unknown>,
    });

    if (error) {
      throw new Error(`Failed to update user app metadata: ${error.message}`);
    }
  }

  async list(
    param: { current?: number; pageSize?: number },
    queryParam: {
      keyword?: string;
      onboard_status?: string;
    },
  ): Promise<{
    paging: { current: number; pageSize: number; total: number };
    data: UserDO[];
  }> {
    const terms: SQL[] = [];
    const { keyword, onboard_status } = queryParam;

    if (keyword) {
      const searchPattern = `%${keyword}%`;
      terms.push(
        sql`(
          raw_user_meta_data->>'name' ILIKE ${searchPattern} OR
          raw_user_meta_data->>'email' ILIKE ${searchPattern} OR
          id::text ILIKE ${searchPattern}
        )`,
      );
    }

    if (onboard_status) {
      terms.push(eq(sql`raw_app_meta_data->>'onboard_status'`, onboard_status));
    }

    const query = terms.length ? (and(...terms) as SQL) : sql`1=1`;

    return this._list(query, param, [
      desc(sql`CASE raw_app_meta_data->>'onboard_status'
        WHEN 'waiting' THEN 3
        WHEN 'onboarded' THEN 2
        ELSE 1 END`),
      desc(users.created_at),
    ]);
  }

  async selectWithSpace(
    param: { current?: number; pageSize?: number },
    queryParam: {
      keyword?: string;
      onboard_status?: string;
      status?: string | string[];
      subscription_status?: string | string[];
      profile_completeness?: string;
      willingness?: string;
      has_message?: string;
      time_zone_category?: string | string[];
    },
  ) {
    const pageSize = (
      isNumber(param.pageSize) ? min([max([param.pageSize, 1]), 100]) : 10
    ) as number;
    const current = (isNumber(param.current) ? max([param.current, 0]) : 0) as number;

    const conditions = [isNull(users.deleted_at)];
    const {
      keyword,
      onboard_status,
      status,
      subscription_status,
      profile_completeness,
      willingness,
      has_message,
      time_zone_category,
    } = queryParam;

    // 构建查询条件 - 从 youapp 迁移的复杂查询逻辑
    if (keyword) {
      conditions.push(
        sql`(raw_user_meta_data->>'name' ILIKE ${`%${keyword}%`} OR raw_user_meta_data->>'email' ILIKE ${`%${keyword}%`})`,
      );
    }

    if (onboard_status) {
      conditions.push(
        eq(sql`COALESCE(raw_app_meta_data->>'onboard_status', 'unknown')`, onboard_status),
      );
    }

    if (status) {
      if (Array.isArray(status)) {
        if (status.length > 0) {
          const quotedValues = status.map((val) => `'${val}'`).join(',');
          conditions.push(sql`spaces.status IN (${sql.raw(quotedValues)})`);
        }
      } else {
        conditions.push(eq(spaces.status, status));
      }
    }

    if (subscription_status) {
      if (Array.isArray(subscription_status)) {
        if (subscription_status.length > 0) {
          const quotedValues = subscription_status.map((val) => `'${val}'`).join(',');
          conditions.push(sql`spaces.subscription_status IN (${sql.raw(quotedValues)})`);
        }
      } else {
        conditions.push(eq(spaces.subscription_status, subscription_status));
      }
    }

    if (profile_completeness != null && profile_completeness !== '') {
      conditions.push(sql`(
        CASE WHEN COALESCE(raw_app_meta_data#>>'{profile, tools}', '') != '' THEN 1 ELSE 0 END +
        CASE WHEN COALESCE(raw_app_meta_data#>>'{profile, content}', '') != '' THEN 1 ELSE 0 END +
        CASE WHEN COALESCE(raw_app_meta_data#>>'{profile, purpose}', '') != '' THEN 1 ELSE 0 END +
        CASE WHEN COALESCE(raw_app_meta_data#>>'{profile, challenge}', '') != '' THEN 1 ELSE 0 END +
        CASE WHEN COALESCE(raw_app_meta_data#>>'{profile, valuation}', '') != '' THEN 1 ELSE 0 END
      )::text = ${profile_completeness}`);
    }

    if (willingness != null && willingness !== '') {
      if (willingness === '1') {
        conditions.push(
          sql`coalesce(raw_app_meta_data#>>'{profile, valuation}', 'None, I only use the free version.') != 'None, I only use the free version.'`,
        );
      } else if (willingness === '0') {
        conditions.push(
          sql`coalesce(raw_app_meta_data#>>'{profile, valuation}', '') = 'None, I only use the free version.'`,
        );
      } else {
        conditions.push(sql`coalesce(raw_app_meta_data#>>'{profile, valuation}', '') = ''`);
      }
    }

    if (has_message != null && has_message !== '') {
      if (has_message === '1') {
        conditions.push(sql`coalesce(raw_app_meta_data#>>'{profile, challenge}', '') != ''`);
      } else if (has_message === '0') {
        conditions.push(sql`coalesce(raw_app_meta_data#>>'{profile, challenge}', '') = ''`);
      }
    }

    if (time_zone_category) {
      if (Array.isArray(time_zone_category)) {
        if (time_zone_category.includes('Mainland') && time_zone_category.includes('Overseas')) {
          // Both selected - no need to filter
        } else if (time_zone_category.includes('Mainland')) {
          conditions.push(
            sql`raw_app_meta_data->>'time_zone' IN ('Asia/Shanghai', 'Asia/Urumqi', 'Etc/GMT-8')`,
          );
        } else if (time_zone_category.includes('Overseas')) {
          conditions.push(
            sql`(raw_app_meta_data->>'time_zone' IS NOT NULL AND raw_app_meta_data->>'time_zone' NOT IN ('Asia/Shanghai', 'Asia/Urumqi', 'Etc/GMT-8'))`,
          );
        }
      } else {
        if (time_zone_category === 'Mainland') {
          conditions.push(
            sql`raw_app_meta_data->>'time_zone' IN ('Asia/Shanghai', 'Asia/Urumqi', 'Etc/GMT-8')`,
          );
        } else if (time_zone_category === 'Overseas') {
          conditions.push(
            sql`(raw_app_meta_data->>'time_zone' IS NOT NULL AND raw_app_meta_data->>'time_zone' NOT IN ('Asia/Shanghai', 'Asia/Urumqi', 'Etc/GMT-8'))`,
          );
        }
      }
    }

    const [total, result] = await Promise.all([
      this.db
        .select({ count: count() })
        .from(users)
        .leftJoin(spaces, eq(users.id, spaces.creator_id))
        .where(and(...conditions)),
      this.db
        .select()
        .from(users)
        .leftJoin(spaces, eq(users.id, spaces.creator_id))
        .where(and(...conditions))
        .limit(pageSize)
        .offset(current * pageSize)
        .orderBy(desc(users.created_at)),
    ]);

    return {
      paging: {
        current,
        pageSize,
        total: total[0].count,
      },
      data: result,
    };
  }

  async countAll(): Promise<number> {
    const result = await this.db
      .select({ count: count() })
      .from(users)
      .where(isNull(users.deleted_at));

    return result[0].count;
  }

  async countBySubscriptionStatus(): Promise<
    Array<{
      count: number;
      subscription_status: string | null;
      status: string | null;
      subscription_price: string | null;
    }>
  > {
    const result = await this.db
      .select({
        count: count(),
        subscription_status: spaces.subscription_status,
        status: spaces.status,
        subscription_price: spaces.subscription_price,
      })
      .from(users)
      .leftJoin(spaces, eq(users.id, spaces.creator_id))
      .where(and(isNull(users.deleted_at)))
      .groupBy(spaces.subscription_status, spaces.status, spaces.subscription_price);

    return result;
  }
}

@Injectable()
export class UserPreferenceDAO extends BaseDAO<typeof user_preferences._.config, UserPreferenceDO> {
  constructor(database: DatabaseService) {
    super(database, user_preferences);
  }

  async selectOneByUserId(userId: string): Promise<UserPreferenceDO | null> {
    return this.selectOneByQuery(eq(user_preferences.id, userId));
  }

  async upsertByUserId(
    userId: string,
    preferences: Partial<UserPreferenceDO>,
  ): Promise<UserPreferenceDO> {
    // 尝试更新，如果不存在则插入
    const existing = await this.selectOneByUserId(userId);

    if (existing) {
      return this.updateOne(existing.id, preferences);
    } else {
      // 创建新的用户偏好设置，使用默认值填充必需字段
      const insertData = {
        id: userId,
        display_language: preferences.display_language || 'en-US',
        ai_response_language: preferences.ai_response_language || 'system',
        ai_2nd_response_language: preferences.ai_2nd_response_language || 'system',
        enable_bilingual: preferences.enable_bilingual ?? true,
        detected_language: preferences.detected_language,
      } as typeof user_preferences.$inferInsert;
      return this.insertOne(insertData);
    }
  }

  convertToVO(dbObject: UserPreferenceDO): unknown {
    // 将数据库对象转换为值对象，处理枚举类型和日期格式
    return {
      id: dbObject.id,
      display_language: dbObject.display_language,
      ai_response_language: dbObject.ai_response_language,
      ai_2nd_response_language: dbObject.ai_2nd_response_language,
      enable_bilingual: dbObject.enable_bilingual,
      detected_language: dbObject.detected_language || undefined,
      created_at: dbObject.created_at.toISOString(),
      updated_at: dbObject.updated_at.toISOString(),
    };
  }
}
