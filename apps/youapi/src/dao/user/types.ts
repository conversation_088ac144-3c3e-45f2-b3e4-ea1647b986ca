/**
 * User DAO Types
 *
 * Data Objects (DO) for user-related database operations
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/dao/user/types.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/dao/user-preference/types.ts
 */

import type { UserOnboardStatusEnum } from '../../common/types';
import type { users } from '../db/auth.schema';
import type { user_preferences } from '../db/public.schema';

export type UserDO = typeof users.$inferSelect & {
  raw_app_meta_data?: UserAppMetadata;
  raw_user_meta_data?: {
    name?: string;
    picture?: string;
    [key: string]: unknown;
  };
};
export type UserPreferenceDO = typeof user_preferences.$inferSelect;

export interface UserAppMetadata {
  name?: string;
  onboard_status?: UserOnboardStatusEnum;
  time_zone?: string;
  avatar_url?: string;
  profile?: {
    skip?: string;
    challenge?: string;
    content?: string[];
    tools?: string[];
    valuation?: string;
    purpose?: string[];
  };
}

export interface UpdateAppMetaDataParam {
  id: string;
  raw_app_meta_data: UserAppMetadata;
}

export interface UserQueryParam {
  limit?: number;
  offset?: number;
  search?: string;
  subscription_status?: string;
}

export interface PatchUserNameParam {
  id: string;
  name: string;
}

export interface PatchUserTimeZoneParam {
  id: string;
  time_zone: string;
}

export interface PatchUserPreferenceParam {
  id: string;
  display_language?: string;
  ai_response_language?: string;
  ai_2nd_response_language?: string;
  enable_bilingual?: boolean;
  detected_language?: string;
}
