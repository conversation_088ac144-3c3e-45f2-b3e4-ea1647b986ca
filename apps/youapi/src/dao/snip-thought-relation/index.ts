/**
 * Snip Thought Relation DAO - 片段思考关系数据访问对象
 * 管理片段和思考之间的关系
 *
 * Migrated from:
 * - youapp/src/lib/dao/snip-thought-relation/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, eq, inArray, isNull } from 'drizzle-orm';

import { DatabaseService } from '../db/database.service';
import { snip_thought_relations } from '../db/public.schema';
import type {
  InsertSnipThoughtRelationDOParam,
  SnipThoughtRelationDO,
  UpdateSnipThoughtRelationDOParam,
} from './types';

@Injectable()
export class SnipThoughtRelationDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  async insert(param: InsertSnipThoughtRelationDOParam): Promise<SnipThoughtRelationDO> {
    const result = await this.db.insert(snip_thought_relations).values(param).returning();
    return result[0];
  }

  async update(
    id: string,
    param: UpdateSnipThoughtRelationDOParam,
  ): Promise<SnipThoughtRelationDO> {
    const result = await this.db
      .update(snip_thought_relations)
      .set({
        ...param,
        updated_at: new Date(),
      } as any)
      .where(and(eq(snip_thought_relations.id, id), isNull(snip_thought_relations.deleted_at)))
      .returning();
    return result[0];
  }

  async delete(id: string): Promise<void> {
    await this.db
      .update(snip_thought_relations)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(and(eq(snip_thought_relations.id, id), isNull(snip_thought_relations.deleted_at)));
  }

  async deleteByThoughtId(thoughtId: string): Promise<void> {
    await this.db
      .update(snip_thought_relations)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(
        and(
          eq(snip_thought_relations.thought_id, thoughtId),
          isNull(snip_thought_relations.deleted_at),
        ),
      );
  }

  async deleteBySnipId(snipId: string): Promise<void> {
    await this.db
      .update(snip_thought_relations)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(
        and(eq(snip_thought_relations.snip_id, snipId), isNull(snip_thought_relations.deleted_at)),
      );
  }

  async selectByThoughtId(thoughtId: string): Promise<SnipThoughtRelationDO[]> {
    return await this.db
      .select()
      .from(snip_thought_relations)
      .where(
        and(
          eq(snip_thought_relations.thought_id, thoughtId),
          isNull(snip_thought_relations.deleted_at),
        ),
      );
  }

  async selectBySnipId(snipId: string): Promise<SnipThoughtRelationDO[]> {
    return await this.db
      .select()
      .from(snip_thought_relations)
      .where(
        and(eq(snip_thought_relations.snip_id, snipId), isNull(snip_thought_relations.deleted_at)),
      );
  }

  async selectByIds(ids: string[]): Promise<SnipThoughtRelationDO[]> {
    return await this.db
      .select()
      .from(snip_thought_relations)
      .where(
        and(inArray(snip_thought_relations.id, ids), isNull(snip_thought_relations.deleted_at)),
      );
  }

  async trySelectById(id: string): Promise<SnipThoughtRelationDO | null> {
    const result = await this.db
      .select()
      .from(snip_thought_relations)
      .where(and(eq(snip_thought_relations.id, id), isNull(snip_thought_relations.deleted_at)))
      .limit(1);
    return result.length > 0 ? result[0] : null;
  }
}
