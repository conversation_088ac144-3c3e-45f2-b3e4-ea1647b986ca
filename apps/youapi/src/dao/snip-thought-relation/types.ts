/**
 * Snip Thought Relation DAO Types
 * 片段思考关系数据访问对象类型定义
 *
 * Migrated from:
 * - youapp/src/lib/dao/snip-thought-relation/types.ts
 */

import type { snip_thought_relations } from '../db/public.schema';

// Database Object (DO) - 数据库实体类型
export type SnipThoughtRelationDO = typeof snip_thought_relations.$inferSelect;

// Insert parameter type
export type InsertSnipThoughtRelationDOParam = typeof snip_thought_relations.$inferInsert;

// Update parameter type
export type UpdateSnipThoughtRelationDOParam = Partial<
  Omit<InsertSnipThoughtRelationDOParam, 'id' | 'created_at'>
>;
