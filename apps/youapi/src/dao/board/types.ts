/**
 * Board DAO Types - Board 数据访问对象类型定义
 * 定义Board相关的数据库对象类型
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/dao/board/types.ts
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/domain/board/types.ts
 */

import type { BoardStatusEnum, BoardTypeEnum } from '../../common/types';
import type { boards } from '../db/public.schema';

/**
 * Board Data Object - 数据库表结构对应的数据对象
 */
export type BoardDO = typeof boards.$inferSelect;

/**
 * Board Insert Object - 插入数据库时使用的对象
 */
export type BoardInsertDO = typeof boards.$inferInsert;

/**
 * Board Update Object - 更新数据库时使用的对象
 */
export type BoardUpdateDO = Partial<BoardInsertDO>;

/**
 * Board Value Object - 业务层使用的值对象
 */
export interface BoardVO {
  id: string;
  name: string;
  description: string;
  icon: {
    name: string;
    color: string;
  };
  pinned_at?: Date;
  created_at: Date;
  updated_at: Date;
  status: BoardStatusEnum;
  hero_image_urls?: string[];
  intro?: string;
  type: BoardTypeEnum;
  space_id: string;
  creator_id: string;
  rank: string;
  filter?: unknown;
}

/**
 * Create Board Parameters - 创建Board的参数
 */
export interface CreateBoardParam {
  name: string;
  description: string;
  icon: {
    name: string;
    color: string;
  };
  space_id: string;
  creator_id: string;
  type?: BoardTypeEnum;
  rank?: string;
}

/**
 * Update Board Parameters - 更新Board的参数
 */
export interface UpdateBoardParam {
  id: string;
  name?: string;
  description?: string;
  icon_name?: string;
  icon_color?: string;
  status?: BoardStatusEnum;
  pinned_at?: Date | null;
  hero_image_urls?: string[];
  intro?: string;
  rank?: string;
}

/**
 * List Boards Parameters - 查询Board列表的参数
 */
export interface ListBoardsParam {
  space_id: string;
  fuzzy_name?: string;
  status?: BoardStatusEnum;
  limit?: number;
  offset?: number;
}

/**
 * Board Count Statistics - Board统计信息
 */
export interface BoardCountStats {
  board_id: string;
  snips_count: number;
  thoughts_count: number;
}
