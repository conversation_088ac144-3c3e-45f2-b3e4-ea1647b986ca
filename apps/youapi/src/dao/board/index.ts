/**
 * Board DAO - Board 数据访问对象
 * 处理Board相关的数据库操作
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/dao/board/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, asc, desc, eq, ilike, inArray, isNull, or, sql } from 'drizzle-orm';

import { BoardStatusEnum, BoardTypeEnum } from '../../common/types';
import { DatabaseService } from '../db/database.service';
import { board_items, boards } from '../db/public.schema';
import type {
  BoardDO,
  BoardVO,
  CreateBoardParam,
  ListBoardsParam,
  UpdateBoardParam,
} from './types';

@Injectable()
export class BoardDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  /**
   * 创建新的Board
   */
  async insert(param: CreateBoardParam): Promise<BoardDO> {
    const insertData = {
      name: param.name,
      description: param.description,
      icon_name: param.icon.name,
      icon_color: param.icon.color,
      space_id: param.space_id,
      creator_id: param.creator_id,
      rank: param.rank || '~',
      status: BoardStatusEnum.IN_PROGRESS,
      type: param.type || BoardTypeEnum.NORMAL,
    };

    const result = await this.db.insert(boards).values(insertData).returning();

    return result[0] as BoardDO;
  }

  /**
   * 根据ID获取Board
   */
  async selectById(id: string): Promise<BoardDO> {
    const result = await this.db
      .select()
      .from(boards)
      .where(and(eq(boards.id, id), isNull(boards.deleted_at)))
      .limit(1);

    if (result.length === 0) {
      throw new Error(`Board not found: ${id}`);
    }

    return result[0] as BoardDO;
  }

  /**
   * 尝试根据ID获取Board（可能不存在）
   */
  async trySelectById(id: string): Promise<BoardDO | null> {
    const result = await this.db
      .select()
      .from(boards)
      .where(and(eq(boards.id, id), isNull(boards.deleted_at)))
      .limit(1);

    return result.length > 0 ? (result[0] as BoardDO) : null;
  }

  /**
   * 获取默认Board
   */
  async selectDefault(spaceId: string): Promise<BoardDO> {
    const result = await this.db
      .select()
      .from(boards)
      .where(
        and(
          eq(boards.space_id, spaceId),
          eq(boards.type, BoardTypeEnum.DEFAULT),
          isNull(boards.deleted_at),
        ),
      )
      .limit(1);

    if (result.length === 0) {
      throw new Error(`Default board not found for space: ${spaceId}`);
    }

    return result[0] as BoardDO;
  }

  /**
   * 获取space下的第一个Board（用于rank计算）
   */
  async trySelectFirst(spaceId: string): Promise<BoardDO | null> {
    const result = await this.db
      .select()
      .from(boards)
      .where(and(eq(boards.space_id, spaceId), isNull(boards.deleted_at)))
      .orderBy(asc(boards.rank))
      .limit(1);

    return result.length > 0 ? (result[0] as BoardDO) : null;
  }

  /**
   * 获取指定rank之后的下一个Board
   */
  async trySelectNextRank(spaceId: string, currentRank: string): Promise<BoardDO | null> {
    const result = await this.db
      .select()
      .from(boards)
      .where(
        and(
          eq(boards.space_id, spaceId),
          sql`${boards.rank} > ${currentRank}`,
          isNull(boards.deleted_at),
        ),
      )
      .orderBy(asc(boards.rank))
      .limit(1);

    return result.length > 0 ? (result[0] as BoardDO) : null;
  }

  /**
   * 更新Board
   */
  async update(param: UpdateBoardParam): Promise<BoardDO> {
    const updateData: Record<string, any> = {};

    if (param.name !== undefined) updateData.name = param.name;
    if (param.description !== undefined) updateData.description = param.description;
    if (param.icon_name !== undefined) updateData.icon_name = param.icon_name;
    if (param.icon_color !== undefined) updateData.icon_color = param.icon_color;
    if (param.status !== undefined) updateData.status = param.status;
    if (param.pinned_at !== undefined) updateData.pinned_at = param.pinned_at;
    if (param.hero_image_urls !== undefined) updateData.hero_image_urls = param.hero_image_urls;
    if (param.intro !== undefined) updateData.intro = param.intro;
    if (param.rank !== undefined) updateData.rank = param.rank;

    updateData.updated_at = new Date();

    const result = await this.db
      .update(boards)
      .set(updateData)
      .where(and(eq(boards.id, param.id), isNull(boards.deleted_at)))
      .returning();

    if (result.length === 0) {
      throw new Error(`Board not found: ${param.id}`);
    }

    return result[0] as BoardDO;
  }

  /**
   * 软删除Board
   */
  async softDelete(id: string): Promise<void> {
    await this.db
      .update(boards)
      .set({ deleted_at: new Date() } as any)
      .where(and(eq(boards.id, id), isNull(boards.deleted_at)));
  }

  /**
   * 置顶Board
   */
  async pin(id: string): Promise<void> {
    await this.db
      .update(boards)
      .set({ pinned_at: new Date() } as any)
      .where(and(eq(boards.id, id), isNull(boards.deleted_at)));
  }

  /**
   * 取消置顶Board
   */
  async unpin(id: string): Promise<void> {
    await this.db
      .update(boards)
      .set({ pinned_at: null } as any)
      .where(and(eq(boards.id, id), isNull(boards.deleted_at)));
  }

  /**
   * 归档Board
   */
  async archive(id: string): Promise<BoardDO> {
    const result = await this.db
      .update(boards)
      .set({ status: BoardStatusEnum.ARCHIVED } as any)
      .where(and(eq(boards.id, id), isNull(boards.deleted_at)))
      .returning();

    if (result.length === 0) {
      throw new Error(`Board not found: ${id}`);
    }

    return result[0] as BoardDO;
  }

  /**
   * 取消归档Board
   */
  async unarchive(id: string): Promise<BoardDO> {
    const result = await this.db
      .update(boards)
      .set({ status: BoardStatusEnum.IN_PROGRESS } as any)
      .where(and(eq(boards.id, id), isNull(boards.deleted_at)))
      .returning();

    if (result.length === 0) {
      throw new Error(`Board not found: ${id}`);
    }

    return result[0] as BoardDO;
  }

  /**
   * 列出Boards
   */
  async list(param: ListBoardsParam): Promise<BoardDO[]> {
    const conditions = [eq(boards.space_id, param.space_id), isNull(boards.deleted_at)];

    if (param.fuzzy_name) {
      conditions.push(ilike(boards.name, `%${param.fuzzy_name}%`));
    }

    if (param.status) {
      conditions.push(eq(boards.status, param.status));
    }

    let query = this.db
      .select()
      .from(boards)
      .where(and(...conditions))
      .orderBy(desc(boards.pinned_at), asc(boards.rank));

    if (param.limit) {
      query = query.limit(param.limit) as any;
    }

    if (param.offset) {
      query = query.offset(param.offset) as any;
    }

    const result = await query;
    return result as BoardDO[];
  }

  /**
   * 根据snip ID列出相关的Boards
   */
  async listBySnipId(snipId: string): Promise<BoardDO[]> {
    const result = await this.db
      .select({
        id: boards.id,
        space_id: boards.space_id,
        creator_id: boards.creator_id,
        created_at: boards.created_at,
        updated_at: boards.updated_at,
        name: boards.name,
        description: boards.description,
        icon_name: boards.icon_name,
        icon_color: boards.icon_color,
        status: boards.status,
        type: boards.type,
        pinned_at: boards.pinned_at,
        hero_image_urls: boards.hero_image_urls,
        intro: boards.intro,
        rank: boards.rank,
        deleted_at: boards.deleted_at,
      })
      .from(boards)
      .innerJoin(board_items, eq(boards.id, board_items.board_id))
      .where(
        and(
          eq(board_items.snip_id, snipId),
          isNull(boards.deleted_at),
          isNull(board_items.deleted_at),
        ),
      );

    return result as BoardDO[];
  }

  /**
   * 根据thought ID列出相关的Boards
   */
  async listByThoughtId(thoughtId: string): Promise<BoardDO[]> {
    const result = await this.db
      .select({
        id: boards.id,
        space_id: boards.space_id,
        creator_id: boards.creator_id,
        created_at: boards.created_at,
        updated_at: boards.updated_at,
        name: boards.name,
        description: boards.description,
        icon_name: boards.icon_name,
        icon_color: boards.icon_color,
        status: boards.status,
        type: boards.type,
        pinned_at: boards.pinned_at,
        hero_image_urls: boards.hero_image_urls,
        intro: boards.intro,
        rank: boards.rank,
        deleted_at: boards.deleted_at,
      })
      .from(boards)
      .innerJoin(board_items, eq(boards.id, board_items.board_id))
      .where(
        and(
          eq(board_items.thought_id, thoughtId),
          isNull(boards.deleted_at),
          isNull(board_items.deleted_at),
        ),
      );

    return result as BoardDO[];
  }

  /**
   * 根据实体IDs获取简单的Board信息
   */
  async listSimpleBoardsByEntityIds(snipIds: string[], thoughtIds: string[]): Promise<BoardDO[]> {
    const conditions = [isNull(boards.deleted_at), isNull(board_items.deleted_at)];

    const entityConditions = [];

    if (snipIds.length > 0) {
      entityConditions.push(inArray(board_items.snip_id, snipIds));
    }

    if (thoughtIds.length > 0) {
      entityConditions.push(inArray(board_items.thought_id, thoughtIds));
    }

    if (entityConditions.length === 0) {
      return [];
    }

    const result = await this.db
      .select({
        id: boards.id,
        space_id: boards.space_id,
        creator_id: boards.creator_id,
        created_at: boards.created_at,
        updated_at: boards.updated_at,
        name: boards.name,
        description: boards.description,
        icon_name: boards.icon_name,
        icon_color: boards.icon_color,
        status: boards.status,
        type: boards.type,
        pinned_at: boards.pinned_at,
        hero_image_urls: boards.hero_image_urls,
        intro: boards.intro,
        rank: boards.rank,
        deleted_at: boards.deleted_at,
      })
      .from(boards)
      .innerJoin(board_items, eq(boards.id, board_items.board_id))
      .where(and(...conditions, or(...entityConditions)))
      .groupBy(boards.id);

    return result as BoardDO[];
  }

  /**
   * 根据IDs批量获取Board
   */
  async selectByIds(ids: string[]): Promise<BoardDO[]> {
    if (ids.length === 0) return [];

    const result = await this.db
      .select()
      .from(boards)
      .where(and(inArray(boards.id, ids), isNull(boards.deleted_at)));

    return result as BoardDO[];
  }

  /**
   * 统计用户创建的Board数量
   */
  async countByCreatorId(creatorId: string): Promise<number> {
    const result = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(boards)
      .where(and(eq(boards.creator_id, creatorId), isNull(boards.deleted_at)));

    return result[0].count;
  }

  /**
   * 将DO转换为VO
   */
  convertToVO(boardDO: BoardDO): BoardVO {
    return {
      id: boardDO.id,
      name: boardDO.name,
      description: boardDO.description,
      icon: {
        name: boardDO.icon_name,
        color: boardDO.icon_color,
      },
      pinned_at: boardDO.pinned_at || undefined,
      created_at: boardDO.created_at,
      updated_at: boardDO.updated_at,
      status: boardDO.status as BoardStatusEnum,
      hero_image_urls: boardDO.hero_image_urls || undefined,
      intro: boardDO.intro || undefined,
      type: boardDO.type as BoardTypeEnum,
      space_id: boardDO.space_id,
      creator_id: boardDO.creator_id,
      rank: boardDO.rank,
      filter: boardDO.filter || undefined,
    };
  }
}
