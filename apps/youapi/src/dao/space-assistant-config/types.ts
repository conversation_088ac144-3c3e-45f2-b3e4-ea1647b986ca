import type { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import type { space_assistant_configs } from '../db/public.schema';

/**
 * Space Assistant Config Data Object - 空间助手配置数据对象
 */
export type SpaceAssistantConfigDO = InferSelectModel<typeof space_assistant_configs>;

/**
 * Insert Space Assistant Config Data Object Parameter - 插入空间助手配置参数
 */
export type InsertSpaceAssistantConfigDOParam = InferInsertModel<typeof space_assistant_configs>;

/**
 * Update Space Assistant Config Data Object Parameter - 更新空间助手配置参数
 */
export type UpdateSpaceAssistantConfigDOParam = Partial<
  Omit<InsertSpaceAssistantConfigDOParam, 'id' | 'created_at' | 'updated_at'>
>;
