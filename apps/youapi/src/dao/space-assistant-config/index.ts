/**
 * Space Assistant Config DAO - 空间助手配置数据访问对象
 * 管理空间助手配置的CRUD操作
 *
 * Migrated from:
 * - youapp/src/lib/dao/space-assistant-config/index.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { and, asc, desc, eq, gt, isNull, lt } from 'drizzle-orm';

import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { space_assistant_configs } from '../db/public.schema';
import type {
  InsertSpaceAssistantConfigDOParam,
  SpaceAssistantConfigDO,
  UpdateSpaceAssistantConfigDOParam,
} from './types';

@Injectable()
export class SpaceAssistantConfigDAO extends BaseDAO<
  typeof space_assistant_configs._.config,
  SpaceAssistantConfigDO
> {
  private readonly logger = new Logger(SpaceAssistantConfigDAO.name);

  constructor(databaseService: DatabaseService) {
    super(databaseService, space_assistant_configs);
  }

  /**
   * 插入空间助手配置
   */
  async insert(insertParam: InsertSpaceAssistantConfigDOParam): Promise<SpaceAssistantConfigDO> {
    this.logger.debug(`Inserting space assistant config: ${JSON.stringify(insertParam)}`);
    return this.insertOne(insertParam);
  }

  /**
   * 更新空间助手配置
   */
  async update(
    space_id: string,
    assistant_id: string,
    updateParam: UpdateSpaceAssistantConfigDOParam,
  ): Promise<SpaceAssistantConfigDO[]> {
    this.logger.debug(
      `Updating space assistant config: space_id=${space_id}, assistant_id=${assistant_id}`,
    );
    return this.updateByQuery(
      and(
        isNull(space_assistant_configs.deleted_at),
        eq(space_assistant_configs.space_id, space_id),
        eq(space_assistant_configs.assistant_id, assistant_id),
      ),
      updateParam,
    );
  }

  /**
   * 根据空间ID和助手ID查询配置
   */
  async select(space_id: string, assistant_id: string): Promise<SpaceAssistantConfigDO | null> {
    return this.selectOneByQuery(
      and(
        isNull(space_assistant_configs.deleted_at),
        eq(space_assistant_configs.space_id, space_id),
        eq(space_assistant_configs.assistant_id, assistant_id),
      ),
    );
  }

  /**
   * 根据空间ID查询所有助手配置
   */
  async selectBySpaceId(space_id: string): Promise<SpaceAssistantConfigDO[]> {
    return this.selectManyByQuery(
      and(
        isNull(space_assistant_configs.deleted_at),
        eq(space_assistant_configs.space_id, space_id),
      ),
      [asc(space_assistant_configs.rank)],
    );
  }

  /**
   * 根据助手ID查询所有空间配置
   */
  async selectByAssistantId(assistant_id: string): Promise<SpaceAssistantConfigDO[]> {
    return this.selectManyByQuery(
      and(
        isNull(space_assistant_configs.deleted_at),
        eq(space_assistant_configs.assistant_id, assistant_id),
      ),
    );
  }

  /**
   * 尝试查询第一个配置（按rank排序）
   */
  async trySelectFirst(space_id: string): Promise<SpaceAssistantConfigDO | null> {
    const result = await this.databaseService.db
      .select()
      .from(space_assistant_configs)
      .where(
        and(
          isNull(space_assistant_configs.deleted_at),
          eq(space_assistant_configs.space_id, space_id),
        ),
      )
      .orderBy(asc(space_assistant_configs.rank))
      .limit(1);

    return result[0] || null;
  }

  /**
   * 尝试查询最后一个配置（按rank排序）
   */
  async trySelectLast(space_id: string): Promise<SpaceAssistantConfigDO | null> {
    const result = await this.databaseService.db
      .select()
      .from(space_assistant_configs)
      .where(
        and(
          isNull(space_assistant_configs.deleted_at),
          eq(space_assistant_configs.space_id, space_id),
        ),
      )
      .orderBy(desc(space_assistant_configs.rank))
      .limit(1);

    return result[0] || null;
  }

  /**
   * 尝试查询下一个rank的配置
   */
  async trySelectNextRank(
    space_id: string,
    current_rank: string,
  ): Promise<SpaceAssistantConfigDO | null> {
    const result = await this.databaseService.db
      .select()
      .from(space_assistant_configs)
      .where(
        and(
          isNull(space_assistant_configs.deleted_at),
          eq(space_assistant_configs.space_id, space_id),
          gt(space_assistant_configs.rank, current_rank),
        ),
      )
      .orderBy(asc(space_assistant_configs.rank))
      .limit(1);

    return result[0] || null;
  }

  /**
   * 尝试查询上一个rank的配置
   */
  async trySelectPrevRank(
    space_id: string,
    current_rank: string,
  ): Promise<SpaceAssistantConfigDO | null> {
    const result = await this.databaseService.db
      .select()
      .from(space_assistant_configs)
      .where(
        and(
          isNull(space_assistant_configs.deleted_at),
          eq(space_assistant_configs.space_id, space_id),
          lt(space_assistant_configs.rank, current_rank),
        ),
      )
      .orderBy(desc(space_assistant_configs.rank))
      .limit(1);

    return result[0] || null;
  }

  /**
   * 根据助手ID删除所有空间配置
   */
  async deleteByAssistantId(assistant_id: string): Promise<void> {
    this.logger.debug(`Deleting all space assistant configs for assistant_id: ${assistant_id}`);
    await this.databaseService.db
      .update(space_assistant_configs)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(
        and(
          isNull(space_assistant_configs.deleted_at),
          eq(space_assistant_configs.assistant_id, assistant_id),
        ),
      );
  }

  /**
   * 根据空间ID删除所有助手配置
   */
  async deleteBySpaceId(space_id: string): Promise<void> {
    this.logger.debug(`Deleting all space assistant configs for space_id: ${space_id}`);
    await this.databaseService.db
      .update(space_assistant_configs)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(
        and(
          isNull(space_assistant_configs.deleted_at),
          eq(space_assistant_configs.space_id, space_id),
        ),
      );
  }

  /**
   * 删除特定的空间助手配置
   */
  async delete(space_id: string, assistant_id: string): Promise<void> {
    this.logger.debug(
      `Deleting space assistant config: space_id=${space_id}, assistant_id=${assistant_id}`,
    );
    await this.databaseService.db
      .update(space_assistant_configs)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(
        and(
          isNull(space_assistant_configs.deleted_at),
          eq(space_assistant_configs.space_id, space_id),
          eq(space_assistant_configs.assistant_id, assistant_id),
        ),
      );
  }

  /**
   * 测试方法 - 验证DAO是否正常工作
   */
  async testConnection(): Promise<boolean> {
    try {
      this.logger.debug('Testing SpaceAssistantConfigDAO connection...');
      // 尝试执行一个简单的查询来验证连接
      await this.databaseService.db
        .select({ count: space_assistant_configs.id })
        .from(space_assistant_configs)
        .limit(1);
      this.logger.debug('SpaceAssistantConfigDAO connection test successful');
      return true;
    } catch (error) {
      this.logger.error('SpaceAssistantConfigDAO connection test failed:', error);
      return false;
    }
  }
}
