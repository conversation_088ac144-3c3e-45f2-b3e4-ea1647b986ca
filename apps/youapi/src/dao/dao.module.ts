import { Global, Module } from '@nestjs/common';
import { ActionDAO } from './action';
import { AssistantDAO } from './assistant';
import { BlockDAO } from './block';
import { BoardDAO } from './board';
import { BoardGroupDAO } from './board-group';
import { BoardItemDAO } from './board-item';
import { ChatDAO, CompletionBlockDAO, MessageDAO } from './chat';
import { ContentDAO } from './content';
import { DatabaseService } from './db/database.service';
import { DiffReviewEventDAO } from './diff-review-event';
import { FavoriteDAO } from './favorite';
import { NoteDAO } from './note';
import { PlaylistItemDAO } from './playlist-item';
import { ShortLinkDAO } from './short-link';
import { SnipDAO } from './snip';
import { SnipThoughtRelationDAO } from './snip-thought-relation';
import { SpaceDAO } from './space';
import { SpaceAssistantConfigDAO } from './space-assistant-config';
import { SystemConfigDAO } from './system-config';
import { ThoughtDAO } from './thought';
import { ThoughtVersionDAO } from './thought-version';
import { UsageRecordDAO } from './usage-record';
import { UserDAO, UserPreferenceDAO } from './user';
import { UserCustomerRelationDAO } from './user-customer-relation';

@Global()
@Module({
  providers: [
    DatabaseService,
    ActionDAO,
    AssistantDAO,
    BoardDAO,
    BoardGroupDAO,
    BoardItemDAO,
    ChatDAO,
    CompletionBlockDAO,
    MessageDAO,
    SnipDAO,
    SpaceDAO,
    SpaceAssistantConfigDAO,
    UsageRecordDAO,
    UserDAO,
    UserPreferenceDAO,
    FavoriteDAO,
    ShortLinkDAO,
    ThoughtDAO,
    NoteDAO,
    BlockDAO,
    ContentDAO,
    PlaylistItemDAO,
    SnipThoughtRelationDAO,
    ThoughtVersionDAO,
    DiffReviewEventDAO,
    SystemConfigDAO,
    UserCustomerRelationDAO,
  ],
  exports: [
    DatabaseService,
    ActionDAO,
    AssistantDAO,
    BoardDAO,
    BoardGroupDAO,
    BoardItemDAO,
    ChatDAO,
    CompletionBlockDAO,
    MessageDAO,
    SnipDAO,
    SpaceDAO,
    SpaceAssistantConfigDAO,
    UsageRecordDAO,
    UserDAO,
    UserPreferenceDAO,
    FavoriteDAO,
    ShortLinkDAO,
    ThoughtDAO,
    NoteDAO,
    BlockDAO,
    ContentDAO,
    PlaylistItemDAO,
    SnipThoughtRelationDAO,
    ThoughtVersionDAO,
    DiffReviewEventDAO,
    SystemConfigDAO,
    UserCustomerRelationDAO,
  ],
})
export class DaoModule {}
