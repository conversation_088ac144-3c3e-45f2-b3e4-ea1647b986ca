import { Injectable } from '@nestjs/common';
import { and, eq, getTableColumns, isNull, sql } from 'drizzle-orm';

import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { blocks, contents, snips, spaces } from '../db/public.schema';
import type { InsertSpaceDOParam, SpaceDO, UpdateSpaceDOParam } from './types';

@Injectable()
export class SpaceDAO extends BaseDAO<typeof spaces._.config, SpaceDO> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, spaces);
  }

  async selectByCreatorId(creatorId: string): Promise<SpaceDO | null> {
    return this.selectOneByQuery(and(isNull(spaces.deleted_at), eq(spaces.creator_id, creatorId))!);
  }

  async insert(insertParam: InsertSpaceDOParam): Promise<SpaceDO> {
    return this.insertOne(insertParam);
  }

  async update(id: string, updateParam: UpdateSpaceDOParam): Promise<SpaceDO> {
    return this.updateOne(id, updateParam);
  }

  async selectByBlockId(block_id: string): Promise<SpaceDO | null> {
    const result = await this.db
      .select(getTableColumns(spaces))
      .from(blocks)
      .leftJoin(snips, eq(blocks.snip_id, snips.id))
      .leftJoin(spaces, eq(snips.space_id, spaces.id))
      .where(
        and(
          isNull(blocks.deleted_at),
          eq(blocks.id, block_id),
          isNull(snips.deleted_at),
          isNull(spaces.deleted_at),
        ),
      );

    if (!result?.length) return null;
    return result[0] as SpaceDO;
  }

  async selectByContentId(content_id: string): Promise<SpaceDO | null> {
    const result = await this.db
      .select(getTableColumns(spaces))
      .from(contents)
      .leftJoin(snips, eq(contents.snip_id, snips.id))
      .leftJoin(spaces, eq(snips.space_id, spaces.id))
      .where(
        and(
          isNull(contents.deleted_at),
          eq(contents.id, content_id),
          isNull(snips.deleted_at),
          isNull(spaces.deleted_at),
        ),
      );

    if (!result?.length) return null;
    return result[0] as SpaceDO;
  }

  async countByDayAndStatus() {
    const result = await this.db.execute(sql`
SELECT
    dates.day,
    COALESCE(created.create_count, 0) as create_count,
    COALESCE(activated.activate_count, 0) as activate_count
FROM (
    SELECT DISTINCT TO_CHAR(day, 'YYYY-MM-DD') as day
    FROM generate_series(
        CURRENT_DATE - INTERVAL '30 days',
        CURRENT_DATE,
        '1 day'
    ) as day
) dates
LEFT JOIN (
    SELECT TO_CHAR(created_at, 'YYYY-MM-DD') as day, COUNT(*) as create_count
    FROM spaces
    WHERE deleted_at IS NULL
    AND created_at >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY TO_CHAR(created_at, 'YYYY-MM-DD')
) created ON dates.day = created.day
LEFT JOIN (
    SELECT TO_CHAR(activated_at, 'YYYY-MM-DD') as day, COUNT(*) as activate_count
    FROM spaces
    WHERE deleted_at IS NULL
    AND activated_at IS NOT NULL
    AND activated_at >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY TO_CHAR(activated_at, 'YYYY-MM-DD')
) activated ON dates.day = activated.day
ORDER BY dates.day
    `);
    return result as unknown as {
      day: string;
      activate_count: number;
      create_count: number;
    }[];
  }
}
