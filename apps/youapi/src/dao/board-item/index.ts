/**
 * Board Item DAO - 看板项目数据访问对象
 * 看板项目的数据库操作封装，包括增删改查等基础操作
 *
 * Migrated from:
 * - /lib/dao/board-item/index.ts (youapp)
 * - /lib/domain/board-item/index.ts (youapp)
 */

import { Injectable } from '@nestjs/common';
import { and, count, desc, eq, gt, inArray, isNotNull, isNull, type SQL, sql } from 'drizzle-orm';
import type { PgColumn } from 'drizzle-orm/pg-core';
import { BoardItemTypeEnum } from '../../common/types/board.types';
import { DatabaseService } from '../db/database.service';
import { board_items, boards } from '../db/public.schema';
import type {
  BoardItemDO,
  BoardItemInsertDO,
  BoardItemUpdateDO,
  BoardItemVO,
  DeleteBoardItemParams,
  DeleteBoardItemsByParentGroupParams,
  MoveEntityToBoardParams,
  PutEntityToBoardParams,
  SelectBoardItemsByBoardIdParams,
  SelectBoardItemsByEntityParams,
  SelectBoardItemsByParentGroupParams,
} from './types';

@Injectable()
export class BoardItemDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * 插入新的看板项目
   * 创建新的看板项目记录
   */
  async insert(data: BoardItemInsertDO): Promise<BoardItemDO> {
    const insertData = {
      ...data,
      created_at: new Date(),
      updated_at: new Date(),
    };

    const result = await this.databaseService.db.insert(board_items).values(insertData).returning();

    return result[0] as any;
  }

  /**
   * 根据ID查询看板项目
   * 获取指定ID的看板项目详情
   */
  async selectById(id: string): Promise<BoardItemDO | undefined> {
    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(eq(board_items.id, id), isNull(board_items.deleted_at)))
      .limit(1);

    return result[0] as any;
  }

  /**
   * 根据ID查询看板项目（必须存在）
   * 如果不存在则抛出异常
   */
  async selectOneById(id: string): Promise<BoardItemDO> {
    const result = await this.selectById(id);
    if (!result) {
      throw new Error(`Board item not found: ${id}`);
    }
    return result;
  }

  /**
   * 根据看板ID查询看板项目
   * 获取指定看板下的所有项目
   */
  async selectByBoardId(params: SelectBoardItemsByBoardIdParams): Promise<BoardItemDO[]> {
    const conditions = [eq(board_items.board_id, params.board_id), isNull(board_items.deleted_at)];

    // 如果指定了父分组ID，则过滤
    if (params.parent_board_group_id !== undefined) {
      if (params.parent_board_group_id === null) {
        conditions.push(isNull(board_items.parent_board_group_id));
      } else {
        conditions.push(eq(board_items.parent_board_group_id, params.parent_board_group_id));
      }
    }

    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(...conditions))
      .orderBy(board_items.rank);

    return result as any;
  }

  async selectSnipsByBoardId(board_id: string): Promise<any[]> {
    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .innerJoin(
        boards,
        and(
          isNull(boards.deleted_at),
          eq(boards.id, board_id),
          eq(board_items.board_id, boards.id),
        ),
      )
      .where(and(isNull(board_items.deleted_at), isNotNull(board_items.snip_id)))
      .orderBy(board_items.rank);

    return result as any;
  }

  /**
   * 根据实体查询看板项目
   * 通过实体类型和ID查找看板项目
   */
  async selectByEntity(params: SelectBoardItemsByEntityParams): Promise<BoardItemDO | undefined> {
    let entityCondition: SQL;

    switch (params.entity_type) {
      case BoardItemTypeEnum.SNIP:
        entityCondition = eq(board_items.snip_id, params.entity_id);
        break;
      case BoardItemTypeEnum.THOUGHT:
        entityCondition = eq(board_items.thought_id, params.entity_id);
        break;
      case BoardItemTypeEnum.BOARD_GROUP:
        entityCondition = eq(board_items.board_group_id, params.entity_id);
        break;
      case BoardItemTypeEnum.CHAT:
        entityCondition = eq(board_items.chat_id, params.entity_id);
        break;
      default:
        throw new Error(`Unsupported entity type: ${params.entity_type}`);
    }

    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(entityCondition, isNull(board_items.deleted_at)))
      .limit(1);

    return result[0] as any;
  }

  /**
   * 根据父分组ID查询看板项目
   * 获取指定分组下的所有项目
   */
  async selectByParentGroupId(params: SelectBoardItemsByParentGroupParams): Promise<BoardItemDO[]> {
    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(
        and(
          eq(board_items.parent_board_group_id, params.parent_board_group_id),
          isNull(board_items.deleted_at),
        ),
      )
      .orderBy(board_items.rank);

    return result as any;
  }

  /**
   * 根据Snip ID查询看板项目
   * 获取指定Snip的所有看板项目
   */
  async selectBySnipId(snip_id: string): Promise<BoardItemDO[]> {
    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(eq(board_items.snip_id, snip_id), isNull(board_items.deleted_at)))
      .orderBy(board_items.rank);

    return result as any;
  }

  /**
   * 根据Thought ID查询看板项目
   * 获取指定Thought的所有看板项目
   */
  async selectByThoughtId(thought_id: string): Promise<BoardItemDO[]> {
    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(eq(board_items.thought_id, thought_id), isNull(board_items.deleted_at)))
      .orderBy(board_items.rank);

    return result as any;
  }

  /**
   * 根据Chat ID查询看板项目
   * 获取指定Chat的所有看板项目
   */
  async selectByChatId(chat_id: string): Promise<BoardItemDO[]> {
    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(eq(board_items.chat_id, chat_id), isNull(board_items.deleted_at)))
      .orderBy(board_items.rank);

    return result as any;
  }

  /**
   * 根据Board Group ID查询看板项目
   * 获取指定Board Group的所有看板项目
   */
  async selectByBoardGroupId(board_group_id: string): Promise<BoardItemDO[]> {
    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(eq(board_items.board_group_id, board_group_id), isNull(board_items.deleted_at)))
      .orderBy(board_items.rank);

    return result as any;
  }

  /**
   * 根据父分组ID查询看板项目（兼容方法）
   * 获取指定分组下的所有项目
   */
  async selectByParentBoardGroupId(parent_board_group_id: string): Promise<BoardItemDO[]> {
    return this.selectByParentGroupId({ parent_board_group_id });
  }

  /**
   * 尝试选择下一个排序项目
   * 用于排序操作
   */
  async trySelectNextRank(
    current_rank: string,
    parent_board_group_id?: string,
  ): Promise<BoardItemDO | undefined> {
    const conditions = [gt(board_items.rank, current_rank), isNull(board_items.deleted_at)];

    if (parent_board_group_id) {
      conditions.push(eq(board_items.parent_board_group_id, parent_board_group_id));
    } else {
      conditions.push(isNull(board_items.parent_board_group_id));
    }

    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(...conditions))
      .orderBy(board_items.rank)
      .limit(1);

    return result[0] as any;
  }

  /**
   * 尝试选择第一个项目
   * 用于排序操作
   */
  async trySelectFirst(parent_board_group_id?: string): Promise<BoardItemDO | undefined> {
    const conditions = [isNull(board_items.deleted_at)];

    if (parent_board_group_id) {
      conditions.push(eq(board_items.parent_board_group_id, parent_board_group_id));
    } else {
      conditions.push(isNull(board_items.parent_board_group_id));
    }

    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(...conditions))
      .orderBy(board_items.rank)
      .limit(1);

    return result[0] as any;
  }

  /**
   * 根据看板ID获取实体ID
   * 获取指定看板中指定类型实体的ID列表
   */
  async getEntityIdsByBoardIds(
    entityType: 'snip' | 'thought' | 'chat',
    boardIds: string[],
  ): Promise<{ entity_id: string }[]> {
    let entityField: PgColumn;
    switch (entityType) {
      case 'snip':
        entityField = board_items.snip_id;
        break;
      case 'thought':
        entityField = board_items.thought_id;
        break;
      case 'chat':
        entityField = board_items.chat_id;
        break;
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }

    const result = await this.databaseService.db
      .select({ entity_id: entityField })
      .from(board_items)
      .where(
        and(
          inArray(board_items.board_id, boardIds),
          isNull(board_items.deleted_at),
          isNotNull(entityField),
        ),
      );

    return result.filter((item) => item.entity_id !== null) as {
      entity_id: string;
    }[];
  }

  /**
   * 根据看板分组ID获取实体ID
   * 获取指定看板分组中指定类型实体的ID列表
   */
  async getEntityIdsByBoardGroupIds(
    entityType: 'snip' | 'thought' | 'chat',
    boardGroupIds: string[],
  ): Promise<{ entity_id: string }[]> {
    let entityField: PgColumn;
    switch (entityType) {
      case 'snip':
        entityField = board_items.snip_id as PgColumn;
        break;
      case 'thought':
        entityField = board_items.thought_id as PgColumn;
        break;
      case 'chat':
        entityField = board_items.chat_id as PgColumn;
        break;
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }

    const result = await this.databaseService.db
      .select({ entity_id: entityField })
      .from(board_items)
      .where(
        and(
          inArray(board_items.parent_board_group_id, boardGroupIds),
          isNull(board_items.deleted_at),
          isNotNull(entityField),
        ),
      );

    return result.filter((item) => item.entity_id !== null) as {
      entity_id: string;
    }[];
  }

  /**
   * 统计看板项目数量
   * 按实体类型统计指定看板的项目数量
   */
  async countByBoardIds(
    board_ids: string[],
  ): Promise<{ board_id: string; snips_count: number; thoughts_count: number }[]> {
    // TODO: 实现复杂的统计查询
    // 这里需要根据实际需求实现分组统计
    throw new Error('countByBoardIds not implemented - TODO');
  }

  /**
   * 统计非分组类型的看板项目数量
   * 统计指定看板中不在任何分组内的项目数量
   */
  async countExcludeGroup(board_id: string): Promise<number> {
    const result = await this.databaseService.db
      .select({ count: count() })
      .from(board_items)
      .where(
        and(
          isNull(board_items.deleted_at),
          eq(board_items.board_id, board_id),
          isNull(board_items.board_group_id),
        ),
      );
    return Number(result[0].count);
  }

  /**
   * 使用游标查询非分组类型的看板项目
   * 支持游标分页，按创建时间倒序排列
   */
  async selectByCursorExcludeGroup(
    board_id: string,
    limit: number,
    starting_after?: string,
  ): Promise<BoardItemDO[]> {
    const conditions = [
      isNull(board_items.deleted_at),
      eq(board_items.board_id, board_id),
      isNull(board_items.board_group_id),
    ];

    if (starting_after) {
      const startingItem = await this.selectById(starting_after);
      if (startingItem) {
        conditions.push(gt(board_items.created_at, startingItem.created_at));
      }
    }

    const result = await this.databaseService.db
      .select()
      .from(board_items)
      .where(and(...conditions))
      .orderBy(desc(board_items.created_at))
      .limit(limit);

    return result as any;
  }

  /**
   * 根据Snip ID列表查询对应的看板ID
   * 返回每个Snip ID对应的所有看板ID列表
   */
  async selectBoardIdsBySnipIds(
    snip_ids: string[],
  ): Promise<Array<{ snip_id: string; board_ids: string[] }>> {
    const result = await this.databaseService.db
      .select({
        snip_id: board_items.snip_id,
        board_ids: sql<string[]>`array_agg(${board_items.board_id})`,
      })
      .from(board_items)
      .where(and(isNull(board_items.deleted_at), inArray(board_items.snip_id, snip_ids)))
      .groupBy(board_items.snip_id);

    return result as any;
  }

  /**
   * 根据Thought ID列表查询对应的看板ID
   * 返回每个Thought ID对应的所有看板ID列表
   */
  async selectBoardIdsByThoughtIds(
    thought_ids: string[],
  ): Promise<Array<{ thought_id: string; board_ids: string[] }>> {
    const result = await this.databaseService.db
      .select({
        thought_id: board_items.thought_id,
        board_ids: sql<string[]>`array_agg(${board_items.board_id})`,
      })
      .from(board_items)
      .where(and(isNull(board_items.deleted_at), inArray(board_items.thought_id, thought_ids)))
      .groupBy(board_items.thought_id);

    return result as any;
  }

  /**
   * 更新看板项目信息
   * 更新指定看板项目的属性
   */
  async update(data: BoardItemUpdateDO): Promise<BoardItemDO> {
    const updateData = {
      ...data,
      updated_at: new Date(),
    };

    // Remove undefined values
    Object.keys(updateData).forEach((key) => {
      if (updateData[key as keyof BoardItemUpdateDO] === undefined) {
        delete updateData[key as keyof BoardItemUpdateDO];
      }
    });

    const result = await this.databaseService.db
      .update(board_items)
      .set(updateData as any)
      .where(and(eq(board_items.id, data.id), isNull(board_items.deleted_at)))
      .returning();

    if (result.length === 0) {
      throw new Error(`Board item not found or already deleted: ${data.id}`);
    }

    return result[0] as any;
  }

  /**
   * 根据Snip ID更新看板项目
   * 更新指定Snip的看板项目属性
   */
  async updateBySnipId(snip_id: string, data: Partial<BoardItemUpdateDO>): Promise<void> {
    const updateData = {
      ...data,
      updated_at: new Date(),
    };

    // Remove undefined values
    Object.keys(updateData).forEach((key) => {
      if (updateData[key as keyof BoardItemUpdateDO] === undefined) {
        delete updateData[key as keyof BoardItemUpdateDO];
      }
    });

    await this.databaseService.db
      .update(board_items)
      .set(updateData as any)
      .where(and(eq(board_items.snip_id, snip_id), isNull(board_items.deleted_at)));
  }

  /**
   * 根据Thought ID更新看板项目
   * 更新指定Thought的看板项目属性
   */
  async updateByThoughtId(thought_id: string, data: Partial<BoardItemUpdateDO>): Promise<void> {
    const updateData = {
      ...data,
      updated_at: new Date(),
    };

    // Remove undefined values
    Object.keys(updateData).forEach((key) => {
      if (updateData[key as keyof BoardItemUpdateDO] === undefined) {
        delete updateData[key as keyof BoardItemUpdateDO];
      }
    });

    await this.databaseService.db
      .update(board_items)
      .set(updateData as any)
      .where(and(eq(board_items.thought_id, thought_id), isNull(board_items.deleted_at)));
  }

  /**
   * 软删除看板项目
   * 标记看板项目为已删除状态
   */
  async delete(params: DeleteBoardItemParams): Promise<void> {
    const result = await this.databaseService.db
      .update(board_items)
      .set({
        deleted_at: new Date(),
        updated_at: new Date(),
      } as any)
      .where(and(eq(board_items.id, params.id), isNull(board_items.deleted_at)))
      .returning();

    if (result.length === 0) {
      throw new Error(`Board item not found or already deleted: ${params.id}`);
    }
  }

  /**
   * 根据看板ID删除看板项目
   * 删除指定看板下的所有项目
   */
  async deleteByBoardId(board_id: string): Promise<void> {
    await this.databaseService.db
      .update(board_items)
      .set({
        deleted_at: new Date(),
        updated_at: new Date(),
      } as any)
      .where(and(eq(board_items.board_id, board_id), isNull(board_items.deleted_at)));
  }

  /**
   * 根据Snip ID删除看板项目
   * 删除指定Snip的所有看板项目
   */
  async deleteBySnipId(snip_id: string): Promise<void> {
    await this.databaseService.db
      .update(board_items)
      .set({
        deleted_at: new Date(),
        updated_at: new Date(),
      } as any)
      .where(and(eq(board_items.snip_id, snip_id), isNull(board_items.deleted_at)));
  }

  /**
   * 根据Thought ID删除看板项目
   * 删除指定Thought的所有看板项目
   */
  async deleteByThoughtId(thought_id: string): Promise<void> {
    await this.databaseService.db
      .update(board_items)
      .set({
        deleted_at: new Date(),
        updated_at: new Date(),
      } as any)
      .where(and(eq(board_items.thought_id, thought_id), isNull(board_items.deleted_at)));
  }

  /**
   * 根据Chat ID删除看板项目
   * 删除指定Chat的所有看板项目
   */
  async deleteByChatId(chat_id: string): Promise<void> {
    await this.databaseService.db
      .update(board_items)
      .set({
        deleted_at: new Date(),
        updated_at: new Date(),
      } as any)
      .where(and(eq(board_items.chat_id, chat_id), isNull(board_items.deleted_at)));
  }

  /**
   * 根据父分组ID删除看板项目
   * 删除指定分组下的所有项目
   */
  async deleteByParentBoardGroupId(params: DeleteBoardItemsByParentGroupParams): Promise<void> {
    await this.databaseService.db
      .update(board_items)
      .set({
        deleted_at: new Date(),
        updated_at: new Date(),
      } as any)
      .where(
        and(
          eq(board_items.parent_board_group_id, params.parent_board_group_id),
          isNull(board_items.deleted_at),
        ),
      );
  }

  /**
   * 将实体添加到看板
   * 幂等操作，如果已存在则更新
   */
  async putEntityToBoard(params: PutEntityToBoardParams): Promise<BoardItemDO> {
    // 检查是否已存在
    const existing = await this.selectByEntity({
      entity_type: params.entity_type,
      entity_id: params.entity_id,
    });

    if (existing) {
      // 更新现有记录
      return this.update({
        id: existing.id,
        parent_board_group_id: params.parent_board_group_id,
      });
    } else {
      // 创建新记录
      const insertData: BoardItemInsertDO = {
        board_id: params.board_id,
        parent_board_group_id: params.parent_board_group_id,
        rank: params.rank_after || 'a0', // 默认排序值
      };

      // 根据实体类型设置对应字段
      switch (params.entity_type) {
        case BoardItemTypeEnum.SNIP:
          insertData.snip_id = params.entity_id;
          break;
        case BoardItemTypeEnum.THOUGHT:
          insertData.thought_id = params.entity_id;
          break;
        case BoardItemTypeEnum.BOARD_GROUP:
          insertData.board_group_id = params.entity_id;
          break;
        case BoardItemTypeEnum.CHAT:
          insertData.chat_id = params.entity_id;
          break;
        default:
          throw new Error(`Unsupported entity type: ${params.entity_type}`);
      }

      return this.insert(insertData);
    }
  }

  /**
   * 移动实体到看板
   * 将实体从一个看板移动到另一个看板
   */
  async moveEntityToBoard(params: MoveEntityToBoardParams): Promise<void> {
    const existing = await this.selectByEntity({
      entity_type: params.entity_type,
      entity_id: params.entity_id,
    });

    if (existing) {
      await this.update({
        id: existing.id,
        parent_board_group_id: params.parent_board_group_id,
      });
    } else {
      await this.putEntityToBoard({
        board_id: params.board_id,
        parent_board_group_id: params.parent_board_group_id,
        entity_type: params.entity_type,
        entity_id: params.entity_id,
      });
    }
  }

  /**
   * 转换为VO对象
   * 将数据库对象转换为业务层对象
   */
  convertToVO(boardItem: BoardItemDO): BoardItemVO {
    return {
      id: boardItem.id,
      created_at: boardItem.created_at,
      updated_at: boardItem.updated_at,
      board_id: boardItem.board_id,
      parent_board_group_id: boardItem.parent_board_group_id,
      snip_id: boardItem.snip_id,
      thought_id: boardItem.thought_id,
      board_group_id: boardItem.board_group_id,
      chat_id: boardItem.chat_id,
      rank: boardItem.rank,
    };
  }

  /**
   * 批量转换为VO对象
   * 将数据库对象数组转换为业务层对象数组
   */
  convertToVOs(boardItems: BoardItemDO[]): BoardItemVO[] {
    return boardItems.map((boardItem) => this.convertToVO(boardItem));
  }

  /**
   * 根据实体IDs获取看板信息
   * 返回包含snip_ids和thought_ids的看板信息
   */
  async selectBoardsInfoByEntityIds(
    snipIds: string[],
    thoughtIds: string[],
  ): Promise<
    Array<{
      id: string;
      name: string;
      icon_name: string | null;
      icon_color: string | null;
      snip_ids: string[];
      thought_ids: string[];
    }>
  > {
    // 获取所有相关的board_id
    const boardIds = new Set<string>();

    // 从snip_ids获取board_ids
    if (snipIds.length > 0) {
      const snipBoardResults = await this.selectBoardIdsBySnipIds(snipIds);
      snipBoardResults.forEach((result: { snip_id: string; board_ids: string[] }) => {
        result.board_ids.forEach((boardId) => boardIds.add(boardId));
      });
    }

    // 从thought_ids获取board_ids
    if (thoughtIds.length > 0) {
      const thoughtBoardResults = await this.selectBoardIdsByThoughtIds(thoughtIds);
      thoughtBoardResults.forEach((result: { thought_id: string; board_ids: string[] }) => {
        result.board_ids.forEach((boardId) => boardIds.add(boardId));
      });
    }

    if (boardIds.size === 0) {
      return [];
    }

    // 获取board信息
    const boardResults = await this.databaseService.db
      .select({
        id: boards.id,
        name: boards.name,
        icon_name: boards.icon_name,
        icon_color: boards.icon_color,
      })
      .from(boards)
      .where(and(inArray(boards.id, Array.from(boardIds)), isNull(boards.deleted_at)));

    // 为每个board构建snip_ids和thought_ids
    const boardInfoMap = new Map<
      string,
      {
        id: string;
        name: string;
        icon_name: string | null;
        icon_color: string | null;
        snip_ids: string[];
        thought_ids: string[];
      }
    >();

    // 初始化board信息
    boardResults.forEach(
      (board: {
        id: string;
        name: string;
        icon_name: string | null;
        icon_color: string | null;
      }) => {
        boardInfoMap.set(board.id, {
          ...board,
          snip_ids: [],
          thought_ids: [],
        });
      },
    );

    // 填充snip_ids
    if (snipIds.length > 0) {
      const snipBoardResults = await this.selectBoardIdsBySnipIds(snipIds);
      snipBoardResults.forEach((result: { snip_id: string; board_ids: string[] }) => {
        result.board_ids.forEach((boardId) => {
          const boardInfo = boardInfoMap.get(boardId);
          if (boardInfo) {
            boardInfo.snip_ids.push(result.snip_id);
          }
        });
      });
    }

    // 填充thought_ids
    if (thoughtIds.length > 0) {
      const thoughtBoardResults = await this.selectBoardIdsByThoughtIds(thoughtIds);
      thoughtBoardResults.forEach((result: { thought_id: string; board_ids: string[] }) => {
        result.board_ids.forEach((boardId) => {
          const boardInfo = boardInfoMap.get(boardId);
          if (boardInfo) {
            boardInfo.thought_ids.push(result.thought_id);
          }
        });
      });
    }

    return Array.from(boardInfoMap.values());
  }
}
