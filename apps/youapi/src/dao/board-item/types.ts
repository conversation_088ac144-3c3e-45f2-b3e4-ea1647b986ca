/**
 * Board Item DAO Types
 * 看板项目数据访问对象类型定义
 *
 * Migrated from:
 * - /lib/dao/board-item (youapp)
 * - /lib/domain/board-item (youapp)
 */

import { z } from 'zod';

import type { BoardItemTypeEnum } from '../../common/types/board.types';

// Database Object (DO) - 数据库实体类型
export interface BoardItemDO {
  id: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  board_id: string;
  parent_board_group_id: string | null;
  snip_id: string | null;
  thought_id: string | null;
  board_group_id: string | null;
  chat_id: string | null;
  rank: string;
}

// Insert Object - 插入数据类型
export interface BoardItemInsertDO {
  id?: string;
  board_id: string;
  parent_board_group_id?: string | null;
  snip_id?: string | null;
  thought_id?: string | null;
  board_group_id?: string | null;
  chat_id?: string | null;
  rank: string;
}

// Update Object - 更新数据类型
export interface BoardItemUpdateDO {
  id: string;
  parent_board_group_id?: string | null;
  snip_id?: string | null;
  thought_id?: string | null;
  board_group_id?: string | null;
  chat_id?: string | null;
  rank?: string;
  updated_at?: Date;
}

// Value Object (VO) - 业务层对象类型
export interface BoardItemVO {
  id: string;
  created_at: Date;
  updated_at: Date;
  board_id: string;
  parent_board_group_id: string | null;
  snip_id: string | null;
  thought_id: string | null;
  board_group_id: string | null;
  chat_id: string | null;
  rank: string;
}

// DAO Method Parameters
export interface SelectBoardItemByIdParams {
  id: string;
}

export interface SelectBoardItemsByBoardIdParams {
  board_id: string;
  parent_board_group_id?: string | null;
}

export interface SelectBoardItemsByEntityParams {
  entity_type: BoardItemTypeEnum;
  entity_id: string;
}

export interface SelectBoardItemsByParentGroupParams {
  parent_board_group_id: string;
}

export interface DeleteBoardItemParams {
  id: string;
}

export interface DeleteBoardItemsByParentGroupParams {
  parent_board_group_id: string;
}

export interface CountBoardItemsByBoardIdParams {
  board_id: string;
}

// Business Logic Parameters
export interface PutEntityToBoardParams {
  board_id: string;
  parent_board_group_id?: string | null;
  rank_after?: string;
  entity_type: BoardItemTypeEnum;
  entity_id: string;
}

export interface PutEntitiesToBoardParams {
  board_id: string;
  parent_board_group_id?: string | null;
  rank_after?: string;
  items: {
    entity_type: BoardItemTypeEnum;
    entity_id: string;
  }[];
}

export interface MoveEntityToBoardParams {
  entity_type: BoardItemTypeEnum;
  entity_id: string;
  board_id: string;
  parent_board_group_id?: string | null;
}

// Validation Schemas
export const BoardItemVOSchema = z.object({
  id: z.string().uuid(),
  created_at: z.date(),
  updated_at: z.date(),
  board_id: z.string().uuid(),
  parent_board_group_id: z.string().uuid().nullable(),
  snip_id: z.string().uuid().nullable(),
  thought_id: z.string().uuid().nullable(),
  board_group_id: z.string().uuid().nullable(),
  chat_id: z.string().uuid().nullable(),
  rank: z.string().min(1),
});

export const BoardItemInsertDOSchema = z.object({
  id: z.string().uuid().optional(),
  board_id: z.string().uuid(),
  parent_board_group_id: z.string().uuid().nullable().optional(),
  snip_id: z.string().uuid().nullable().optional(),
  thought_id: z.string().uuid().nullable().optional(),
  board_group_id: z.string().uuid().nullable().optional(),
  chat_id: z.string().uuid().nullable().optional(),
  rank: z.string().min(1),
});

export const BoardItemUpdateDOSchema = z.object({
  id: z.string().uuid(),
  parent_board_group_id: z.string().uuid().nullable().optional(),
  snip_id: z.string().uuid().nullable().optional(),
  thought_id: z.string().uuid().nullable().optional(),
  board_group_id: z.string().uuid().nullable().optional(),
  chat_id: z.string().uuid().nullable().optional(),
  rank: z.string().min(1).optional(),
  updated_at: z.date().optional(),
});
