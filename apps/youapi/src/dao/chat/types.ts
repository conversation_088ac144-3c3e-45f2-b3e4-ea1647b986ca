import type z from 'zod';
import type {
  MessageContext,
  MessageModeEnum,
  MessageRoleEnum,
  MessageStatusEnum,
  RestErrorInfo,
  UseToolSchema,
} from '@/common/types';
import type { chats, completionBlocks, messages } from '../db/public.schema';

export type ChatDO = typeof chats.$inferSelect & {
  messages: MessageDO[];
};
export type InsertChatDOParam = typeof chats.$inferInsert;
export type UpdateChatDOParam = Partial<typeof chats.$inferInsert>;

export type MessageDO = typeof messages.$inferSelect & {
  role: MessageRoleEnum;
  status: MessageStatusEnum;
  content: string;
  context: MessageContext[];
  blocks?: CompletionBlockDO[];
  tools?: z.infer<typeof UseToolSchema>;
  error?: RestErrorInfo;
  mode?: MessageModeEnum;
  command?: string;
  shortcut?: string;
};
export type MessageWithBlocksDO = MessageDO & Required<Pick<MessageDO, 'blocks'>>;

export type InsertMessageDOParam = typeof messages.$inferInsert;
export type UpdateMessageDOParam = Partial<typeof messages.$inferInsert>;

export type CompletionBlockDO = typeof completionBlocks.$inferSelect;
export type InsertCompletionBlockDOParam = typeof completionBlocks.$inferInsert;
export type UpdateCompletionBlockDOParam = Partial<typeof completionBlocks.$inferInsert>;
