import { Injectable } from '@nestjs/common';
import { and, asc, desc, eq, gt, inArray, isNull, lt, type SQL, sql } from 'drizzle-orm';
import type { z } from 'zod';
import { NotFound, ResourceEnum } from '../../common/errors';
import {
  ChatModeEnum,
  type ChatOrigin,
  ChatOriginTypeEnum,
  type ChatSnipOriginSchema,
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  type MessageContext,
  type MessageRoleEnum,
  type MessageStatusEnum,
} from '../../common/types';
import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { chats, completionBlocks, messages } from '../db/public.schema';
import type { ChatDO, CompletionBlockDO, MessageDO, MessageWithBlocksDO } from './types';

@Injectable()
export class ChatDAO extends BaseDAO<typeof chats._.config, ChatDO> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, chats);
  }

  /**
   * 根据ID查询聊天记录
   */
  async selectById(id: string): Promise<ChatDO> {
    const result = await this.trySelectById(id);
    if (!result) {
      throw new NotFound({
        resource: ResourceEnum.CHAT,
        id,
      });
    }
    return result;
  }

  /**
   * 根据ID尝试查询聊天记录
   */
  async trySelectById(id: string): Promise<ChatDO | null> {
    const result = await this.databaseService.db
      .select()
      .from(chats)
      .where(and(eq(chats.id, id), isNull(chats.deletedAt)))
      .limit(1);

    return result.length > 0 ? (result[0] as ChatDO) : null;
  }

  /**
   * 根据聊天ID查询聊天内容
   * 用于网页上下文构建器
   */
  async selectContentById(chat_id: string): Promise<string | null> {
    const result = await this.databaseService.db
      .select({
        content: messages.content,
      })
      .from(messages)
      .where(and(eq(messages.chatId, chat_id), isNull(messages.deletedAt)))
      .orderBy(messages.createdAt);

    // 合并所有消息内容
    const contents = result
      .map((row) => row.content)
      .filter((content) => content)
      .join('\n\n');

    return contents || null;
  }

  /**
   * 更新聊天记录的来源信息
   */
  async updateOrigin(
    chat_id: string,
    origin: {
      type: ChatOriginTypeEnum;
      id: string;
    },
    board_id?: string,
  ): Promise<void> {
    await this.databaseService.db
      .update(chats)
      .set({
        origin_type: origin.type,
        origin_id: origin.id,
        board_id: board_id || null,
        updated_at: new Date(),
      } as any)
      .where(eq(chats.id, chat_id));
  }

  /**
   * 更新聊天记录的标题
   */
  async updateTitle(id: string, title: string, skipTimestampUpdate: boolean = false) {
    return await this.updateOne(id, {
      title,
      ...(skipTimestampUpdate ? {} : { updated_at: new Date() }),
    });
  }

  /**
   * 更新聊天记录的精髓信息
   */
  async updateEssence(
    id: string,
    essence: string,
    last_updated_at: Date,
    trace_id: string,
  ): Promise<void> {
    await this.databaseService.db
      .update(chats)
      .set({
        essence,
        essence_last_updated_at: last_updated_at,
        essence_trace_id: trace_id,
        updated_at: new Date(),
      } as any)
      .where(eq(chats.id, id));
  }

  /**
   * 根据聊天ID查询有效消息列表
   */
  async selectValidMessagesById(
    chat_id: string,
    options?: {
      roles?: MessageRoleEnum[];
      order?: 'asc' | 'desc';
      statuses?: string[];
    },
  ): Promise<MessageDO[]> {
    const result = await this.databaseService.db
      .select()
      .from(messages)
      .where(and(eq(messages.chatId, chat_id), isNull(messages.deletedAt)))
      .orderBy(options?.order === 'desc' ? desc(messages.createdAt) : asc(messages.createdAt));

    let filteredResult = result as MessageDO[];

    // Apply role filtering in memory if needed
    if (options?.roles && options.roles.length > 0) {
      filteredResult = filteredResult.filter((msg) =>
        options.roles!.includes(msg.role as MessageRoleEnum),
      );
    }

    // Apply status filtering in memory if needed
    if (options?.statuses && options.statuses.length > 0) {
      filteredResult = filteredResult.filter((msg) => options.statuses!.includes(msg.status || ''));
    }

    return filteredResult;
  }

  async selectByUserAndOrigin(
    userId: string,
    origin: ChatOrigin,
    modes: ChatModeEnum[] = [ChatModeEnum.CHAT],
  ) {
    const clauses = [
      isNull(chats.deletedAt),
      eq(chats.creatorId, userId),
      modes.length > 1 ? inArray(chats.mode, modes) : eq(chats.mode, modes[0]),
      eq(chats.originType, origin.type),
      (origin as z.infer<typeof ChatSnipOriginSchema>).id
        ? eq(chats.originId, (origin as z.infer<typeof ChatSnipOriginSchema>).id)
        : isNull(chats.originId),
      origin.type === ChatOriginTypeEnum.WEBPAGE && origin.url
        ? eq(chats.originUrl, origin.url)
        : isNull(chats.originUrl),
    ];

    const query = this.databaseService.db
      .select()
      .from(chats)
      .where(and(...clauses) as SQL)
      .orderBy(desc(chats.updatedAt));

    const result = await query;
    if (!result || !result.length) return [];
    return result as ChatDO[];
  }

  /**
   * Select chat details by user and origin
   */
  async selectDetailsByUserAndOrigin(
    userId: string,
    origin: ChatOrigin,
    modes: ChatModeEnum[] = [ChatModeEnum.CHAT],
  ): Promise<ChatDO[]> {
    return this.selectByUserAndOrigin(userId, origin, modes);
  }

  /**
   * Select chat details for chat assistant
   */
  async selectDetailsForChatAssistant(param: {
    user_id: string;
    chat_id: string;
    limit?: number;
  }): Promise<ChatDO[]> {
    const { user_id, chat_id, limit = 10 } = param;

    const result = await this.databaseService.db
      .select()
      .from(chats)
      .where(and(eq(chats.creatorId, user_id), eq(chats.id, chat_id), isNull(chats.deletedAt)))
      .limit(limit);

    return result as ChatDO[];
  }

  /**
   * Select chat details by IDs
   */
  async selectDetailByIds(ids: string[]): Promise<ChatDO[]> {
    if (ids.length === 0) return [];

    const result = await this.databaseService.db
      .select()
      .from(chats)
      .where(and(inArray(chats.id, ids), isNull(chats.deletedAt)));

    return result as ChatDO[];
  }

  /**
   * Select chat detail by ID with messages and completion blocks
   * Uses efficient three-table JOIN similar to youapp implementation
   *
   * 对应youapp的selectDetailById方法，使用高效的三表JOIN查询
   * 一次查询获取聊天、消息和完成块的完整数据
   */
  async selectDetailById(id: string) {
    const clauses = [isNull(chats.deletedAt), eq(chats.id, id)];

    const query = this.databaseService.db
      .select({
        chats,
        chat_messages: messages,
        blocks: completionBlocks,
      })
      .from(chats)
      .leftJoin(messages, and(isNull(messages.deletedAt), eq(messages.chatId, chats.id)))
      .leftJoin(
        completionBlocks,
        and(isNull(completionBlocks.deletedAt), eq(completionBlocks.messageId, messages.id)),
      )
      .where(and(...clauses) as SQL)
      .orderBy(asc(messages.createdAt), asc(completionBlocks.createdAt), desc(chats.createdAt));

    const result = await query;
    if (!result || !result.length) return null;
    return this.handleJoinResult(result)[0];
  }

  private handleJoinResult(
    result: Array<{
      chats: typeof chats.$inferSelect;
      chat_messages: typeof messages.$inferSelect | null;
      blocks: typeof completionBlocks.$inferSelect | null;
    }>,
  ) {
    const aggregates = result.reduce<Record<string, ChatDO>>((accu, row) => {
      const chat = row.chats;
      if (!accu[chat.id]) {
        accu[chat.id] = {
          ...chat,
          messages: [],
        } as ChatDO;
      }
      let message_index = accu[chat.id].messages!.findIndex((m) => m.id === row.chat_messages?.id);
      if (message_index === -1 && row.chat_messages) {
        accu[chat.id]!.messages!.push({
          ...(row.chat_messages as MessageDO),
          blocks: [],
        } as MessageWithBlocksDO);
        message_index = accu[chat.id].messages!.length - 1;
      }
      if (row.blocks) {
        accu[chat.id].messages![message_index].blocks!.push({
          ...(row.blocks as CompletionBlockDO),
        });
      }

      return accu;
    }, {});

    return Object.values(aggregates).map((r) => this.transformDO(r));
  }

  /**
   * 聚合JOIN查询结果为单个ChatDO对象
   * 类似youapp的handleJoinResult方法
   */
  private aggregateJoinResult(result: any[]): ChatDO | null {
    return null;
    // if (!result || result.length === 0) {
    //   return null;
    // }

    // 获取第一行的chat数据
    const firstRow = result[0];
    const chat: ChatDO = {
      id: firstRow.chat_id,
      createdAt: firstRow.chat_createdAt,
      updatedAt: firstRow.chat_updatedAt,
      deletedAt: firstRow.chat_deletedAt,
      creatorId: firstRow.chat_creatorId,
      title: firstRow.chat_title,
      originType: firstRow.chat_originType,
      originId: firstRow.chat_originId,
      originUrl: firstRow.chat_originUrl,
      boardId: firstRow.chat_boardId,
      mode: firstRow.chat_mode,
      context: firstRow.chat_context,
      assistantId: firstRow.chat_assistantId,
      essence: firstRow.chat_essence,
      essenceLastUpdatedAt: firstRow.chat_essenceLastUpdatedAt,
      essenceTraceId: firstRow.chat_essenceTraceId,
      messages: undefined,
    };

    // return chat;
  }

  /**
   * Select chat detail by ID (simple version without joins)
   * 简单版本，不包含消息和块的数据
   */
  async selectDetailByIdSimple(id: string): Promise<ChatDO | null> {
    const result = await this.databaseService.db
      .select()
      .from(chats)
      .where(and(eq(chats.id, id), isNull(chats.deletedAt)))
      .limit(1);

    return result.length > 0 ? (result[0] as ChatDO) : null;
  }

  /**
   * List chats by user ID
   */
  async listByUserId(
    user_id: string,
    query: {
      limit?: number;
      offset?: number;
      order?: 'asc' | 'desc';
    },
  ): Promise<{
    data: ChatDO[];
    total: number;
  }> {
    const { limit = 20, offset = 0, order = 'desc' } = query;

    const result = await this.databaseService.db
      .select()
      .from(chats)
      .where(and(eq(chats.creatorId, user_id), isNull(chats.deletedAt)))
      .orderBy(order === 'desc' ? desc(chats.updatedAt) : asc(chats.updatedAt))
      .limit(limit)
      .offset(offset);

    const total = await this.databaseService.db
      .select({ count: sql<number>`count(*)` })
      .from(chats)
      .where(and(eq(chats.creatorId, user_id), isNull(chats.deletedAt)));

    return {
      data: result as ChatDO[],
      total: total[0]?.count || 0,
    };
  }

  /**
   * List chats by user ID unsorted
   */
  async listByUserIdAndUnsorted(
    user_id: string,
    query: {
      limit?: number;
      offset?: number;
    },
  ): Promise<{
    data: ChatDO[];
    total: number;
  }> {
    const { limit = 20, offset = 0 } = query;

    const result = await this.databaseService.db
      .select()
      .from(chats)
      .where(and(eq(chats.creatorId, user_id), isNull(chats.deletedAt)))
      .limit(limit)
      .offset(offset);

    const total = await this.databaseService.db
      .select({ count: sql<number>`count(*)` })
      .from(chats)
      .where(and(eq(chats.creatorId, user_id), isNull(chats.deletedAt)));

    return {
      data: result as ChatDO[],
      total: total[0]?.count || 0,
    };
  }

  /**
   * List for chat assistant
   */
  async listForChatAssistant(
    user_id: string,
    param: {
      query: {
        limit?: number;
        offset?: number;
        order?: 'asc' | 'desc';
      };
      user_id: string;
      board_id?: string;
      origin?: ChatOrigin;
    },
    board_id?: string,
    origin?: ChatOrigin,
    query?: {
      limit?: number;
      offset?: number;
      order?: 'asc' | 'desc';
    },
  ): Promise<{
    data: ChatDO[];
    total: number;
  }> {
    const { limit = 20, offset = 0, order = 'desc' } = query || {};

    const clauses = [eq(chats.creatorId, user_id), isNull(chats.deletedAt)];

    if (board_id) {
      clauses.push(eq(chats.boardId, board_id));
    }

    if (origin) {
      clauses.push(eq(chats.originType, origin.type));
      if ('id' in origin && origin.id) {
        clauses.push(eq(chats.originId, origin.id));
      }
    }

    const result = await this.databaseService.db
      .select()
      .from(chats)
      .where(and(...clauses) as SQL)
      .orderBy(order === 'desc' ? desc(chats.updatedAt) : asc(chats.updatedAt))
      .limit(limit)
      .offset(offset);

    const total = await this.databaseService.db
      .select({ count: sql<number>`count(*)` })
      .from(chats)
      .where(and(...clauses) as SQL);

    return {
      data: result as ChatDO[],
      total: total[0]?.count || 0,
    };
  }
}

@Injectable()
export class MessageDAO extends BaseDAO<typeof messages._.config, MessageDO> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, messages);
  }

  /**
   * 根据ID查询消息
   */
  async selectById(id: string): Promise<MessageDO> {
    const result = await this.trySelectById(id);
    if (!result) {
      throw new NotFound({
        resource: ResourceEnum.MESSAGE,
        id,
      });
    }
    return result;
  }

  /**
   * 根据ID尝试查询消息
   */
  async trySelectById(id: string): Promise<MessageDO | null> {
    const result = await this.databaseService.db
      .select()
      .from(messages)
      .where(and(eq(messages.id, id), isNull(messages.deletedAt)))
      .limit(1);

    return result.length > 0 ? (result[0] as MessageDO) : null;
  }

  /**
   * 根据聊天ID查询消息列表
   */
  async selectByChatId(
    chat_id: string,
    options?: {
      roles?: MessageRoleEnum[];
      order?: 'asc' | 'desc';
      limit?: number;
    },
  ): Promise<MessageDO[]> {
    const result = await this.databaseService.db
      .select()
      .from(messages)
      .where(and(eq(messages.chatId, chat_id), isNull(messages.deletedAt)))
      .orderBy(options?.order === 'desc' ? desc(messages.createdAt) : asc(messages.createdAt));

    let filteredResult = result as MessageDO[];

    // Apply role filtering in memory if needed
    if (options?.roles && options.roles.length > 0) {
      filteredResult = filteredResult.filter((msg) =>
        options.roles!.includes(msg.role as MessageRoleEnum),
      );
    }

    // Apply limit if specified
    if (options?.limit) {
      filteredResult = filteredResult.slice(0, options.limit);
    }

    return filteredResult;
  }

  /**
   * 根据聊天ID删除消息
   */
  async deleteByChatId(chat_id: string): Promise<void> {
    const now = new Date();
    await this.databaseService.db
      .update(messages)
      .set({
        deletedAt: now,
        updated_at: now,
      } as any)
      .where(and(eq(messages.chatId, chat_id), isNull(messages.deletedAt)));
  }

  /**
   * 根据聊天ID查询有效消息数量
   */
  async countByChatId(chat_id: string): Promise<number> {
    const result = await this.databaseService.db
      .select()
      .from(messages)
      .where(and(eq(messages.chatId, chat_id), isNull(messages.deletedAt)));

    return result.length;
  }

  async updateContext(id: string, context: MessageContext[]) {
    return await this.updateOne(id, {
      context,
      updated_at: new Date(),
    });
  }

  async updateTraceId(message_id: string, trace_id: string) {
    return await this.updateOne(message_id, {
      trace_id,
      updated_at: new Date(),
    });
  }

  async updateStatus(message_id: string, status: MessageStatusEnum) {
    return await this.updateOne(message_id, {
      status,
      updated_at: new Date(),
    });
  }

  async deleteByRegenerate(param: {
    chat_id: string;
    user_message_id: string;
    regenerated_message_id: string;
  }) {
    // delete messages between user_message and regenerated_message
    // exclusive of these messages
    await this.updateByQuery(
      and(
        eq(messages.chatId, param.chat_id),
        gt(messages.id, param.user_message_id),
        lt(messages.id, param.regenerated_message_id),
      ) as SQL,
      {
        deletedAt: new Date(),
      },
    );
  }

  async deleteByChat(param: { chat_id: string }) {
    const clauses = [isNull(messages.deletedAt), eq(messages.chatId, param.chat_id)];

    return await this.updateByQuery(and(...clauses) as SQL, {
      deletedAt: new Date(),
    });
  }
}

@Injectable()
export class CompletionBlockDAO extends BaseDAO<
  typeof completionBlocks._.config,
  CompletionBlockDO
> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, completionBlocks);
  }

  protected transformDO(data: typeof completionBlocks.$inferSelect): CompletionBlockDO {
    return {
      ...data,
      type: data.type as CompletionBlockTypeEnum,
      extra: data.extra,
      status: data.status as CompletionBlockStatusEnum,
    };
  }

  async selectByMessageId(message_id: string) {
    return await this.selectManyByQuery(
      and(isNull(completionBlocks.deletedAt), eq(completionBlocks.messageId, message_id)),
      [asc(completionBlocks.createdAt)],
    );
  }

  async insertToolBlock(param: {
    message_id: string;
    tool_id: string;
    tool_name: string;
    tool_arguments: object;
  }) {
    return await this.insertOne({
      type: CompletionBlockTypeEnum.TOOL,
      status: CompletionBlockStatusEnum.ING,
      message_id: param.message_id,
      tool_id: param.tool_id,
      tool_name: param.tool_name,
      tool_arguments: param.tool_arguments,
      extra: {},
    } as typeof completionBlocks.$inferInsert);
  }

  async insertContentBlock(param: { message_id: string }) {
    return await this.insertOne({
      type: CompletionBlockTypeEnum.CONTENT,
      status: CompletionBlockStatusEnum.ING,
      message_id: param.message_id,
      data: '',
      extra: {},
    } as typeof completionBlocks.$inferInsert);
  }

  async insertReasoningBlock(param: { message_id: string }) {
    return await this.insertOne({
      type: CompletionBlockTypeEnum.REASONING,
      status: CompletionBlockStatusEnum.ING,
      message_id: param.message_id,
      data: '',
      extra: {},
    } as typeof completionBlocks.$inferInsert);
  }

  async updateBlocks(
    id: string,
    param: Partial<{
      tool_id: string;
      tool_name: string;
      tool_arguments: object;
      tool_result: object;
      tool_response: string;
      data: string;
      extra: object;
      status: CompletionBlockStatusEnum;
    }>,
  ) {
    return await this.updateOne(id, {
      data: param.data,
      status: param.status,
      tool_id: param.tool_id,
      tool_name: param.tool_name,
      tool_arguments: param.tool_arguments,
      tool_result: param.tool_result,
      tool_response: param.tool_response,
      extra: param.extra,
    });
  }

  async updateBlock(
    id: string,
    param: Partial<{
      tool_id: string;
      tool_name: string;
      tool_arguments: object;
      tool_result: object;
      tool_response: string;
      data: string;
      extra: object;
      status: CompletionBlockStatusEnum;
    }>,
  ) {
    return await this.updateOne(id, {
      data: param.data,
      status: param.status,
      tool_id: param.tool_id,
      tool_name: param.tool_name,
      tool_arguments: param.tool_arguments,
      tool_result: param.tool_result,
      tool_response: param.tool_response,
      extra: param.extra,
    });
  }
}
