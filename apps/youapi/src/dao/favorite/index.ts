/**
 * Favorite DAO - 收藏数据访问对象
 * 管理用户收藏项的CRUD操作
 *
 * Migrated from:
 * - youapp/src/lib/dao/favorite/index.ts
 */

import { Injectable } from '@nestjs/common';
import { and, eq, gt, isNull } from 'drizzle-orm';

import { NotFound, ResourceEnum } from '../../common/errors';
import type { FavoriteEntityTypeEnum } from '../../common/types';
import { DatabaseService } from '../db/database.service';
import { favorites } from '../db/public.schema';
import type { FavoriteDO, InsertFavoriteDOParam, UpdateFavoriteDOParam } from './types';

@Injectable()
export class FavoriteDAO {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  async insert(param: InsertFavoriteDOParam): Promise<FavoriteDO> {
    const result = await this.db.insert(favorites).values(param).returning();
    return result[0];
  }

  async update(id: string, param: UpdateFavoriteDOParam): Promise<FavoriteDO> {
    const result = await this.db
      .update(favorites)
      .set({
        ...param,
        updated_at: new Date(),
      } as any)
      .where(and(isNull(favorites.deleted_at), eq(favorites.id, id)))
      .returning();

    return result[0];
  }

  async deleteByEntity(param: {
    space_id: string;
    entity_type: FavoriteEntityTypeEnum;
    entity_id: string;
  }): Promise<void> {
    await this.db
      .update(favorites)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(
        and(
          isNull(favorites.deleted_at),
          eq(favorites.space_id, param.space_id),
          eq(favorites.entity_type, param.entity_type),
          eq(favorites.entity_id, param.entity_id),
        ),
      );
  }

  async delete(id: string): Promise<void> {
    await this.db
      .update(favorites)
      .set({
        deleted_at: new Date(),
      } as any)
      .where(and(isNull(favorites.deleted_at), eq(favorites.id, id)));
  }

  async trySelectById(id: string): Promise<FavoriteDO | null> {
    const result = await this.db.select().from(favorites).where(eq(favorites.id, id));
    if (result.length === 0) {
      return null;
    }
    return result[0];
  }

  async selectById(id: string): Promise<FavoriteDO> {
    const result = await this.trySelectById(id);
    if (!result) {
      throw new NotFound({
        resource: ResourceEnum.FAVORITE,
        id,
      });
    }
    return result;
  }

  async select(space_id: string): Promise<FavoriteDO[]> {
    return this.db
      .select()
      .from(favorites)
      .where(and(isNull(favorites.deleted_at), eq(favorites.space_id, space_id)))
      .orderBy(favorites.rank);
  }

  async trySelectNextRank(space_id: string, rank: string): Promise<FavoriteDO | null> {
    const result = await this.db
      .select()
      .from(favorites)
      .where(
        and(
          isNull(favorites.deleted_at),
          eq(favorites.space_id, space_id),
          gt(favorites.rank, rank),
        ),
      )
      .orderBy(favorites.rank)
      .limit(1);

    if (result.length === 0) {
      return null;
    }
    return result[0];
  }

  async trySelectFirst(space_id: string): Promise<FavoriteDO | null> {
    const result = await this.db
      .select()
      .from(favorites)
      .where(and(isNull(favorites.deleted_at), eq(favorites.space_id, space_id)))
      .orderBy(favorites.rank)
      .limit(1);

    if (result.length === 0) {
      return null;
    }
    return result[0];
  }

  async trySelectByEntity(param: {
    space_id: string;
    entity_type: FavoriteEntityTypeEnum;
    entity_id: string;
  }): Promise<FavoriteDO | null> {
    const result = await this.db
      .select()
      .from(favorites)
      .where(
        and(
          isNull(favorites.deleted_at),
          eq(favorites.space_id, param.space_id),
          eq(favorites.entity_type, param.entity_type),
          eq(favorites.entity_id, param.entity_id),
        ),
      )
      .limit(1);

    if (result.length === 0) {
      return null;
    }
    return result[0];
  }
}
