/**
 * Favorite DAO Types - 收藏数据类型
 * 定义收藏相关的数据对象类型
 *
 * Migrated from:
 * - youapp/src/lib/dao/favorite/types.ts
 */

import type { PgUpdateSetSource } from 'drizzle-orm/pg-core';
import type { favorites } from '../db/public.schema';

export type FavoriteDO = typeof favorites.$inferSelect;
export type InsertFavoriteDOParam = typeof favorites.$inferInsert;
export type UpdateFavoriteDOParam = Omit<
  PgUpdateSetSource<typeof favorites>,
  'id' | 'created_at' | 'deleted_at'
>;
