import { Injectable } from '@nestjs/common';
import { and, desc, eq, gte, inArray, isNull, like, lte, sql } from 'drizzle-orm';

import { SnipFromEnum } from '../../common/types';

import { BaseDAO } from '../db/base';
import { DatabaseService } from '../db/database.service';
import { board_items, snips } from '../db/public.schema';
import type { InsertSnipDOParam, SelectSnipDOsParam, SnipDO, UpdateSnipDOParam } from './types';

@Injectable()
export class SnipDAO extends BaseDAO<typeof snips._.config, SnipDO> {
  constructor(databaseService: DatabaseService) {
    super(databaseService, snips);
  }
  async trySelectById(id: string): Promise<SnipDO | null> {
    return this.selectOneByQuery(and(isNull(snips.deleted_at), eq(snips.id, id))!);
  }

  async selectById(id: string): Promise<SnipDO> {
    const result = await this.trySelectById(id);
    if (!result) {
      throw new Error(`Snip not found: ${id}`);
    }
    return result;
  }

  async select(param: SelectSnipDOsParam) {
    return this.db
      .select()
      .from(snips)
      .where(this.buildWhere(param))
      .orderBy(desc(snips.created_at));
  }

  async insert(insertParam: InsertSnipDOParam): Promise<SnipDO> {
    return this.insertOne(insertParam);
  }

  async update(id: string, updateParam: UpdateSnipDOParam): Promise<SnipDO> {
    return this.updateOne(id, updateParam);
  }

  async delete(id: string): Promise<void> {
    await this.updateByQuery(and(isNull(snips.deleted_at), eq(snips.id, id))!, {
      deleted_at: new Date(),
    });
  }

  /**
   * 根据规范化URL查询网页类型的Snips
   * 对应 youapp 的 selectWebpagesByNormalizedUrls 方法
   */
  async selectWebpagesByNormalizedUrls(
    spaceId: string,
    normalizedUrls: string[],
  ): Promise<SnipDO[]> {
    return this.db
      .select()
      .from(snips)
      .where(
        and(
          isNull(snips.deleted_at),
          eq(snips.from, SnipFromEnum.WEBPAGE),
          eq(snips.space_id, spaceId),
          inArray(snips.webpage_normalized_url, normalizedUrls),
        ),
      );
  }

  /**
   * 根据规范化URL查询特定Board中的网页类型Snips
   * 对应 youapp 的 selectWebpagesInBoardByNormalizedUrls 方法
   */
  async selectWebpagesInBoardByUrls(
    boardId: string,
    normalizedUrls: string[],
  ): Promise<Array<{ snips: SnipDO; board_items: any }>> {
    return this.db
      .select()
      .from(snips)
      .innerJoin(
        board_items,
        and(
          isNull(board_items.deleted_at),
          eq(board_items.board_id, boardId),
          eq(snips.id, board_items.snip_id),
        ),
      )
      .where(
        and(
          isNull(snips.deleted_at),
          eq(snips.from, SnipFromEnum.WEBPAGE),
          inArray(snips.webpage_normalized_url, normalizedUrls),
        ),
      )
      .orderBy(desc(snips.created_at));
  }

  private buildWhere(param: SelectSnipDOsParam) {
    const {
      spaceId,
      ids,
      normalizedUrl,
      type,
      from,
      fuzzyTitle,
      createdAfter,
      createdBefore,
      site,
    } = param;
    const conditions = [isNull(snips.deleted_at), eq(snips.space_id, spaceId)];

    if (ids) {
      conditions.push(inArray(snips.id, ids));
    }
    if (normalizedUrl) {
      conditions.push(eq(snips.webpage_normalized_url, normalizedUrl));
    }
    if (type) {
      conditions.push(eq(snips.type, type));
    }
    if (from) {
      conditions.push(eq(snips.from, from));
    }
    if (fuzzyTitle) {
      conditions.push(like(snips.title, `%${fuzzyTitle}%`));
    }
    if (createdAfter) {
      conditions.push(gte(snips.created_at, createdAfter));
    }
    if (createdBefore) {
      conditions.push(lte(snips.created_at, createdBefore));
    }
    if (site) {
      conditions.push(eq(snips.webpage_site_name, site));
    }
    return and(...conditions);
  }

  async selectTitlesByIds(ids: string[]) {
    return this.db
      .select({
        id: snips.id,
        title: snips.title,
      })
      .from(snips)
      .where(and(isNull(snips.deleted_at), inArray(snips.id, ids)));
  }

  async trySelectWebpageByNormalizedUrl(
    spaceId: string,
    normalizedUrl: string,
  ): Promise<SnipDO | null> {
    const results = await this.db
      .select()
      .from(snips)
      .where(
        and(
          isNull(snips.deleted_at),
          eq(snips.from, SnipFromEnum.WEBPAGE),
          eq(snips.space_id, spaceId),
          eq(snips.webpage_normalized_url, normalizedUrl),
        ),
      )
      .orderBy(desc(snips.created_at))
      .limit(1);

    return results.length > 0 ? results[0] : null;
  }

  async selectUnused(spaceId: string, limit?: number): Promise<SnipDO[]> {
    const query = this.db
      .select()
      .from(snips)
      .where(and(isNull(snips.deleted_at), eq(snips.space_id, spaceId)))
      .orderBy(desc(snips.created_at));

    if (limit) {
      query.limit(limit);
    }

    return query;
  }

  async selectInProgress(spaceId: string): Promise<SnipDO[]> {
    return this.db
      .select()
      .from(snips)
      .where(
        and(isNull(snips.deleted_at), eq(snips.space_id, spaceId), eq(snips.status, 'in_progress')),
      )
      .orderBy(desc(snips.created_at));
  }

  async selectByRelatedThoughtId(relatedThoughtId: string): Promise<SnipDO[]> {
    // This would need to join with a thought-snip relation table
    // For now, returning empty array as placeholder
    return [];
  }

  async selectByFilter(spaceId: string, filter: any): Promise<SnipDO[]> {
    // Implement filter logic based on the filter object
    // For now, returning basic query
    return this.db
      .select()
      .from(snips)
      .where(and(isNull(snips.deleted_at), eq(snips.space_id, spaceId)))
      .orderBy(desc(snips.created_at));
  }

  async selectForBoardHeroImages(boardId: string): Promise<SnipDO[]> {
    const results = await this.db
      .select()
      .from(snips)
      .innerJoin(
        board_items,
        and(
          isNull(board_items.deleted_at),
          eq(board_items.board_id, boardId),
          eq(snips.id, board_items.snip_id),
        ),
      )
      .where(and(isNull(snips.deleted_at), eq(snips.type, 'image')))
      .orderBy(desc(snips.created_at));

    return results.map((result) => result.snips);
  }

  async superDangerousSelectAllByUpdatedAtRange(
    timeStart: Date,
    timeEnd: Date,
    paging: { limit: number; offset: number },
    userId?: string,
  ): Promise<SnipDO[]> {
    const conditions = [gte(snips.updated_at, timeStart), lte(snips.updated_at, timeEnd)];

    if (userId) {
      conditions.push(eq(snips.creator_id, userId));
    }

    return this.db
      .select()
      .from(snips)
      .where(and(...conditions))
      .orderBy(desc(snips.updated_at))
      .limit(paging.limit)
      .offset(paging.offset);
  }

  async count(
    userId?: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<Array<{ count: number }>> {
    const conditions = [isNull(snips.deleted_at)];

    if (userId) {
      conditions.push(eq(snips.creator_id, userId));
    }
    if (startDate) {
      conditions.push(gte(snips.created_at, startDate));
    }
    if (endDate) {
      conditions.push(lte(snips.created_at, endDate));
    }

    const result = await this.db
      .select({ count: sql`count(*)` })
      .from(snips)
      .where(and(...conditions));

    return result as Array<{ count: number }>;
  }

  async getLatestTimestamp(userId?: string): Promise<Array<{ timestamp: string }>> {
    const conditions = [isNull(snips.deleted_at)];

    if (userId) {
      conditions.push(eq(snips.creator_id, userId));
    }

    const result = await this.db
      .select({ timestamp: sql`max(updated_at)` })
      .from(snips)
      .where(and(...conditions));

    return result as Array<{ timestamp: string }>;
  }

  async selectBriefById(id: string): Promise<SnipDO | null> {
    return this.trySelectById(id);
  }

  async selectBriefsByIds(ids: string[]): Promise<SnipDO[]> {
    return this.db
      .select()
      .from(snips)
      .where(and(isNull(snips.deleted_at), inArray(snips.id, ids)));
  }
}
