import type { snips } from '../db/public.schema';

export type SnipDO = typeof snips.$inferSelect;
export type InsertSnipDOParam = typeof snips.$inferInsert;
export type UpdateSnipDOParam = Partial<Omit<typeof snips.$inferSelect, 'id' | 'created_at'>>;

export interface SelectSnipDOsParam {
  spaceId: string;
  ids?: string[];
  normalizedUrl?: string;
  type?: string;
  from?: string;
  fuzzyTitle?: string;
  createdAfter?: Date;
  createdBefore?: Date;
  site?: string;
}
