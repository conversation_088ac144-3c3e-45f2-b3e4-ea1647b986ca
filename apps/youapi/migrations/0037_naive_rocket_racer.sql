CREATE TABLE IF NOT EXISTS "thoughts" (
	"id" uuid PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"deleted_at" timestamp with time zone,
	"space_id" uuid NOT NULL,
	"creator_id" uuid NOT NULL,
	"content_raw" text,
	"content_plain" text
);
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_thoughts_deleted_at_space_id" ON "thoughts" USING btree ("deleted_at","space_id");