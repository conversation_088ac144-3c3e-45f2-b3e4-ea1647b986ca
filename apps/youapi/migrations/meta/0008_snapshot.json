{"version": "7", "dialect": "postgresql", "tables": {"public.sources": {"name": "sources", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": true}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "modal": {"name": "modal", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "note_raw": {"name": "note_raw", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "note_plain": {"name": "note_plain", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "normalized_url": {"name": "normalized_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "authors": {"name": "authors", "type": "jsonb", "primaryKey": false, "notNull": false}, "transcript_type": {"name": "transcript_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "transcript_raw": {"name": "transcript_raw", "type": "text", "primaryKey": false, "notNull": false}, "transcript_plain": {"name": "transcript_plain", "type": "text", "primaryKey": false, "notNull": false}, "overview_raw": {"name": "overview_raw", "type": "text", "primaryKey": false, "notNull": false}, "overview_plain": {"name": "overview_plain", "type": "text", "primaryKey": false, "notNull": false}, "hero_image_url": {"name": "hero_image_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "site_name": {"name": "site_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "site_host": {"name": "site_host", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "site_favicon_url": {"name": "site_favicon_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "file_mime_type": {"name": "file_mime_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "file_path": {"name": "file_path", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "audio_url": {"name": "audio_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}}, "indexes": {"sources_space_id_type_normalized_url_idx": {"columns": [{"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "normalized_url", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "sources_space_id_type_normalized_url_idx", "isUnique": true, "method": "btree", "concurrently": false}, "sources_space_id_type_file_path_idx": {"columns": [{"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "file_path", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "sources_space_id_type_file_path_idx", "isUnique": true, "method": "btree", "concurrently": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.spaces": {"name": "spaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"spaces_creator_id_idx": {"columns": [{"expression": "creator_id", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "spaces_creator_id_idx", "isUnique": true, "method": "btree", "concurrently": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "id": "62619435-4bd2-41ab-b318-4e0b08a7512c", "prevId": "d8d4c22d-7d1a-44a3-9eb0-28ce6318a425", "sequences": {}}