{"version": "7", "dialect": "postgresql", "tables": {"public.cards": {"name": "cards", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": true}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "modal": {"name": "modal", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "note_source": {"name": "note_source", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "note_plain": {"name": "note_plain", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "authors": {"name": "authors", "type": "jsonb", "primaryKey": false, "notNull": false}, "transcript_type": {"name": "transcript_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "transcript_source": {"name": "transcript_source", "type": "text", "primaryKey": false, "notNull": false}, "transcript_plain": {"name": "transcript_plain", "type": "text", "primaryKey": false, "notNull": false}, "summary_source": {"name": "summary_source", "type": "text", "primaryKey": false, "notNull": false}, "summary_plain": {"name": "summary_plain", "type": "text", "primaryKey": false, "notNull": false}, "outline_source": {"name": "outline_source", "type": "text", "primaryKey": false, "notNull": false}, "outline_plain": {"name": "outline_plain", "type": "text", "primaryKey": false, "notNull": false}, "hero_image_url": {"name": "hero_image_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "site_name": {"name": "site_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "site_host": {"name": "site_host", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "site_favicon_url": {"name": "site_favicon_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "file_mime_type": {"name": "file_mime_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "resource_id": {"name": "resource_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.spaces": {"name": "spaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"spaces_creator_id_idx": {"columns": [{"expression": "creator_id", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "spaces_creator_id_idx", "isUnique": true, "method": "btree", "concurrently": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "id": "4c5f9300-24a0-4c24-b0ec-ca9ee3b20497", "prevId": "a260f506-10a5-4582-91cb-0930513eb161", "sequences": {}}