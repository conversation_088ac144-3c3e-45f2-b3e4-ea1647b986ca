{"id": "1d39ad92-144e-4124-9d9e-d382b00ce95a", "prevId": "32a9b4e7-2b34-4424-9261-d306913cb82d", "version": "7", "dialect": "postgresql", "tables": {"public.actions": {"name": "actions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "llm_prompt_name": {"name": "llm_prompt_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "result_block_type": {"name": "result_block_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "result_block_format": {"name": "result_block_format", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"uk_actions_name": {"name": "uk_actions_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"actions\".\"deleted_at\" is null", "concurrently": false, "method": "btree", "with": {}}, "idx_actions_space_id": {"name": "idx_actions_space_id", "columns": [{"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"actions_name_unique": {"name": "actions_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}}, "public.blocks": {"name": "blocks", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "snip_id": {"name": "snip_id", "type": "uuid", "primaryKey": false, "notNull": false}, "board_id": {"name": "board_id", "type": "uuid", "primaryKey": false, "notNull": false}, "action_id": {"name": "action_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "related_snip_id": {"name": "related_snip_id", "type": "uuid", "primaryKey": false, "notNull": false}, "display": {"name": "display", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "default": "'show'"}, "current_content_id": {"name": "current_content_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"idx_blocks_deleted_at": {"name": "idx_blocks_deleted_at", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_blocks_snip_id": {"name": "idx_blocks_snip_id", "columns": [{"expression": "snip_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.board_groups": {"name": "board_groups", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "board_id": {"name": "board_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "rank": {"name": "rank", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {"idx_board_groups_deleted_at_board_id_rank": {"name": "idx_board_groups_deleted_at_board_id_rank", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "board_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "rank", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.board_items": {"name": "board_items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "board_id": {"name": "board_id", "type": "uuid", "primaryKey": false, "notNull": true}, "board_group_id": {"name": "board_group_id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "'0192994d-3f50-7d95-bd37-eda7eed5ce54'"}, "snip_id": {"name": "snip_id", "type": "uuid", "primaryKey": false, "notNull": false}, "thought_id": {"name": "thought_id", "type": "uuid", "primaryKey": false, "notNull": false}, "rank": {"name": "rank", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'~'"}}, "indexes": {"idx_board_items_board_id_snip_id": {"name": "idx_board_items_board_id_snip_id", "columns": [{"expression": "board_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "snip_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"board_items\".\"deleted_at\" is null", "concurrently": false, "method": "btree", "with": {}}, "idx_board_items_board_id_thought_id": {"name": "idx_board_items_board_id_thought_id", "columns": [{"expression": "board_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "thought_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"board_items\".\"deleted_at\" is null", "concurrently": false, "method": "btree", "with": {}}, "idx_board_items_deleted_at_board_id_board_group_id_rank": {"name": "idx_board_items_deleted_at_board_id_board_group_id_rank", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "board_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "board_group_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "rank", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.boards": {"name": "boards", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": true}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": true}, "icon_name": {"name": "icon_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "icon_color": {"name": "icon_color", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "pinned_at": {"name": "pinned_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "filter": {"name": "filter", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "default": "'in-progress'"}, "hero_image_urls": {"name": "hero_image_urls", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"idx_boards_deleted_at_space_id": {"name": "idx_boards_deleted_at_space_id", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chats": {"name": "chats", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "origin_type": {"name": "origin_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "origin_id": {"name": "origin_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "origin_url": {"name": "origin_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "context": {"name": "context", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_origin_and_creator": {"name": "idx_origin_and_creator", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "creator_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "origin_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "origin_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "origin_url", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_creator": {"name": "idx_creator", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "creator_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.contents": {"name": "contents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "format": {"name": "format", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "raw": {"name": "raw", "type": "text", "primaryKey": false, "notNull": false}, "plain": {"name": "plain", "type": "text", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "block_id": {"name": "block_id", "type": "uuid", "primaryKey": false, "notNull": false}, "snip_id": {"name": "snip_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"idx_contents_deleted_at": {"name": "idx_contents_deleted_at", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_contents_block_id": {"name": "idx_contents_block_id", "columns": [{"expression": "block_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_contents_snip_id": {"name": "idx_contents_snip_id", "columns": [{"expression": "snip_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false, "default": "'queued'"}}, "indexes": {"idx_chat": {"name": "idx_chat", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.placements": {"name": "placements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "rank": {"name": "rank", "type": "double precision", "primaryKey": false, "notNull": true}, "height": {"name": "height", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "snip_id": {"name": "snip_id", "type": "uuid", "primaryKey": false, "notNull": false, "default": "null"}, "block_id": {"name": "block_id", "type": "uuid", "primaryKey": false, "notNull": false, "default": "null"}}, "indexes": {"idx_placements_rank": {"name": "idx_placements_rank", "columns": [{"expression": "rank", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_placements_block_id": {"name": "idx_placements_block_id", "columns": [{"expression": "block_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_placements_snip_block": {"name": "idx_placements_snip_block", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "snip_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "block_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.snip_thought_relations": {"name": "snip_thought_relations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "snip_id": {"name": "snip_id", "type": "uuid", "primaryKey": false, "notNull": true}, "thought_id": {"name": "thought_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"idx_snip_thought_relations_deleted_at_snip_id_thought_id": {"name": "idx_snip_thought_relations_deleted_at_snip_id_thought_id", "columns": [{"expression": "snip_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "thought_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"snip_thought_relations\".\"deleted_at\" is null", "concurrently": false, "method": "btree", "with": {}}, "idx_snip_thought_relations_deleted_at_thought_id_snip_id": {"name": "idx_snip_thought_relations_deleted_at_thought_id_snip_id", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "thought_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "snip_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.snips": {"name": "snips", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": true}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "from": {"name": "from", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "webpage_url": {"name": "webpage_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "webpage_normalized_url": {"name": "webpage_normalized_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "webpage_title": {"name": "webpage_title", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "webpage_description": {"name": "webpage_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "webpage_site_name": {"name": "webpage_site_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "webpage_site_host": {"name": "webpage_site_host", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "webpage_site_favicon_url": {"name": "webpage_site_favicon_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "file_mime_type": {"name": "file_mime_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "file_storage_url": {"name": "file_storage_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "file_original_url": {"name": "file_original_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "selection": {"name": "selection", "type": "text", "primaryKey": false, "notNull": false}, "annotation_raw": {"name": "annotation_raw", "type": "text", "primaryKey": false, "notNull": false}, "annotation_plain": {"name": "annotation_plain", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "authors": {"name": "authors", "type": "jsonb", "primaryKey": false, "notNull": false}, "hero_image_url": {"name": "hero_image_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "content_format": {"name": "content_format", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "content_raw": {"name": "content_raw", "type": "text", "primaryKey": false, "notNull": false}, "content_plain": {"name": "content_plain", "type": "text", "primaryKey": false, "notNull": false}, "content_language": {"name": "content_language", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "play_url": {"name": "play_url", "type": "<PERSON><PERSON><PERSON>(2048)", "primaryKey": false, "notNull": false}, "extra": {"name": "extra", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_snips_deleted_at_space_id": {"name": "idx_snips_deleted_at_space_id", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_snips_deleted_at_space_id_from": {"name": "idx_snips_deleted_at_space_id_from", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "from", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_snips_deleted_at_space_id_type": {"name": "idx_snips_deleted_at_space_id_type", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.spaces": {"name": "spaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "activated_at": {"name": "activated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "subscription_id": {"name": "subscription_id", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "subscription_status": {"name": "subscription_status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}}, "indexes": {"spaces_creator_id_idx": {"name": "spaces_creator_id_idx", "columns": [{"expression": "creator_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"spaces\".\"deleted_at\" is null", "concurrently": false, "method": "btree", "with": {}}, "idx_spaces_subscription_id": {"name": "idx_spaces_subscription_id", "columns": [{"expression": "subscription_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"spaces\".\"deleted_at\" is null", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.system_configs": {"name": "system_configs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "modifier_id": {"name": "modifier_id", "type": "uuid", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"system_configs_key_idx": {"name": "system_configs_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"system_configs\".\"deleted_at\" is null", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.thoughts": {"name": "thoughts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": true}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "content_raw": {"name": "content_raw", "type": "text", "primaryKey": false, "notNull": true}, "content_plain": {"name": "content_plain", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_thoughts_deleted_at_space_id": {"name": "idx_thoughts_deleted_at_space_id", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.usage_records": {"name": "usage_records", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "'019222d7-8450-7a24-ae08-c67e6a21b31f'"}, "resource": {"name": "resource", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "bigint", "primaryKey": false, "notNull": true}, "extra": {"name": "extra", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"usage_records_deleted_at_space_id_resource_idx": {"name": "usage_records_deleted_at_space_id_resource_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "resource", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_customer_relations": {"name": "user_customer_relations", "schema": "", "columns": {"created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_customer_relations_user_id_unique": {"name": "user_customer_relations_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}}, "public.user_preferences": {"name": "user_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "display_language": {"name": "display_language", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ai_response_language": {"name": "ai_response_language", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ai_2nd_response_language": {"name": "ai_2nd_response_language", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'system'"}, "enable_bilingual": {"name": "enable_bilingual", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "detected_language": {"name": "detected_language", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}