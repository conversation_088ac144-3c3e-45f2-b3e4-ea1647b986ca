{"version": "7", "dialect": "postgresql", "tables": {"public.spaces": {"name": "spaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "creator_id": {"name": "creator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"spaces_creator_id_idx": {"columns": [{"expression": "creator_id", "isExpression": false, "asc": true, "nulls": "last"}], "with": {}, "name": "spaces_creator_id_idx", "isUnique": true, "method": "btree", "concurrently": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "id": "a260f506-10a5-4582-91cb-0930513eb161", "prevId": "00000000-0000-0000-0000-000000000000", "sequences": {}}