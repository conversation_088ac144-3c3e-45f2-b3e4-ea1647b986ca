# 避免参数传错位置的最佳实践

## 问题描述
在函数或构造函数中使用多个位置参数时，容易出现参数顺序错误，导致数据被存储到错误的字段中。

## 解决方案

### 1. 使用对象参数（推荐）

**❌ 错误的方式：位置参数**
```typescript
class UpdateThoughtCommand {
  constructor(
    public readonly thoughtId: string,
    public readonly title?: string,
    public readonly titleType?: TitleType,
    public readonly content?: ThoughtContent,
  ) {}
}

// 容易出错：参数顺序可能搞错
const command = new UpdateThoughtCommand(
  dto.id,
  dto.title,
  dto.content, // ❌ 错误：这应该是 titleType
);
```

**✅ 正确的方式：对象参数**
```typescript
interface UpdateThoughtParams {
  thoughtId: string;
  title?: string;
  titleType?: TitleType;
  content?: ThoughtContent;
}

class UpdateThoughtCommand {
  constructor(params: UpdateThoughtParams) {
    this.thoughtId = params.thoughtId;
    this.title = params.title;
    this.titleType = params.titleType;
    this.content = params.content;
  }
}

// 明确且不易出错
const command = new UpdateThoughtCommand({
  thoughtId: dto.id,
  title: dto.title,
  titleType: dto.titleType,
  content: dto.content,
});
```

### 2. 使用 Builder 模式

```typescript
class UpdateThoughtCommandBuilder {
  private params: Partial<UpdateThoughtParams> = {};

  setThoughtId(thoughtId: string): this {
    this.params.thoughtId = thoughtId;
    return this;
  }

  setTitle(title: string): this {
    this.params.title = title;
    return this;
  }

  setTitleType(titleType: TitleType): this {
    this.params.titleType = titleType;
    return this;
  }

  setContent(content: ThoughtContent): this {
    this.params.content = content;
    return this;
  }

  build(): UpdateThoughtCommand {
    if (!this.params.thoughtId) {
      throw new Error('thoughtId is required');
    }
    return new UpdateThoughtCommand(this.params as UpdateThoughtParams);
  }
}

// 使用
const command = new UpdateThoughtCommandBuilder()
  .setThoughtId(dto.id)
  .setTitle(dto.title)
  .setTitleType(dto.titleType)
  .setContent(dto.content)
  .build();
```

### 3. 使用工厂方法

```typescript
class UpdateThoughtCommand {
  constructor(private params: UpdateThoughtParams) {}

  static fromDto(dto: PatchThoughtDto): UpdateThoughtCommand {
    return new UpdateThoughtCommand({
      thoughtId: dto.id,
      title: dto.title,
      titleType: dto.titleType,
      content: dto.content,
    });
  }

  get thoughtId(): string { return this.params.thoughtId; }
  get title(): string | undefined { return this.params.title; }
  get titleType(): TitleType | undefined { return this.params.titleType; }
  get content(): ThoughtContent | undefined { return this.params.content; }
}

// 使用
const command = UpdateThoughtCommand.fromDto(dto);
```

### 4. 使用 TypeScript 严格检查

在 `tsconfig.json` 中启用严格检查：

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "exactOptionalPropertyTypes": true
  }
}
```

### 5. 使用 ESLint 规则

```json
{
  "rules": {
    "@typescript-eslint/prefer-readonly-parameter-types": "error",
    "@typescript-eslint/parameter-properties": "error",
    "max-params": ["error", 3]
  }
}
```

### 6. 使用命名参数模式

```typescript
// 对于简单情况，使用对象解构
function updateThought({
  thoughtId,
  title,
  titleType,
  content
}: {
  thoughtId: string;
  title?: string;
  titleType?: TitleType;
  content?: ThoughtContent;
}) {
  // 实现
}

// 调用
updateThought({
  thoughtId: dto.id,
  title: dto.title,
  titleType: dto.titleType,
  content: dto.content,
});
```

## 在我们的项目中的应用

### 已重构的 Command

`UpdateThoughtCommand` 已经重构为使用对象参数，避免了参数位置错误。

### 建议重构的其他 Commands

1. `CreateThoughtCommand`
2. `DeleteThoughtCommand`
3. 其他 material-mng 模块中的 Commands

### 代码审查检查清单

- [ ] 构造函数参数超过 3 个时，使用对象参数
- [ ] 可选参数较多时，使用对象参数
- [ ] 参数类型相似时（如多个 string 参数），使用对象参数
- [ ] 为复杂对象提供工厂方法或 Builder
- [ ] 使用 TypeScript 类型检查防止错误

## 总结

使用对象参数是最简单有效的方法，它提供了：
- **类型安全**：TypeScript 会检查属性名
- **可读性**：参数意图明确
- **维护性**：添加新参数不会影响现有调用
- **重构安全**：IDE 可以安全地重命名属性

在我们的 CQRS 架构中，建议所有 Command 和 Query 都使用对象参数模式。