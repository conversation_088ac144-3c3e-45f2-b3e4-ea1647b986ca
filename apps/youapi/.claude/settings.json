{"permissions": {"allow": ["Bash(*)", "Bash(npx tsc:*)", "Bash(npm run:*)", "Bash(pnpm install:*)", "Bash(pnpm run:*)", "Bash(pnpm add:*)", "Bash(pnpm build:*)", "Bash(pnpm list:*)", "Bash(pnpm remove:*)", "Bash(pnpm ls:*)", "Bash(pnpm test:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "Bash(node:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "Bash(xargs:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(sed:*)", "Bash(sort:*)", "Bash(git:*)", "Bash(rg:*)", "Write(file:*)", "WebFetch(domain:*)", "mcp__ide__getDiagnostics"], "deny": ["<PERSON><PERSON>(git reset --hard)", "<PERSON><PERSON>(git commit)"]}}